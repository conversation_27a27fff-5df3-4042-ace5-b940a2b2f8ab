# Sub-Main-Agent Permission Management Guide

## 🔍 **Where to Find Permission Settings**

### Step 1: Access Sub-Main-Agent Management
1. **Login as a Main Agent** (not sub-main-agent)
2. **Look at the left sidebar** - you'll see a new menu item:
   ```
   📊 Dashboard
   👤 Profile  
   🏢 Branch List
   🤝 Agent
   👥 Sub Main Agent  ← NEW MENU ITEM
   🏦 Bank Account
   ```
3. **Click on "Sub Main Agent"** to access the management interface

### Step 2: Register a New Sub-Main-Agent (with Initial Permissions)
1. Click the **"Register New Sub-Main-Agent"** button
2. Fill in name and email
3. **Set Default Permissions** in the registration form:
   ```
   Dashboard: [View] or [Edit]
   Profile: [View] or [Edit]  
   Agents: [View] or [Edit]
   Reports: [View] or [Edit]
   Agreements: [View] or [Edit]
   Bank Account: [View] or [Edit]
   ```
4. Click each permission to toggle between **View** and **Edit**
5. Submit the registration

### Step 3: Manage Existing Sub-Main-Agent Permissions
1. In the **Sub-Main-Agent List**, find the user you want to manage
2. Click the **"Actions"** dropdown button for that user
3. Select **"Manage Permissions"** from the dropdown menu
4. **Permission Management Modal** will open with:
   - Individual cards for each resource
   - **View Only** and **View & Edit** buttons for each resource
   - Real-time permission updates

### Step 4: Permission Options Explained

| Resource | View Permission | Edit Permission |
|----------|----------------|-----------------|
| **Dashboard** | Can see dashboard | Can modify dashboard elements |
| **Profile** | Can view own profile | Can edit own profile |
| **Agents** | Can view agent list | Can register/manage agents |
| **Reports** | Can view reports | Can create/edit reports |
| **Agreements** | Can view agreements | Can manage agreements |
| **Bank Account** | Can view bank info | Can edit bank details |

## 🎯 **Quick Access Locations**

### Primary Location: Sidebar Menu
```
Main Agent Dashboard → Sub Main Agent (sidebar)
URL: /main-agent/sub-main-agent
```

### Permission Management Actions:
1. **During Registration**: Set default permissions in the registration modal
2. **After Registration**: Use "Actions" → "Manage Permissions" for any user
3. **Bulk Updates**: Use the main interface to activate/deactivate users

## 🔧 **Permission Management Interface Features**

### Registration Modal
- ✅ Name and email input
- ✅ **Default Permissions Section** with toggle buttons
- ✅ Real-time permission preview
- ✅ Copy registration link after creation

### Management Table  
- ✅ Search and filter sub-main-agents
- ✅ **Permissions Summary** column showing edit/view counts
- ✅ **Actions dropdown** with permission management
- ✅ User status management (Active/Suspended)

### Permission Management Modal
- ✅ **Individual resource cards** for each permission category
- ✅ **Visual toggle buttons** (View Only vs View & Edit)
- ✅ **Real-time updates** - changes apply immediately
- ✅ **User-specific settings** - each sub-main-agent has unique permissions

## 🚨 **Important Notes**

### Who Can Manage Permissions?
- ✅ **Main Agents**: Full access to register and manage sub-main-agents
- ❌ **Sub-Main-Agents**: Cannot see the "Sub Main Agent" menu item
- ❌ **Other roles**: No access to this feature

### Permission Effects
- ⚡ **Immediate**: Permission changes take effect immediately
- 🔒 **Restrictive**: Sub-main-agents see 403 errors when accessing restricted areas
- 🎯 **Granular**: Each resource can be set independently

### Security Features
- 🔐 Only main agents who registered a sub-main-agent can manage their permissions
- 🎛️ Permissions are checked on every page load
- 📊 Permission usage is tracked in the database

## 📱 **Visual Reference**

### Sidebar Navigation
```
├── 📊 Dashboard
├── 👤 Profile
├── 🏢 Branch List  
├── 🤝 Agent
├── 👥 Sub Main Agent ← CLICK HERE
└── 🏦 Bank Account
```

### Permission Management Flow
```
Sub Main Agent Page → Register New OR Actions Dropdown → Manage Permissions
     ↓                           ↓                              ↓
   User List              Actions Menu                 Permission Modal
   ┌─────────┐           ┌──────────────┐             ┌─────────────────┐
   │ Search  │           │ View Details │             │ Dashboard: Edit │
   │ Filter  │           │ Manage Perms │ ← CLICK     │ Profile: View   │
   │ Users   │           │ Activate     │             │ Agents: Edit    │
   └─────────┘           └──────────────┘             │ Reports: View   │
                                                      │ Agreements: Edit│
                                                      │ Bank Acc: View  │
                                                      └─────────────────┘
```

## 🎉 **You're Ready!**

The permission system is now integrated into your sidebar and ready to use. Main agents can immediately start registering sub-main-agents with customized permissions through the new "Sub Main Agent" menu item. 
