# Master Agent Profile Management

## Overview
The Master Agent Profile page provides comprehensive user management functionality for Master Agents to view and update their personal information, bank details, and account settings.

## Features

### 1. Organization Information (Read-only)
- **Cooperative Name**: Displays the cooperative the user belongs to
- **Branch Name**: Shows the specific branch assignment
- **Account Status**: Current account status (Active, Pending, Suspended, etc.)
- **KYC Status**: Know Your Customer verification status with color-coded badges

### 2. Account Information
- **Email Address**: Read-only email display
- **KYC Status Badge**: Visual indicator of verification status

### 3. Personal Information (Editable)
- **Full Name**: User's complete name
- **Phone Number**: Contact phone number
- **Address**: Two-line address system
- **Location Details**: Postcode, City, and State (Malaysian states)
- **Registration Number**: Optional registration identifier



## File Structure

### Backend Components
- **Controller**: `app/Livewire/MasterAgent/Profile.php`
- **View**: `resources/views/livewire/master-agent/profile.blade.php`
- **Route**: `routes/master-agent.php` (Route name: `master_agent.profile.form`)

### Related Models
- `app/Models/User.php` - Main user model
- `app/Models/AcsUsersDetail.php` - User details and KYC information

## Functionality

### Profile Updates
- Real-time validation with error messages
- Transaction-based updates for data integrity
- Automatic status management for KYC process

## Validation Rules

### Required Fields
- Full Name (max 255 characters)
- Phone Number (max 20 characters)
- Address Line 1 (max 255 characters)
- Postcode (max 10 characters)
- City (max 100 characters)
- State (must be valid Malaysian state)

### Optional Fields
- Address Line 2 (max 255 characters)
- Registration Number (max 100 characters)

## Security Features

### Data Protection
- Transaction-based updates
- Input validation and sanitization
- CSRF protection through Livewire
- Authenticated access only

## Usage Instructions

### Accessing the Profile Page
1. Navigate to the Master Agent dashboard
2. Click on "Profile" in the navigation menu
3. Or access directly via route: `/master-agent/profile`

### Updating Profile Information
1. Fill in the required fields marked with red asterisks (*)
2. Select appropriate options from dropdowns
3. Click "Save Profile" to submit changes
4. Review success/error messages

## Error Handling

### Validation Errors
- Real-time field validation
- Clear error messages below each field
- Form submission prevention on validation failure

### Database Errors
- Transaction rollback on errors
- User-friendly error messages
- Logging for debugging purposes



## Status Management

### KYC Status Flow
1. **Not Submitted**: No user details exist
2. **Pending**: Details submitted, awaiting approval
3. **Approved**: KYC verified and approved
4. **Rejected**: KYC rejected, requires resubmission

### Account Status
- **Active**: Fully functional account
- **Pending**: Awaiting activation
- **Suspended**: Temporarily disabled
- **Inactive**: Deactivated account

## Technical Notes

### Database Relationships
- User → UserDetails (One-to-One)

### Performance Considerations
- Eager loading of relationships
- Efficient database queries
- Minimal page reloads with Livewire

## Future Enhancements

### Potential Features
- Profile picture upload
- Two-factor authentication
- Activity log tracking
- Email notifications for changes
- Bulk data export functionality
- Advanced search and filtering

### Security Improvements
- Rate limiting for password changes
- Audit trail for profile modifications
- Enhanced file validation
- Backup and recovery options 
