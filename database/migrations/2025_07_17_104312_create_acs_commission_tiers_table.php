<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_commission_tiers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('commission_setting_id')->constrained('acs_comission_settings')->onDelete('cascade');
            $table->integer('min_qty');
            $table->integer('max_qty');
            $table->decimal('commission_percentage', 10, 2);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
            
            // Add indexes for better performance
            $table->index(['commission_setting_id', 'sort_order']);
            $table->index(['min_qty', 'max_qty']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_commission_tiers');
    }
};
