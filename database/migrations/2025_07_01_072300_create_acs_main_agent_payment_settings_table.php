<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_main_agent_payment_settings', function (Blueprint $table) {
            $table->id();
            $table->ulid('main_agent_id')->index();
            $table->enum('payment_method', ['individual_branch', 'main_cooperative'])->default('main_cooperative');
            $table->boolean('allow_branch_override')->default(false);
            $table->json('branch_specific_settings')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->ulid('created_by')->nullable();
            $table->ulid('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('main_agent_id')->references('id')->on('acs_users')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('acs_users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('acs_users')->onDelete('set null');

            // Add index for performance
            $table->index(['main_agent_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_main_agent_payment_settings');
    }
};
