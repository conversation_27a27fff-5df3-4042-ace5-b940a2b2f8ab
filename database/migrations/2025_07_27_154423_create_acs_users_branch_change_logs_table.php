<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_users_branch_change_logs', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('acs_users_id'); // ULID reference to acs_users
            $table->unsignedBigInteger('from_branch_id')->nullable(); // Previous branch ID
            $table->unsignedBigInteger('to_branch_id'); // New branch ID
            $table->string('from_branch_name')->nullable(); // Previous branch name (for history)
            $table->string('to_branch_name'); // New branch name (for history)
            $table->unsignedBigInteger('from_cooperative_id')->nullable(); // Previous cooperative ID
            $table->unsignedBigInteger('to_cooperative_id'); // New cooperative ID
            $table->string('from_cooperative_name')->nullable(); // Previous cooperative name (for history)
            $table->string('to_cooperative_name'); // New cooperative name (for history)
            $table->text('reason')->nullable(); // Reason for the change
            $table->string('action_done_by'); // ULID reference to user who made the change
            $table->timestamps();

            // Indexes for better performance
            $table->index(['acs_users_id', 'created_at']);
            $table->index(['action_done_by', 'created_at']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_users_branch_change_logs');
    }
};
