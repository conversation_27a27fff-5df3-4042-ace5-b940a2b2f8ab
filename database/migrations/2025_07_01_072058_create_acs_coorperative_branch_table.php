<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_coorperative_branch', function (Blueprint $table) {
            $table->id();
            $table->foreignId('acs_coorperative_id')->constrained('acs_coorperative')->onDelete('cascade');
            $table->string('name');
            $table->string('organization_name')->nullable();
            $table->string('business_registration_no')->nullable();
            $table->string('business_registration_document')->nullable();
            $table->unsignedBigInteger('acs_bank_names_id')->nullable();
            $table->string('account_name')->nullable();
            $table->string('account_number')->nullable();
            $table->enum('status', ['pending', 'active', 'inactive'])->default('pending');
            $table->ulid('created_by')->nullable();
            $table->ulid('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->uuid('registration_token')->nullable();
            $table->boolean('registration_enabled')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_coorperative_branch');
    }
};
