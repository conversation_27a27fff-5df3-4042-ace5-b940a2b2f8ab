<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_users', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('name');
            $table->foreignId('acs_role_id')->constrained('acs_roles')->onDelete('cascade');
            $table->string('username');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('password')->nullable();
            $table->enum('status', ['pending', 'active', 'suspended', 'inactive', 'in_progress', 'invitation_sent'])->default('pending');
            $table->timestamp('activated_at')->nullable();
            $table->timestamp('activation_email_sent_at')->nullable();
            $table->foreignId('acs_coorperative_id')->nullable()->constrained('acs_coorperative');
            $table->foreignId('acs_coorperative_branch_id')->nullable()->constrained('acs_coorperative_branch');
            $table->string('invited_by', 26)->nullable();
            $table->foreign('invited_by')->references('id')->on('acs_users')->onDelete('set null');
            $table->string('referral_code', 20)->nullable()->unique();
            $table->index('referral_code');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_users');
    }
};
