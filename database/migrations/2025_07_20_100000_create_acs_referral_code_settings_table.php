<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_referral_code_settings', function (Blueprint $table) {
            $table->id();
            $table->string('method_name')->unique(); // e.g., 'random', 'pattern_based', 'user_input'
            $table->json('settings')->nullable(); // JSON configuration for the method
            $table->boolean('is_active')->default(false); // Only one method can be active at a time
            $table->integer('code_length')->default(5); // Length of the referral code
            $table->string('prefix')->nullable(); // Optional prefix for codes
            $table->string('suffix')->nullable(); // Optional suffix for codes
            $table->boolean('include_numbers')->default(true);
            $table->boolean('include_uppercase')->default(true);
            $table->boolean('include_lowercase')->default(false);
            $table->boolean('exclude_similar_chars')->default(true); // Exclude O, 0, I, 1, etc.
            $table->foreignUlid('created_by')->constrained('acs_users');
            $table->foreignUlid('updated_by')->nullable()->constrained('acs_users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_referral_code_settings');
    }
};
