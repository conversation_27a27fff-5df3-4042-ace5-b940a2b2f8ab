<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_sales_commission', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->integer('table_22_jualan_id');
            $table->integer('table_23_senarai_jualan_id');
            $table->string('invoice_no');
            // Campaign reference removed as campaign functionality is not included
            $table->string('affiliate_membership_no');
            $table->integer('senarai_pelanggan_id');
            $table->decimal('comission_percentage', 10, 2);
            $table->decimal('comission_amount', 10, 2);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_sales_commission');
    }
};
