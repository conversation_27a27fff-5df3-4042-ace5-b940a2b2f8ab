<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('type')->unique(); // e.g., 'agent_activation'
            $table->string('subject');
            $table->text('body');
            $table->text('available_placeholders')->nullable(); // JSON array of available placeholders
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_email_templates');
    }
};
