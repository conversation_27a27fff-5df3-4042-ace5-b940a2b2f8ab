<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
        public function up(): void
    {
        Schema::create('acs_agreement_emails', function (Blueprint $table) {
            $table->id();
            $table->foreignId('terms_conditions_id')->constrained('acs_terms_conditions')->onDelete('cascade');
            $table->string('email');
            $table->string('main_agent_name')->nullable();
            $table->foreignUlid('main_agent_id')->nullable()->constrained('acs_users')->onDelete('cascade');
            $table->enum('status', ['pending', 'sent', 'failed'])->default('pending');
            $table->timestamp('sent_at')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamps();

            $table->index(['terms_conditions_id', 'status']);
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_agreement_emails');
    }
};
