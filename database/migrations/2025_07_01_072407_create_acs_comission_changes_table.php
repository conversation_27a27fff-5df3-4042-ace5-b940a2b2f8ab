<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_comission_changes', function (Blueprint $table) {
            $table->id();
            $table->integer('acs_comission_setting_id');
            $table->string('variety_type')->nullable();
            $table->string('variety_name')->nullable();
            $table->string('product_category_code')->nullable();
            $table->string('product_category_name')->nullable();
            $table->integer('min_qty')->nullable();
            $table->integer('max_qty')->nullable();
            $table->decimal('comission_percentage', 10, 2)->nullable();
            $table->json('proposed_tiers')->nullable();
            $table->string('change_type', 50)->default('single_tier');
            $table->integer('status')->default(0);
            $table->foreignUlid('action_by')->constrained('acs_users');
            $table->text('rejected_reason')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_comission_changes');
    }
};
