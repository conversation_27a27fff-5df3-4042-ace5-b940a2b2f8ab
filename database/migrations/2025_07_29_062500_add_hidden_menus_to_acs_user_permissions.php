<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_user_permissions', function (Blueprint $table) {
            if (!Schema::hasColumn('acs_user_permissions', 'hidden_menus')) {
                $table->json('hidden_menus')->nullable()->after('permissions');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_user_permissions', function (Blueprint $table) {
            if (Schema::hasColumn('acs_user_permissions', 'hidden_menus')) {
                $table->dropColumn('hidden_menus');
            }
        });
    }
};
