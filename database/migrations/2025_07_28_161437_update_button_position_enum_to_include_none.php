<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the enum to include 'none' option
        DB::statement("ALTER TABLE acs_email_templates MODIFY COLUMN button_position ENUM('top', 'middle', 'bottom', 'none') NOT NULL DEFAULT 'middle'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE acs_email_templates MODIFY COLUMN button_position ENUM('top', 'middle', 'bottom') NOT NULL DEFAULT 'middle'");
    }
};
