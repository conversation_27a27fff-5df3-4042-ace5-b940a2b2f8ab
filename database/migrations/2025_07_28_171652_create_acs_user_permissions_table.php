<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_user_permissions', function (Blueprint $table) {
            $table->id();
            $table->ulid('main_agent_id'); // The main agent who set the permissions
            $table->ulid('sub_main_agent_id'); // The sub-main-agent who receives the permissions
            $table->json('permissions'); // Store permissions as JSON (e.g., {"profile": ["view", "edit"], "reports": ["view"]})
            $table->json('hidden_menus')->nullable(); // Store hidden menu items as JSON array (e.g., ["reports", "settings"])
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('main_agent_id')->references('id')->on('acs_users')->onDelete('cascade');
            $table->foreign('sub_main_agent_id')->references('id')->on('acs_users')->onDelete('cascade');

            // Ensure one record per sub-main-agent
            $table->unique('sub_main_agent_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_user_permissions');
    }
};
