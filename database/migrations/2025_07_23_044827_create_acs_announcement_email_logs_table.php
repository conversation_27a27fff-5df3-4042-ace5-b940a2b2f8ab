<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_announcement_email_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('acs_announcement_id')->constrained('acs_announcement')->onDelete('cascade');
            $table->string('user_id'); // ULID string, not foreign key constraint
            $table->string('user_email');
            $table->string('user_name');
            $table->string('user_role');
            $table->enum('status', ['sent', 'failed'])->default('sent');
            $table->text('error_message')->nullable();
            $table->timestamp('sent_at');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['acs_announcement_id', 'status']);
            $table->index(['user_id', 'sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_announcement_email_logs');
    }
};
