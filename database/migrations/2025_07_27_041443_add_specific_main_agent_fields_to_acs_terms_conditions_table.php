<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_terms_conditions', function (Blueprint $table) {
            if (!Schema::hasColumn('acs_terms_conditions', 'main_agent_type')) {
                $table->enum('main_agent_type', ['all', 'specific'])->default('all')->after('acs_role_id');
            }
            if (!Schema::hasColumn('acs_terms_conditions', 'main_agent_email')) {
                $table->string('main_agent_email')->nullable()->after('main_agent_type');
            }
            if (!Schema::hasColumn('acs_terms_conditions', 'main_agent_id')) {
                $table->foreignUlid('main_agent_id')->nullable()->constrained('acs_users')->onDelete('cascade')->after('main_agent_email');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_terms_conditions', function (Blueprint $table) {
            $table->dropForeign(['main_agent_id']);
            $table->dropColumn(['main_agent_type', 'main_agent_email', 'main_agent_id']);
        });
    }
};
