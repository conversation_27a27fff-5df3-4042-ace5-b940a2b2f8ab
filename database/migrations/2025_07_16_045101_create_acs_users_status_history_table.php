<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_users_status_history', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('acs_users_id');
            $table->enum('from_status', ['pending', 'approved', 'rejected', 'in_progress'])->nullable();
            $table->enum('to_status', ['pending', 'approved', 'rejected', 'in_progress']);
            $table->text('reason')->nullable();
            $table->string('action_done_by'); // User ID who performed the action
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('acs_users_id')->references('id')->on('acs_users')->onDelete('cascade');
            $table->foreign('action_done_by')->references('id')->on('acs_users')->onDelete('cascade');

            // Indexes
            $table->index('acs_users_id');
            $table->index('action_done_by');
            $table->index('to_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_users_status_history');
    }
};
