<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_comission_settings', function (Blueprint $table) {
            $table->string('product_category_name')->after('product_category_code');
            $table->string('variety_name')->after('product_category_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_comission_settings', function (Blueprint $table) {
            $table->dropColumn([
                'product_category_name',
                'variety_name'
            ]);
        });
    }
};
