<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modify the enum column to include 'invitation_sent'
        DB::statement("ALTER TABLE acs_users MODIFY status ENUM('pending', 'active', 'suspended', 'inactive', 'in_progress', 'invitation_sent') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to the original enum values
        DB::statement("ALTER TABLE acs_users MODIFY status ENUM('pending', 'active', 'suspended', 'inactive', 'in_progress') DEFAULT 'pending'");
    }
};
