<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->foreign('acs_bank_detail_id')->references('id')->on('acs_bank_details')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->dropForeign(['acs_bank_detail_id']);
        });
    }
};
