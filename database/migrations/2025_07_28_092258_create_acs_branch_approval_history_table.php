<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_branch_approval_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('acs_coorperative_branch_id')->constrained('acs_coorperative_branch')->onDelete('cascade');
            $table->enum('action', ['approve', 'decline'])->comment('The action taken (approve or decline)');
            $table->text('reason')->nullable()->comment('Reason for approval or rejection');
            $table->foreignUlid('action_by')->constrained('acs_users')->onDelete('cascade')->comment('User who performed the action');
            $table->enum('previous_status', ['pending', 'active', 'inactive'])->comment('Status before the action');
            $table->enum('new_status', ['pending', 'active', 'inactive'])->comment('Status after the action');
            $table->json('additional_data')->nullable()->comment('Additional data like email sent status, etc.');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['acs_coorperative_branch_id', 'created_at'], 'branch_approval_history_branch_created_idx');
            $table->index(['action_by', 'created_at'], 'branch_approval_history_action_by_created_idx');
            $table->index(['action', 'created_at'], 'branch_approval_history_action_created_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_branch_approval_history');
    }
};
