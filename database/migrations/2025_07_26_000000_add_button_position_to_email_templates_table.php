<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_email_templates', function (Blueprint $table) {
            $table->enum('button_position', ['top', 'middle', 'bottom'])->default('middle')->after('button_text');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_email_templates', function (Blueprint $table) {
            $table->dropColumn('button_position');
        });
    }
};
