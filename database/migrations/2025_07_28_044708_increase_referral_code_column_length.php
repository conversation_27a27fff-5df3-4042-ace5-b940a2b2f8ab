<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_users', function (Blueprint $table) {
            // Drop the existing unique constraint first
            $table->dropUnique(['referral_code']);
        });

        Schema::table('acs_users', function (Blueprint $table) {
            // Change the column length
            $table->string('referral_code', 50)->nullable()->change();
        });

        Schema::table('acs_users', function (Blueprint $table) {
            // Add the unique constraint back
            $table->unique('referral_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_users', function (Blueprint $table) {
            // Drop the unique constraint first
            $table->dropUnique(['referral_code']);
        });

        Schema::table('acs_users', function (Blueprint $table) {
            // Change the column length back
            $table->string('referral_code', 20)->nullable()->change();
        });

        Schema::table('acs_users', function (Blueprint $table) {
            // Add the unique constraint back
            $table->unique('referral_code');
        });
    }
};
