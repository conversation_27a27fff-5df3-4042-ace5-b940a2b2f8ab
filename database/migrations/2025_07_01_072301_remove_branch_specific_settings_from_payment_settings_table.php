<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_main_agent_payment_settings', function (Blueprint $table) {
            $table->dropColumn(['allow_branch_override', 'branch_specific_settings']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_main_agent_payment_settings', function (Blueprint $table) {
            $table->boolean('allow_branch_override')->default(false);
            $table->json('branch_specific_settings')->nullable();
        });
    }
};
