<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_terms_conditions', function (Blueprint $table) {
            // Change content field to support file path for PDF uploads
            $table->string('content', 500)->nullable()->change();

            // Add file_path field for storing PDF file paths
            $table->string('file_path')->nullable()->after('content');

            // Add unique index to ensure only one active record per role
            // Status 1 = active, 0 = inactive
            $table->index(['acs_role_id', 'status'], 'idx_role_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_terms_conditions', function (Blueprint $table) {
            // Drop the unique index
            $table->dropIndex('idx_role_status');

            // Remove file_path field
            $table->dropColumn('file_path');

            // Revert content field back to original
            $table->string('content')->change();
        });
    }
};
