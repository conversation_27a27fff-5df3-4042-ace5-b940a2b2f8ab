<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_coorperative', function (Blueprint $table) {
            // Check if column exists before dropping
            if (Schema::hasColumn('acs_coorperative', 'outlet_name')) {
                $table->dropColumn('outlet_name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_coorperative', function (Blueprint $table) {
            // Check if column doesn't exist before adding
            if (!Schema::hasColumn('acs_coorperative', 'outlet_name')) {
                $table->string('outlet_name')->after('name');
            }
        });
    }
};
