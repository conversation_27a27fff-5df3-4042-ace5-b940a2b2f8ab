<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove bank fields from acs_users_details
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->dropColumn(['bank_account_number', 'bank_account_name', 'bank_statement']);
        });

        // Create new acs_user_bank_details table
        Schema::create('acs_user_bank_details', function (Blueprint $table) {
            $table->id();
            $table->foreignUlid('acs_users_id')->constrained('acs_users')->onDelete('cascade');
            $table->string('bank_account_number');
            $table->string('bank_account_name');
            $table->string('bank_statement')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add bank fields back to acs_users_details
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->string('bank_account_number')->nullable();
            $table->string('bank_account_name')->nullable();
            $table->string('bank_statement')->nullable();
        });

        // Drop acs_user_bank_details table
        Schema::dropIfExists('acs_user_bank_details');
    }
};
