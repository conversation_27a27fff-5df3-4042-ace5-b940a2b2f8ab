<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_announcement', function (Blueprint $table) {
            $table->boolean('email_sent')->default(false)->after('status');
            $table->timestamp('email_sent_at')->nullable()->after('email_sent');
            $table->json('email_sent_to_roles')->nullable()->after('email_sent_at');
            $table->integer('email_sent_count')->default(0)->after('email_sent_to_roles');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_announcement', function (Blueprint $table) {
            $table->dropColumn(['email_sent', 'email_sent_at', 'email_sent_to_roles', 'email_sent_count']);
        });
    }
};
