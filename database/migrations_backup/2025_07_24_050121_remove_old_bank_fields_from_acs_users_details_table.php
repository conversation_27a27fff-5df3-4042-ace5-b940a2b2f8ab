<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_users_details', function (Blueprint $table) {
            // Remove the old bank fields that are now handled by acs_bank_details table
            $table->dropColumn([
                'bank_account_number',
                'bank_account_name',
                'bank_statement'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_users_details', function (Blueprint $table) {
            // Add back the old bank fields
            $table->string('bank_account_number')->after('acs_range_income_id');
            $table->string('bank_account_name')->after('bank_account_number');
            $table->string('bank_statement')->after('bank_account_name');
        });
    }
};
