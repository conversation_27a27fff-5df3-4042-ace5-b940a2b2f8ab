<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_users', function (Blueprint $table) {
            $table->string('referral_code', 20)->nullable()->unique()->after('invited_by');
            $table->index('referral_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_users', function (Blueprint $table) {
            $table->dropIndex(['referral_code']);
            $table->dropColumn('referral_code');
        });
    }
};
