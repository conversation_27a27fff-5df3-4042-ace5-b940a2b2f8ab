<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_coorperative_branch', function (Blueprint $table) {
            $table->string('business_registration_no')->nullable()->after('name');
            $table->enum('status', ['pending', 'active', 'inactive'])->default('pending')->after('business_registration_no');
            $table->foreignUlid('created_by')->nullable()->after('status')->constrained('acs_users');
            $table->foreignUlid('verified_by')->nullable()->after('created_by')->constrained('acs_users');
            $table->timestamp('verified_at')->nullable()->after('verified_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_coorperative_branch', function (Blueprint $table) {
            $table->dropForeign(['verified_by']);
            $table->dropForeign(['created_by']);
            $table->dropColumn([
                'business_registration_no',
                'status',
                'created_by',
                'verified_by',
                'verified_at'
            ]);
        });
    }
};
