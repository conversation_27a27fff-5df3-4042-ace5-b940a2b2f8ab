<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_comission_settings', function (Blueprint $table) {
            // Remove tier-specific columns as they'll move to acs_commission_tiers table
            $table->dropColumn(['min_qty', 'max_qty', 'comission_percentage']);

            // Add audit fields if they don't exist
            if (!Schema::hasColumn('acs_comission_settings', 'created_by')) {
                $table->ulid('created_by')->nullable()->after('product_category_code');
            }
            if (!Schema::hasColumn('acs_comission_settings', 'updated_by')) {
                $table->ulid('updated_by')->nullable()->after('created_by');
            }

            // Add description field for better commission management
            $table->text('description')->nullable()->after('product_category_code');
            $table->text('design_code')->nullable()->after('description');
            $table->text('variety_code')->nullable()->after('design_code');

            // Add status field for enabling/disabling commission settings
            $table->enum('status', ['active', 'inactive'])->default('active')->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_comission_settings', function (Blueprint $table) {
            // Restore the original tier-specific columns
            $table->integer('min_qty')->after('product_category_code');
            $table->integer('max_qty')->after('min_qty');
            $table->decimal('comission_percentage', 10, 2)->after('max_qty');

            // Remove the new columns
            $table->dropColumn(['description', 'status', 'created_by', 'updated_by']);
        });
    }
};
