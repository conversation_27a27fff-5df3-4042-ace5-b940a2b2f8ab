<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_comission_changes', function (Blueprint $table) {
            // Add new columns for tiered structure
            $table->json('proposed_tiers')->nullable()->after('comission_percentage');
            $table->string('change_type', 50)->default('single_tier')->after('proposed_tiers');
            
            // Make existing columns nullable since they might not apply to tiered changes
            $table->integer('min_qty')->nullable()->change();
            $table->integer('max_qty')->nullable()->change();
            $table->decimal('comission_percentage', 10, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_comission_changes', function (Blueprint $table) {
            $table->dropColumn(['proposed_tiers', 'change_type']);
            
            // Revert columns to not nullable
            $table->integer('min_qty')->nullable(false)->change();
            $table->integer('max_qty')->nullable(false)->change();
            $table->decimal('comission_percentage', 10, 2)->nullable(false)->change();
        });
    }
};