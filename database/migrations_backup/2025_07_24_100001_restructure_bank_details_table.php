<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create new acs_bank_details table with updated structure
        Schema::create('acs_bank_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('acs_bank_names_id')->constrained('acs_bank_names')->onDelete('cascade');
            $table->string('account_number');
            $table->string('account_holder_name');
            $table->string('bank_statement')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Add acs_bank_detail_id to acs_users_details table
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->foreignId('acs_bank_detail_id')->nullable()->constrained('acs_bank_details')->onDelete('set null')->after('acs_range_income_id');
        });

        // Drop the old acs_user_bank_details table
        Schema::dropIfExists('acs_user_bank_details');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove acs_bank_detail_id from acs_users_details
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->dropForeign(['acs_bank_detail_id']);
            $table->dropColumn('acs_bank_detail_id');
        });

        // Drop acs_bank_details table
        Schema::dropIfExists('acs_bank_details');

        // Recreate the old acs_user_bank_details table
        Schema::create('acs_user_bank_details', function (Blueprint $table) {
            $table->id();
            $table->foreignUlid('acs_users_id')->constrained('acs_users')->onDelete('cascade');
            $table->string('bank_account_number');
            $table->string('bank_account_name');
            $table->string('bank_statement')->nullable();
            $table->timestamps();
        });
    }
};
