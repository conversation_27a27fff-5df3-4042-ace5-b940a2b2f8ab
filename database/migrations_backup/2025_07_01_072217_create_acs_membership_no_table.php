<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_membership_no', function (Blueprint $table) {
            $table->id();
            $table->foreignUlid('user_id')->constrained('acs_users');
            $table->string('membership_no');
            $table->string('verified_at');
            $table->foreignUlid('verified_by')->constrained('acs_users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_membership_no');
    }
};
