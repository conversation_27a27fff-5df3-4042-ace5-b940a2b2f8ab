<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_comission_settings', function (Blueprint $table) {
            $table->string('variety_name')->nullable()->after('variety_type');
            $table->string('product_category_name')->nullable()->after('variety_code');
            // Add unique constraint on variety_type and product_category_code combination
            $table->unique(['variety_type', 'product_category_code'], 'unique_variety_category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_comission_settings', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique('unique_variety_category');
        });
    }
};
