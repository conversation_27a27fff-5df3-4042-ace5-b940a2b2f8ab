<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->string('ic_front')->nullable()->after('no_ic');
            $table->string('ic_back')->nullable()->after('ic_front');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_users_details', function (Blueprint $table) {
            $table->dropColumn(['ic_front', 'ic_back']);
        });
    }
};
