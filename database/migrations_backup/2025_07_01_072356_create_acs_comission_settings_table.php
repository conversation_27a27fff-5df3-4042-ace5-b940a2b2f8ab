<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_comission_settings', function (Blueprint $table) {
            $table->id();
            //important notes
            //this attribute must be match 1=1 with gcomm intergration categories
            $table->string('variety_type')->nullable();
            $table->string('product_category_code')->nullable();
            //end here
            $table->integer('min_qty');
            $table->integer('max_qty');
            $table->decimal('comission_percentage', 10, 2);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_comission_settings');
    }
};
