<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_users_details', function (Blueprint $table) {
            $table->id();
            $table->foreignUlid('acs_users_id')->constrained('acs_users')->onDelete('cascade');
            $table->string('no_ic')->nullable();
            $table->string('address_1')->nullable();
            $table->string('address_2')->nullable();
            $table->string('postcode')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('working_position')->nullable();
            $table->foreignId('acs_range_income_id')->nullable()->constrained('acs_range_income');
            $table->string('bank_account_number');
            $table->string('bank_account_name');
            $table->string('bank_statement');
            $table->string('next_to_kin_name')->nullable();
            $table->string('next_to_kin_ic')->nullable();
            $table->string('next_to_kin_phone')->nullable();
            $table->string('kyc_approve_at');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_users_details');
    }
};
