<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_coorperative_branch', function (Blueprint $table) {
            if (!Schema::hasColumn('acs_coorperative_branch', 'registration_enabled')) {
                $table->boolean('registration_enabled')->default(true)->after('registration_token');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_coorperative_branch', function (Blueprint $table) {
            if (Schema::hasColumn('acs_coorperative_branch', 'registration_enabled')) {
                $table->dropColumn('registration_enabled');
            }
        });
    }
};
