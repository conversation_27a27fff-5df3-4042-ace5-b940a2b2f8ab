<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Recreate the changes table with updated structure
        Schema::create('acs_campaign_comission_changes', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('acs_campaign_com_id')->constrained('acs_campaign_commission')->onDelete('cascade');
            $table->foreignId('acs_comission_setting_id')->constrained('acs_comission_settings')->onDelete('cascade');
            $table->integer('status')->default(0);
            $table->text('reason')->nullable();
            $table->foreignUlid('action_by')->constrained('acs_users');
            $table->foreignUlid('aprove_by')->nullable()->constrained('acs_users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_campaign_commissions_changes');
    }
};
