<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_notifications', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('recipient_id'); // User who should receive this notification
            $table->string('sender_id')->nullable(); // User who triggered this notification
            $table->string('type'); // Type of notification (e.g., 'master_agent_details_completed')
            $table->string('title'); // Notification title
            $table->text('message'); // Notification message
            $table->json('data')->nullable(); // Additional data (user info, etc.)
            $table->enum('status', ['unread', 'read'])->default('unread');
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('recipient_id')->references('id')->on('acs_users')->onDelete('cascade');
            $table->foreign('sender_id')->references('id')->on('acs_users')->onDelete('set null');

            // Indexes for performance
            $table->index(['recipient_id', 'status']);
            $table->index('type');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_notifications');
    }
};
