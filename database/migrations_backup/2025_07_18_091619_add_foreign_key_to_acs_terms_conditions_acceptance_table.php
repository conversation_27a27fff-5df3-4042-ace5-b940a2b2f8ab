<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing records to have the latest terms conditions ID for their role
        $this->updateExistingRecords();

        // Add foreign key constraint
        Schema::table('acs_terms_conditions_acceptance', function (Blueprint $table) {
            $table->foreign('acs_terms_conditions_id')
                ->references('id')
                ->on('acs_terms_conditions')
                ->onDelete('cascade');
        });
    }

    /**
     * Update existing records with the latest terms conditions ID for their role
     */
    private function updateExistingRecords(): void
    {
        // Get all existing acceptance records that don't have a terms conditions ID
        $acceptances = DB::table('acs_terms_conditions_acceptance')
            ->whereNull('acs_terms_conditions_id')
            ->get();

        foreach ($acceptances as $acceptance) {
            // Get the latest terms and conditions for this role
            $latestTerms = DB::table('acs_terms_conditions')
                ->where('acs_role_id', $acceptance->acs_role_id)
                ->where('status', 'active')
                ->orderBy('created_at', 'desc')
                ->first();

            if ($latestTerms) {
                // Update the acceptance record with the terms conditions ID
                DB::table('acs_terms_conditions_acceptance')
                    ->where('id', $acceptance->id)
                    ->update(['acs_terms_conditions_id' => $latestTerms->id]);
            } else {
                // If no terms exist for this role, delete the orphaned acceptance record
                DB::table('acs_terms_conditions_acceptance')
                    ->where('id', $acceptance->id)
                    ->delete();
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_terms_conditions_acceptance', function (Blueprint $table) {
            $table->dropForeign(['acs_terms_conditions_id']);
        });
    }
};
