<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_commission_distributions', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('acs_sales_commission_id')->constrained('acs_sales_commission')->onDelete('cascade');
            $table->foreignId('acs_coorperative_id')->constrained('acs_coorperative');
            $table->foreignId('acs_coorperative_branch_id')->constrained('acs_coorperative_branch');
            $table->foreignUlid('acs_main_agent_user_id')->constrained('acs_users');
            $table->decimal('main_agent_percentage', 10, 2)->default(0);
            $table->decimal('main_agent_commission_amount', 10, 2)->default(0);
            $table->decimal('agent_percentage', 10, 2)->default(0);
            $table->decimal('agent_commission_amount', 10, 2)->default(0);
            $table->foreignUlid('acs_agent_user_id')->constrained('acs_users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_commission_distributions');
    }
};
