<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_users_details', function (Blueprint $table) {
            // Change kyc_approve_at from string to nullable datetime
            $table->datetime('kyc_approve_at')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_users_details', function (Blueprint $table) {
            // Revert back to string (but keep it nullable for safety)
            $table->string('kyc_approve_at')->nullable()->change();
        });
    }
};
