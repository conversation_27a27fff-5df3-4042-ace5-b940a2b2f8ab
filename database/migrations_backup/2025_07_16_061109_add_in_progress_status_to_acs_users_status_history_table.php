<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the enum values to include 'in_progress'
        DB::statement("ALTER TABLE acs_users_status_history MODIFY COLUMN from_status ENUM('pending', 'approved', 'rejected', 'in_progress') NULL");
        DB::statement("ALTER TABLE acs_users_status_history MODIFY COLUMN to_status ENUM('pending', 'approved', 'rejected', 'in_progress') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE acs_users_status_history MODIFY COLUMN from_status ENUM('pending', 'approved', 'rejected') NULL");
        DB::statement("ALTER TABLE acs_users_status_history MODIFY COLUMN to_status ENUM('pending', 'approved', 'rejected') NOT NULL");
    }
};
