<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_users', function (Blueprint $table) {
            // Add status field with enum values
            $table->enum('status', ['pending', 'active', 'suspended', 'inactive'])
                  ->default('pending')
                  ->after('password');

            // Add activated_at timestamp to track when account was activated
            $table->timestamp('activated_at')->nullable()->after('status');

            // Make phone and password nullable since they're set during activation
            $table->string('phone')->nullable()->change();
            $table->string('password')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_users', function (Blueprint $table) {
            // Remove the added fields
            $table->dropColumn(['status', 'activated_at']);

            // Revert phone and password to NOT NULL (though this might cause issues if there are nullable values)
            $table->string('phone')->nullable(false)->change();
            $table->string('password')->nullable(false)->change();
        });
    }
};
