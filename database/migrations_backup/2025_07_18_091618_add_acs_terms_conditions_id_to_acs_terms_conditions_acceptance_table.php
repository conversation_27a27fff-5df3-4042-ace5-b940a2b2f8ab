<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_terms_conditions_acceptance', function (Blueprint $table) {
            $table->foreignId('acs_terms_conditions_id')->nullable()->after('acs_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_terms_conditions_acceptance', function (Blueprint $table) {
            $table->dropColumn('acs_terms_conditions_id');
        });
    }
};
