<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('acs_campaign_commission', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('acs_campaign_id')->constrained('acs_campaign')->onDelete('cascade');
            $table->foreignId('acs_comission_setting_id')->constrained('acs_comission_settings')->onDelete('cascade');
            $table->foreignUlid('action_by')->constrained('acs_users');
            $table->timestamps();
            $table->softDeletes();
            $table->unique(['acs_campaign_id', 'acs_comission_setting_id'], 'unique_campaign_commission');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('acs_campaign_commission');

    }
};
