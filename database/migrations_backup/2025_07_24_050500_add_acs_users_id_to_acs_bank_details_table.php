<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the column already exists
        if (!Schema::hasColumn('acs_bank_details', 'acs_users_id')) {
            Schema::table('acs_bank_details', function (Blueprint $table) {
                // Add acs_users_id field as nullable first
                $table->foreignUlid('acs_users_id')->nullable()->constrained('acs_users')->onDelete('cascade')->after('id');
            });

            // Populate acs_users_id from the relationship through acs_users_details
            DB::statement("
                UPDATE acs_bank_details
                SET acs_users_id = (
                    SELECT acs_users_id
                    FROM acs_users_details
                    WHERE acs_users_details.acs_bank_detail_id = acs_bank_details.id
                )
            ");

            // Now make the column required
            Schema::table('acs_bank_details', function (Blueprint $table) {
                $table->foreignUlid('acs_users_id')->nullable(false)->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('acs_bank_details', 'acs_users_id')) {
            Schema::table('acs_bank_details', function (Blueprint $table) {
                $table->dropForeign(['acs_users_id']);
                $table->dropColumn('acs_users_id');
            });
        }
    }
};
