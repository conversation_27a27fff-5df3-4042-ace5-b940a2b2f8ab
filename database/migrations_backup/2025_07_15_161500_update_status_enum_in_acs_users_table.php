<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modify the ENUM to include 'invitation_sent'
        DB::statement("ALTER TABLE acs_users MODIFY COLUMN status ENUM('pending', 'active', 'suspended', 'inactive', 'invitation_sent') NOT NULL DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original ENUM values
        DB::statement("ALTER TABLE acs_users MODIFY COLUMN status ENUM('pending', 'active', 'suspended', 'inactive') NOT NULL DEFAULT 'pending'");
    }
};
