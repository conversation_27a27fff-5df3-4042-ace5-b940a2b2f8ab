<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AcsWorkingPositionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $positions = [
            // For main agents (senior positions)
            ['name' => 'Senior Sales Manager', 'description' => 'Senior level sales management position'],
            ['name' => 'Regional Director', 'description' => 'Regional leadership and oversight'],
            ['name' => 'Business Development Manager', 'description' => 'Business development and strategy'],
            ['name' => 'Area Manager', 'description' => 'Area management and coordination'],
            ['name' => 'General Manager', 'description' => 'General management responsibilities'],
            ['name' => 'Deputy General Manager', 'description' => 'Deputy general management role'],
            ['name' => 'Senior Executive', 'description' => 'Senior executive position'],
            ['name' => 'Branch Manager', 'description' => 'Branch management and operations'],
            ['name' => 'Operations Manager', 'description' => 'Operations management and oversight'],

            // For agents (field positions)
            ['name' => 'Sales Agent', 'description' => 'Field sales agent'],
            ['name' => 'Insurance Agent', 'description' => 'Insurance sales and advisory'],
            ['name' => 'Field Agent', 'description' => 'Field operations and sales'],
            ['name' => 'Marketing Executive', 'description' => 'Marketing and promotional activities'],
            ['name' => 'Customer Service Executive', 'description' => 'Customer service and support'],
            ['name' => 'Business Consultant', 'description' => 'Business advisory and consultation'],
            ['name' => 'Sales Representative', 'description' => 'Sales representation and client relations'],
            ['name' => 'Account Executive', 'description' => 'Account management and client relations'],
            ['name' => 'Financial Consultant', 'description' => 'Financial advisory services'],
            ['name' => 'Product Specialist', 'description' => 'Product expertise and sales'],
        ];

        foreach ($positions as $position) {
            DB::table('acs_working_positions')->updateOrInsert(
                ['name' => $position['name']],
                [
                    'name' => $position['name'],
                    'description' => $position['description'],
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]
            );
        }

        $this->command->info('Working positions seeded successfully!');
    }
}
