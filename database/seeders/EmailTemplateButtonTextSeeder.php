<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\EmailTemplate;

class EmailTemplateButtonTextSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update all email templates with default button text if not set
        $templates = EmailTemplate::whereNull('button_text')->get();

        foreach ($templates as $template) {
            // Set appropriate button text based on template type
            $buttonText = 'Create Password';

            if (strpos($template->type, 'activation') !== false) {
                $buttonText = 'Activate Account';
            } elseif (strpos($template->type, 'reset_password') !== false) {
                $buttonText = 'Reset Password';
            } elseif (strpos($template->type, 'verification') !== false) {
                $buttonText = 'Verify Email';
            }

            $template->button_text = $buttonText;
            $template->save();
        }
    }
}
