<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AcsBankName;

class AcsBankNamesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $banks = [
            ['bank_name' => 'Maybank', 'bank_code' => 'MBB'],
            ['bank_name' => 'CIMB Bank', 'bank_code' => 'CIMB'],
            ['bank_name' => 'Public Bank', 'bank_code' => 'PBB'],
            ['bank_name' => 'RHB Bank', 'bank_code' => 'RHB'],
            ['bank_name' => 'Hong Leong Bank', 'bank_code' => 'HLB'],
            ['bank_name' => 'AmBank', 'bank_code' => 'AMB'],
            ['bank_name' => 'UOB Bank', 'bank_code' => 'UOB'],
            ['bank_name' => 'Bank Islam Malaysia', 'bank_code' => 'BIMB'],
            ['bank_name' => 'Bank Rakyat', 'bank_code' => 'BKRM'],
            ['bank_name' => 'OCBC Bank', 'bank_code' => 'OCBC'],
            ['bank_name' => 'Standard Chartered Bank', 'bank_code' => 'SCB'],
            ['bank_name' => 'HSBC Bank Malaysia', 'bank_code' => 'HSBC'],
            ['bank_name' => 'Affin Bank', 'bank_code' => 'AFFIN'],
            ['bank_name' => 'Alliance Bank', 'bank_code' => 'ABM'],
            ['bank_name' => 'Bank Simpanan Nasional (BSN)', 'bank_code' => 'BSN'],
            ['bank_name' => 'Bank Muamalat Malaysia', 'bank_code' => 'BMM'],
            ['bank_name' => 'MBSB Bank', 'bank_code' => 'MBSB'],
            ['bank_name' => 'Citibank', 'bank_code' => 'CITI'],
            ['bank_name' => 'Bank Negara Malaysia', 'bank_code' => 'BNM'],
            ['bank_name' => 'Agrobank', 'bank_code' => 'AGRO'],
        ];

        foreach ($banks as $bank) {
            AcsBankName::updateOrCreate(
                ['bank_code' => $bank['bank_code']],
                $bank
            );
        }

        $this->command->info('Bank names seeded successfully!');
    }
}
