<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AcsUsersDetailsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get main-agent and agent users only
        $users = DB::table('acs_users')
            ->join('acs_roles', 'acs_users.acs_role_id', '=', 'acs_roles.id')
            ->whereIn('acs_roles.name', ['main-agent', 'agent'])
            ->whereNull('acs_users.deleted_at')
            ->whereNull('acs_roles.deleted_at')
            ->select('acs_users.*', 'acs_roles.name as role_name')
            ->get();

        if ($users->isEmpty()) {
            $this->command->error('No main-agent or agent users found. Please run user seeders first.');
            return;
        }

        // Get income ranges
        $incomeRanges = DB::table('acs_range_income')
            ->whereNull('deleted_at')
            ->get();

        if ($incomeRanges->isEmpty()) {
            $this->command->error('No income ranges found. Please run AcsRangeIncomeSeeder first.');
            return;
        }

        $malaysianStates = [
            'Selangor', 'Kuala Lumpur', 'Johor', 'Penang', 'Perak', 'Kedah',
            'Kelantan', 'Terengganu', 'Pahang', 'Negeri Sembilan', 'Melaka',
            'Sabah', 'Sarawak', 'Perlis', 'Putrajaya', 'Labuan'
        ];

        $workingPositions = [
            // For main agents (senior positions)
            'Senior Sales Manager', 'Regional Director', 'Business Development Manager',
            'Area Manager', 'General Manager', 'Deputy General Manager',
            'Senior Executive', 'Branch Manager', 'Operations Manager',

            // For agents (field positions)
            'Sales Agent', 'Insurance Agent', 'Field Agent', 'Marketing Executive',
            'Customer Service Executive', 'Business Consultant', 'Sales Representative',
            'Account Executive', 'Financial Consultant', 'Product Specialist'
        ];

        $cities = [
            'Kuala Lumpur' => ['Kuala Lumpur', 'Cheras', 'Ampang', 'Petaling Jaya', 'Subang Jaya'],
            'Selangor' => ['Shah Alam', 'Klang', 'Kajang', 'Puchong', 'Seri Kembangan'],
            'Johor' => ['Johor Bahru', 'Skudai', 'Batu Pahat', 'Muar', 'Kluang'],
            'Penang' => ['George Town', 'Bayan Lepas', 'Bukit Mertajam', 'Butterworth', 'Tanjung Tokong'],
            'Perak' => ['Ipoh', 'Taiping', 'Sitiawan', 'Kampar', 'Teluk Intan'],
            'Kedah' => ['Alor Setar', 'Sungai Petani', 'Kulim', 'Langkawi', 'Baling'],
            'Kelantan' => ['Kota Bharu', 'Kuala Krai', 'Tanah Merah', 'Machang', 'Gua Musang'],
            'Terengganu' => ['Kuala Terengganu', 'Kemaman', 'Dungun', 'Marang', 'Besut'],
            'Pahang' => ['Kuantan', 'Temerloh', 'Bentong', 'Raub', 'Jerantut'],
            'Negeri Sembilan' => ['Seremban', 'Port Dickson', 'Nilai', 'Bahau', 'Tampin'],
            'Melaka' => ['Melaka City', 'Batu Berendam', 'Ayer Keroh', 'Jasin', 'Merlimau'],
            'Sabah' => ['Kota Kinabalu', 'Sandakan', 'Tawau', 'Lahad Datu', 'Keningau'],
            'Sarawak' => ['Kuching', 'Miri', 'Sibu', 'Bintulu', 'Kapit']
        ];

        $bankNames = DB::table('acs_bank_names')->get();

        $userDetails = [];

        foreach ($users as $user) {
            // Skip if user details already exist
            $existing = DB::table('acs_users_details')
                ->where('acs_users_id', $user->id)
                ->whereNull('deleted_at')
                ->first();

            if ($existing) {
                $this->command->info("User details already exist for: {$user->name}");
                continue;
            }

            // Generate realistic IC number (Malaysian format: YYMMDD-PB-XXXX)
            $birthYear = rand(70, 95); // Born between 1970-1995
            $birthMonth = str_pad(rand(1, 12), 2, '0', STR_PAD_LEFT);
            $birthDay = str_pad(rand(1, 28), 2, '0', STR_PAD_LEFT);
            $placeOfBirth = str_pad(rand(1, 16), 2, '0', STR_PAD_LEFT);
            $lastFour = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $icNumber = "{$birthYear}{$birthMonth}{$birthDay}-{$placeOfBirth}-{$lastFour}";

            // Select random state and corresponding city
            $selectedState = $malaysianStates[array_rand($malaysianStates)];
            $availableCities = $cities[$selectedState] ?? [$selectedState];
            $selectedCity = $availableCities[array_rand($availableCities)];

            // Choose working position based on role
            $isMainAgent = $user->role_name === 'main-agent';
            $seniorPositions = array_slice($workingPositions, 0, 9);
            $agentPositions = array_slice($workingPositions, 9);
            $workingPosition = $isMainAgent ?
                $seniorPositions[array_rand($seniorPositions)] :
                $agentPositions[array_rand($agentPositions)];

            // Choose income range based on role (main agents typically have higher income)
            $incomeRange = $isMainAgent ?
                $incomeRanges->where('min', '>=', 8001)->first() : // Higher income for main agents
                $incomeRanges->where('min', '<=', 12000)->first(); // Lower-mid income for agents

            // Handle case if no income range matches the criteria
            if (!$incomeRange) {
                $incomeRange = $incomeRanges->first();
            }

            // Generate bank details
            if ($bankNames->isEmpty()) {
                $this->command->error('No bank names found. Please run AcsBankNamesSeeder first.');
                return;
            }

            $selectedBank = $bankNames->random();
            $accountNumber = rand(************, ************); // 12 digit account number

            // Generate next of kin details
            $kinNames = [
                'Ahmad bin Abdullah', 'Siti binti Hassan', 'Lim Wei Cheng', 'Priya Devi',
                'Mohd Farid bin Ali', 'Nurul Aina binti Omar', 'Tan Ah Kow', 'Kavitha Nair',
                'Abdul Rahman bin Ismail', 'Fatimah binti Yusof', 'Wong Li Ming', 'Deepa Kumari'
            ];
            $kinName = $kinNames[array_rand($kinNames)];

            // Generate kin IC (different from user IC)
            $kinBirthYear = rand(45, 85);
            $kinBirthMonth = str_pad(rand(1, 12), 2, '0', STR_PAD_LEFT);
            $kinBirthDay = str_pad(rand(1, 28), 2, '0', STR_PAD_LEFT);
            $kinPlaceOfBirth = str_pad(rand(1, 16), 2, '0', STR_PAD_LEFT);
            $kinLastFour = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $kinIC = "{$kinBirthYear}{$kinBirthMonth}{$kinBirthDay}-{$kinPlaceOfBirth}-{$kinLastFour}";

            // Generate kin phone
            $kinPhone = '+601' . rand(1, 9) . str_pad(rand(1000000, 9999999), 7, '0', STR_PAD_LEFT);

            // Determine KYC status and approval date
            $statuses = ['pending', 'approved', 'rejected', 'in_progress'];
            $weights = [0.1, 0.7, 0.1, 0.1]; // 70% approved, 10% each for others
            $randomValue = mt_rand() / mt_getrandmax();
            $cumulativeWeight = 0;
            $status = 'pending';

            foreach ($statuses as $index => $statusOption) {
                $cumulativeWeight += $weights[$index];
                if ($randomValue <= $cumulativeWeight) {
                    $status = $statusOption;
                    break;
                }
            }

            $kycApproveAt = $status === 'approved' ?
                Carbon::now()->subDays(rand(1, 90)) :
                null;

            // Create bank detail first
            $bankDetail = DB::table('acs_bank_details')->insertGetId([
                'acs_users_id' => $user->id,
                'acs_bank_names_id' => $selectedBank->id,
                'account_number' => (string) $accountNumber,
                'account_holder_name' => $user->name,
                'bank_statement' => 'bank_statements/' . strtolower(str_replace(' ', '_', $user->name)) . '_statement.pdf',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            $userDetails[] = [
                'acs_users_id' => $user->id,
                'status' => $status,
                'no_ic' => $icNumber,
                'address_1' => 'No. ' . rand(1, 999) . ', Jalan ' . ['Merdeka', 'Tunku Abdul Rahman', 'Bukit Bintang', 'Ampang', 'Gombak'][array_rand(['Merdeka', 'Tunku Abdul Rahman', 'Bukit Bintang', 'Ampang', 'Gombak'])],
                'address_2' => 'Taman ' . ['Melawati', 'Desa', 'Sri', 'Bandar'][array_rand(['Melawati', 'Desa', 'Sri', 'Bandar'])] . ' ' . ['Indah', 'Jaya', 'Bahagia'][array_rand(['Indah', 'Jaya', 'Bahagia'])],
                'postcode' => str_pad(rand(10000, 99999), 5, '0', STR_PAD_LEFT),
                'city' => $selectedCity,
                'state' => $selectedState,
                'working_position' => $workingPosition,
                'acs_range_income_id' => $incomeRange->id,
                'acs_bank_detail_id' => $bankDetail,
                'next_to_kin_name' => $kinName,
                'next_to_kin_ic' => $kinIC,
                'next_to_kin_phone' => $kinPhone,
                'kyc_approve_at' => $kycApproveAt,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];

            $statusText = $status === 'approved' ? 'APPROVED' : strtoupper($status);
            $this->command->info("Prepared details for {$user->role_name}: {$user->name} - Status: {$statusText}");
        }

        // Insert all user details
        if (!empty($userDetails)) {
            DB::table('acs_users_details')->insert($userDetails);
            $this->command->info("Successfully created " . count($userDetails) . " user detail records.");
        }

        // Summary
        $summary = DB::table('acs_users_details')
            ->join('acs_users', 'acs_users_details.acs_users_id', '=', 'acs_users.id')
            ->join('acs_roles', 'acs_users.acs_role_id', '=', 'acs_roles.id')
            ->whereIn('acs_roles.name', ['main-agent', 'agent'])
            ->whereNull('acs_users_details.deleted_at')
            ->selectRaw('acs_roles.name as role, acs_users_details.status, COUNT(*) as count')
            ->groupBy('acs_roles.name', 'acs_users_details.status')
            ->get();

        $this->command->info("\n📊 User Details Summary:");
        foreach ($summary as $item) {
            $this->command->info("• {$item->role}: {$item->count} users with status '{$item->status}'");
        }
    }
}
