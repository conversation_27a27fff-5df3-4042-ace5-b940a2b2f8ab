<?php

namespace Database\Seeders;

use App\Models\AcsReferralCodeSetting;
use Illuminate\Database\Seeder;

class AcsReferralCodeSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a default random method
        AcsReferralCodeSetting::create([
            'method_name' => AcsReferralCodeSetting::METHOD_RANDOM,
            'is_active' => true,
            'code_length' => 6,
            'prefix' => 'RMS',
            'suffix' => '',
            'include_numbers' => true,
            'include_uppercase' => true,
            'include_lowercase' => false,
            'exclude_similar_chars' => true,
            'settings' => [
                'avoid_profanity' => true,
                'ensure_uniqueness' => true,
            ],
            'created_by' => '01k17g432ad98pjh43dp8cm1xa', // Super admin user ID
        ]);


        $this->command->info('Referral code settings seeded successfully');
    }
}
