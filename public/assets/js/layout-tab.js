"use strict";!function(){document.getElementsByTagName("body")[0].setAttribute("data-pc-layout","tab");document.querySelector(".pc-navbar").innerHTML;for(var r,o,c,l,i,d=document.querySelector(".tab-container > .tab-sidemenu > .pc-tab-link"),s=document.querySelector(".tab-container > .tab-link > .navbar-content > .tab-content"),e=document.querySelector(".tab-container > .tab-sidemenu"),e=(e&&new SimpleBar(e),document.querySelector(".tab-container > .tab-link .navbar-content")),t=(e&&new SimpleBar(e),document.querySelectorAll(".pc-navbar li .pc-submenu")),a=0;a<t.length;a++)t[a].style.display="none";r=document.querySelectorAll(".pc-navbar > li.pc-item"),c=0,l=!1,i=o="",r.forEach(function(t,e){if(t.classList.contains("pc-caption")){if(d){c+=1;var a="";try{a=t.children[1].outerHTML}catch(e){a=t.children[0].innerHTML.charAt(0)}d.insertAdjacentHTML("beforeend",'<li class="nav-item" data-bs-toggle="tooltip" title="'+t.children[0].innerHTML+'"><a class="nav-link" id="pc-tab-link-'+c+'" data-bs-target="#pc-tab-'+c+'" role="tab" data-bs-toggle="tab" aria-controls="home-tab-pane"            "data-bs-placement="right">'+a+"</a></li>")}!0===l&&s&&(0==(n=c-1)&&(i=o),1==n&&(o=i+=o,i=""),s.insertAdjacentHTML("beforeend",'<div class="tab-pane fade" id="pc-tab-'+n+'" role="tabpanel" aria-labelledby="pc-tab-link-'+n+'" tabindex="'+n+'"><ul class="pc-navbar">              '+o+"              </ul></div>"),o=""),t.remove()}else{var n;o+=t.outerHTML,l=!0,t.remove(),e+1===r.length&&s&&(n=c,s.insertAdjacentHTML("beforeend",'<div class="tab-pane fade" id="pc-tab-'+n+'" role="tabpanel" aria-labelledby="pc-tab-link-'+n+'" tabindex="'+n+'"><ul class="pc-navbar">              '+o+"              </ul></div>"),o="")}});for(var n=document.querySelectorAll(".pc-sidebar .pc-navbar a"),b=0;b<n.length;b++){var p=window.location.href.split(/[?#]/)[0];if(n[b].href==p&&""!=n[b].getAttribute("href")){n[b].parentNode.classList.add("active"),n[b].parentNode.parentNode.parentNode.classList.add("pc-trigger"),n[b].parentNode.parentNode.parentNode.classList.add("active"),n[b].parentNode.parentNode.style.display="block",n[b].parentNode.parentNode.parentNode.parentNode.parentNode.classList.add("pc-trigger"),n[b].parentNode.parentNode.parentNode.parentNode.style.display="block";for(var u,m=!0,v=n[b];m;)(v=v.parentNode).classList.contains("tab-pane")&&(u=v.getAttribute("id"),u=document.querySelector('.tab-sidemenu a[data-bs-target="#'+u+'"]'),new bootstrap.Tab(u).show(),m=!1)}}}();