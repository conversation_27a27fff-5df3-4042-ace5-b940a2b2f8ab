"use strict";if (typeof window.DEFAULT_OPTIONS === 'undefined') {
window.DEFAULT_OPTIONS={flagList:{en:"flag-united-kingdom",pl:"flag-poland",ja:"flag-japan",de:"flag-germany"},preloadLngs:["en"],fallbackLng:"en",loadPath:"../assets/json/locales/{{lng}}.json"};
if (typeof window.Translator === 'undefined') {
window.Translator = class Translator{constructor(t={}){this._options={...window.DEFAULT_OPTIONS,...t},this._currentLng=this._options.fallbackLng,this._i18nextInit(),this._listenToLangChange()}_i18nextInit(){i18next.use(i18nextHttpBackend).init({fallbackLng:this._options.fallbackLng,preload:this._options.preloadLngs,backend:{loadPath:this._options.loadPath,stringify:JSON.stringify}}).then(()=>{this._translateAll()})}_listenToLangChange=()=>{document.querySelectorAll("[data-lng]").forEach(t=>{t.addEventListener("click",()=>{this._currentLng=t.getAttribute("data-lng"),i18next.changeLanguage(this._currentLng).then(()=>{this._translateAll()})})})};_translateAll=()=>{document.querySelectorAll("[data-i18n]").forEach(t=>{var n=t.dataset.i18n;t.innerHTML=i18next.t(n)})}};
if (!window.translatorInstance) {
window.translatorInstance = new window.Translator;
}
}
}
