"use strict";!function(){var e=document.querySelector("#tree-demo");const a=document.querySelector("#tree-msg");var n=new VanillaTree(e,{contextmenu:[{label:"Hey",action:function(e){alert("Hey "+e)}},{label:"Blah",action:function(e){alert("Blah "+e)}}]});n.add({label:"Label A",id:"a",opened:!0}),n.add({label:"Label B",id:"b"}),n.add({label:"Label A.A",parent:"a",id:"a.a",opened:!0,selected:!0}),n.add({label:"Label A.A.A",parent:"a.a"}),n.add({label:"Label A.A.B",parent:"a.a"}),n.add({label:"Label B.A",parent:"b"}),e.addEventListener("vtree-open",function(e){a.innerHTML=e.detail.id+" is opened"}),e.addEventListener("vtree-close",function(e){a.innerHTML=e.detail.id+" is closed"}),e.addEventListener("vtree-select",function(e){a.innerHTML=e.detail.id+" is selected"})}();