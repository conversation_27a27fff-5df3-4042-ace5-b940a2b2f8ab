/*! FixedHeader 4.0.1
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var i,s;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(i=require("jquery"),s=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||i(t),s(t,e),o(e,t,t.document)}:(s(window,i),module.exports=o(i,window,window.document))):o(jQuery,window,document)}(function(b,m,v){"use strict";function s(t,e){if(!d.versionCheck("2"))throw"Warning: FixedHeader requires DataTables 2 or newer";if(!(this instanceof s))throw"FixedHeader must be initialised with the 'new' keyword.";if(!0===e&&(e={}),t=new d.Api(t),this.c=b.extend(!0,{},s.defaults,e),this.s={dt:t,position:{theadTop:0,tbodyTop:0,tfootTop:0,tfootBottom:0,width:0,left:0,tfootHeight:0,theadHeight:0,windowHeight:b(m).height(),visible:!0},headerMode:null,footerMode:null,autoWidth:t.settings()[0].oFeatures.bAutoWidth,namespace:".dtfc"+o++,scrollLeft:{header:-1,footer:-1},enable:!0,autoDisable:!1},this.dom={floatingHeader:null,thead:b(t.table().header()),tbody:b(t.table().body()),tfoot:b(t.table().footer()),header:{host:null,floating:null,floatingParent:b('<div class="dtfh-floatingparent"><div></div></div>'),placeholder:null},footer:{host:null,floating:null,floatingParent:b('<div class="dtfh-floatingparent"><div></div></div>'),placeholder:null}},this.dom.header.host=this.dom.thead.parent(),this.dom.footer.host=this.dom.tfoot.parent(),(e=t.settings()[0])._fixedHeader)throw"FixedHeader already initialised on table "+e.nTable.id;(e._fixedHeader=this)._constructor()}var d=b.fn.dataTable,o=0;return b.extend(s.prototype,{destroy:function(){var t=this.dom;this.s.dt.off(".dtfc"),b(m).off(this.s.namespace),t.header.rightBlocker&&t.header.rightBlocker.remove(),t.header.leftBlocker&&t.header.leftBlocker.remove(),t.footer.rightBlocker&&t.footer.rightBlocker.remove(),t.footer.leftBlocker&&t.footer.leftBlocker.remove(),this.c.header&&this._modeChange("in-place","header",!0),this.c.footer&&t.tfoot.length&&this._modeChange("in-place","footer",!0)},enable:function(t,e,o){this.s.enable=t,this.s.enableType=o,!e&&void 0!==e||(this._positions(),this._scroll(!0))},enabled:function(){return this.s.enable},headerOffset:function(t){return void 0!==t&&(this.c.headerOffset=t,this.update()),this.c.headerOffset},footerOffset:function(t){return void 0!==t&&(this.c.footerOffset=t,this.update()),this.c.footerOffset},update:function(t){var e=this.s.dt.table().node();(this.s.enable||this.s.autoDisable)&&(b(e).is(":visible")?(this.s.autoDisable=!1,this.enable(!0,!1)):(this.s.autoDisable=!0,this.enable(!1,!1)),0!==b(e).children("thead").length)&&(this._positions(),this._scroll(void 0===t||t),this._widths(this.dom.header),this._widths(this.dom.footer))},_constructor:function(){var o=this,i=this.s.dt,t=(b(m).on("scroll"+this.s.namespace,function(){o._scroll()}).on("resize"+this.s.namespace,d.util.throttle(function(){o.s.position.windowHeight=b(m).height(),o.update()},50)),b(".fh-fixedHeader")),t=(!this.c.headerOffset&&t.length&&(this.c.headerOffset=t.outerHeight()),b(".fh-fixedFooter"));!this.c.footerOffset&&t.length&&(this.c.footerOffset=t.outerHeight()),i.on("column-reorder.dt.dtfc column-visibility.dt.dtfc column-sizing.dt.dtfc responsive-display.dt.dtfc",function(t,e){o.update()}).on("draw.dt.dtfc",function(t,e){o.update(e!==i.settings()[0])}),i.on("destroy.dtfc",function(){o.destroy()}),this._positions(),this._scroll()},_clone:function(t,e){var o,i,s=this,d=this.s.dt,n=this.dom[t],r="header"===t?this.dom.thead:this.dom.tfoot;"footer"===t&&this._scrollEnabled()||(!e&&n.floating?n.floating.removeClass("fixedHeader-floating fixedHeader-locked"):(n.floating&&(null!==n.placeholder&&n.placeholder.remove(),n.floating.children().detach(),n.floating.remove()),e=b(d.table().node()),o=b(e.parent()),i=this._scrollEnabled(),n.floating=b(d.table().node().cloneNode(!1)).attr("aria-hidden","true").css({top:0,left:0}).removeAttr("id"),n.floatingParent.css({width:o[0].offsetWidth,overflow:"hidden",height:"fit-content",position:"fixed",left:i?e.offset().left+o.scrollLeft():0}).css("header"===t?{top:this.c.headerOffset,bottom:""}:{top:"",bottom:this.c.footerOffset}).addClass("footer"===t?"dtfh-floatingparent-foot":"dtfh-floatingparent-head").appendTo("body").children().eq(0).append(n.floating),this._stickyPosition(n.floating,"-"),(i=function(){var t=o.scrollLeft();s.s.scrollLeft={footer:t,header:t},n.floatingParent.scrollLeft(s.s.scrollLeft.header)})(),o.off("scroll.dtfh").on("scroll.dtfh",i),n.floatingParent.children().css({width:"fit-content",paddingRight:s.s.dt.settings()[0].oBrowser.barWidth}),(e=b("footer"===t?"div.dtfc-bottom-blocker":"div.dtfc-top-blocker",d.table().container())).length&&e.clone().appendTo(n.floatingParent).css({position:"fixed",right:e.width()}),n.placeholder=r.clone(!1),n.placeholder.find("*[id]").removeAttr("id"),n.host.prepend(n.placeholder),n.floating.append(r),this._widths(n)))},_stickyPosition:function(t,e){var i;this._scrollEnabled()&&(i="rtl"===b(this.s.dt.table().node()).css("direction"),t.find("th").each(function(){var t,e,o;"sticky"===b(this).css("position")&&(t=b(this).css("right"),e=b(this).css("left"),"auto"===t||i?"auto"!==e&&i&&(o=+e.replace(/px/g,""),b(this).css("left",0<o?o:0)):(o=+t.replace(/px/g,""),b(this).css("right",0<o?o:0)))}))},_horizontal:function(t,e){var o,i=this.dom[t],s=this.s.scrollLeft;i.floating&&s[t]!==e&&(this._scrollEnabled()&&(o=b(b(this.s.dt.table().node()).parent()).scrollLeft(),i.floating.scrollLeft(o),i.floatingParent.scrollLeft(o)),s[t]=e)},_modeChange:function(t,e,o){var i,s,d,n,r,a,f,h=this.dom[e],l=this.s.position,c=this._scrollEnabled();"footer"===e&&c||(i=function(t){h.floating[0].style.setProperty("width",t+"px","important"),c||h.floatingParent[0].style.setProperty("width",t+"px","important")},n=this.dom["footer"===e?"tfoot":"thead"],s=b.contains(n[0],v.activeElement)?v.activeElement:null,r=b(b(this.s.dt.table().node()).parent()),"in-place"===t?(h.placeholder&&(h.placeholder.remove(),h.placeholder=null),"header"===e?h.host.prepend(n):h.host.append(n),h.floating&&(h.floating.remove(),h.floating=null,this._stickyPosition(h.host,"+")),h.floatingParent&&(h.floatingParent.find("div.dtfc-top-blocker").remove(),h.floatingParent.remove()),b(b(h.host.parent()).parent()).scrollLeft(r.scrollLeft())):"in"===t?(this._clone(e,o),n=r.offset(),f=(d=b(v).scrollTop())+b(m).height(),a=c?n.top:l.tbodyTop,n=c?n.top+r.outerHeight():l.tfootTop,r="footer"===e?f<a?l.tfootHeight:a+l.tfootHeight-f:d+this.c.headerOffset+l.theadHeight-n,a="header"===e?"top":"bottom",f=this.c[e+"Offset"]-(0<r?r:0),h.floating.addClass("fixedHeader-floating"),h.floatingParent.css(a,f).css({left:l.left,"z-index":3}),i(l.width),"footer"===e&&h.floating.css("top","")):"below"===t?(this._clone(e,o),h.floating.addClass("fixedHeader-locked"),h.floatingParent.css({position:"absolute",top:l.tfootTop-l.theadHeight,left:l.left+"px"}),i(l.width)):"above"===t&&(this._clone(e,o),h.floating.addClass("fixedHeader-locked"),h.floatingParent.css({position:"absolute",top:l.tbodyTop,left:l.left+"px"}),i(l.width)),s&&s!==v.activeElement&&setTimeout(function(){s.focus()},10),this.s.scrollLeft.header=-1,this.s.scrollLeft.footer=-1,this.s[e+"Mode"]=t)},_positions:function(){var t=this.s.dt,e=t.table(),o=this.s.position,i=this.dom,e=b(e.node()),s=this._scrollEnabled(),d=b(t.table().header()),t=b(t.table().footer()),i=i.tbody,n=e.parent();o.visible=e.is(":visible"),o.width=e.outerWidth(),o.left=e.offset().left,o.theadTop=d.offset().top,o.tbodyTop=(s?n:i).offset().top,o.tbodyHeight=(s?n:i).outerHeight(),o.theadHeight=d.outerHeight(),o.theadBottom=o.theadTop+o.theadHeight,o.tfootTop=o.tbodyTop+o.tbodyHeight,t.length?(o.tfootBottom=o.tfootTop+t.outerHeight(),o.tfootHeight=t.outerHeight()):(o.tfootBottom=o.tfootTop,o.tfootHeight=0)},_scroll:function(t){var e,o,i,s,d,n,r,a,f,h,l,c,p,g,u;this.s.dt.settings()[0].bDestroying||(e=this._scrollEnabled(),i=(o=b(this.s.dt.table().node()).parent()).offset(),h=o.outerHeight(),s=b(v).scrollLeft(),d=b(v).scrollTop(),n=b(m).height()+d,l=this.s.position,c=e?i.top:l.tbodyTop,a=(e?i:l).left,h=e?i.top+h:l.tfootTop,f=e?o.outerWidth():l.tbodyWidth,this.c.header&&(!this.s.enable||!l.visible||d+this.c.headerOffset+l.theadHeight<=c?r="in-place":d+this.c.headerOffset+l.theadHeight>c&&d+this.c.headerOffset+l.theadHeight<h?(r="in",d+this.c.headerOffset+l.theadHeight>h||void 0===this.dom.header.floatingParent?t=!0:this.dom.header.floatingParent.css({top:this.c.headerOffset,position:"fixed"}).children().eq(0).append(this.dom.header.floating)):r="below",!t&&r===this.s.headerMode||this._modeChange(r,"header",t),this._horizontal("header",s)),p={offset:{top:0,left:0},height:0},g={offset:{top:0,left:0},height:0},this.c.footer&&this.dom.tfoot.length&&this.dom.tfoot.find("th, td").length&&(!this.s.enable||!l.visible||l.tfootBottom+this.c.footerOffset<=n?u="in-place":h+l.tfootHeight+this.c.footerOffset>n&&c+this.c.footerOffset<n?(u="in",t=!0):u="above",!t&&u===this.s.footerMode||this._modeChange(u,"footer",t),this._horizontal("footer",s),h=function(t){return{offset:t.offset(),height:t.outerHeight()}},p=this.dom.header.floating?h(this.dom.header.floating):h(this.dom.thead),g=this.dom.footer.floating?h(this.dom.footer.floating):h(this.dom.tfoot),e)&&g.offset.top>d&&(c=n+((l=d-i.top)>-p.height?l:0)-(p.offset.top+(l<-p.height?p.height:0)+g.height),o.outerHeight(c=c<0?0:c),Math.round(o.outerHeight())>=Math.round(c)?b(this.dom.tfoot.parent()).addClass("fixedHeader-floating"):b(this.dom.tfoot.parent()).removeClass("fixedHeader-floating")),this.dom.header.floating&&this.dom.header.floatingParent.css("left",a-s),this.dom.footer.floating&&this.dom.footer.floatingParent.css("left",a-s),void 0!==this.s.dt.settings()[0]._fixedColumns&&(this.dom.header.rightBlocker=(u=function(t,e,o){var i;return null!==(o=void 0===o?0===(i=b("div.dtfc-"+t+"-"+e+"-blocker")).length?null:i.clone().css("z-index",1):o)&&("in"===r||"below"===r?o.appendTo("body").css({top:("top"===e?p:g).offset.top,left:"right"===t?a+f-o.width():a}):o.detach()),o})("right","top",this.dom.header.rightBlocker),this.dom.header.leftBlocker=u("left","top",this.dom.header.leftBlocker),this.dom.footer.rightBlocker=u("right","bottom",this.dom.footer.rightBlocker),this.dom.footer.leftBlocker=u("left","bottom",this.dom.footer.leftBlocker)))},_scrollEnabled:function(){var t=this.s.dt.settings()[0].oScroll;return""!==t.sY||""!==t.sX},_widths:function(t){if(t&&t.placeholder)for(var e=b(this.s.dt.table().node()),o=b(e.parent()),i=(t.floatingParent.css("width",o[0].offsetWidth),t.floating.css("width",e[0].offsetWidth),b("colgroup",t.floating).remove(),t.placeholder.parent().find("colgroup").clone().appendTo(t.floating).find("col")),s=this.s.dt.columns(":visible").widths(),d=0;d<s.length;d++)i.eq(d).css("width",s[d])}}),s.version="4.0.1",s.defaults={header:!0,footer:!1,headerOffset:0,footerOffset:0},b.fn.dataTable.FixedHeader=s,b.fn.DataTable.FixedHeader=s,b(v).on("init.dt.dtfh",function(t,e,o){var i;"dt"===t.namespace&&(t=e.oInit.fixedHeader,i=d.defaults.fixedHeader,t||i)&&!e._fixedHeader&&(i=b.extend({},i,t),!1!==t)&&new s(e,i)}),d.Api.register("fixedHeader()",function(){}),d.Api.register("fixedHeader.adjust()",function(){return this.iterator("table",function(t){t=t._fixedHeader;t&&t.update()})}),d.Api.register("fixedHeader.enable()",function(e){return this.iterator("table",function(t){t=t._fixedHeader;e=void 0===e||e,t&&e!==t.enabled()&&t.enable(e)})}),d.Api.register("fixedHeader.enabled()",function(){if(this.context.length){var t=this.context[0]._fixedHeader;if(t)return t.enabled()}return!1}),d.Api.register("fixedHeader.disable()",function(){return this.iterator("table",function(t){t=t._fixedHeader;t&&t.enabled()&&t.enable(!1)})}),b.each(["header","footer"],function(t,o){d.Api.register("fixedHeader."+o+"Offset()",function(e){var t=this.context;return void 0===e?t.length&&t[0]._fixedHeader?t[0]._fixedHeader[o+"Offset"]():void 0:this.iterator("table",function(t){t=t._fixedHeader;t&&t[o+"Offset"](e)})})}),d});