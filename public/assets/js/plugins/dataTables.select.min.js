/*! Select for DataTables 2.1.0
 * © SpryMedia Ltd - datatables.net/license/mit
 */
!function(l){var s,c;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return l(e,window,document)}):"object"==typeof exports?(s=require("jquery"),c=function(e,t){t.fn.dataTable||require("datatables.net")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||s(e),c(e,t),l(t,e,e.document)}:(c(window,s),module.exports=l(s,window,window.document))):l(jQuery,window,document)}(function(m,i,e){"use strict";var p=m.fn.dataTable;function r(n,e,t){function l(t,l){l<t&&(e=l,l=t,t=e);var e,s=!1;return n.columns(":visible").indexes().filter(function(e){return e===t&&(s=!0),e===l?!(s=!1):s})}function s(t,l){var e,s=n.rows({search:"applied"}).indexes(),c=(s.indexOf(t)>s.indexOf(l)&&(e=l,l=t,t=e),!1);return s.filter(function(e){return e===t&&(c=!0),e===l?!(c=!1):c})}var c,t=n.cells({selected:!0}).any()||t?(c=l(t.column,e.column),s(t.row,e.row)):(c=l(0,e.column),s(0,e.row)),t=n.cells(t,c).flatten();n.cells(e,{selected:!0}).any()?n.cells(t).deselect():n.cells(t).select()}function v(e){var t=p.select.classes.checkbox;return e?t.replace(/ /g,"."):t}function c(e){var t=e.settings()[0]._select.selector;m(e.table().container()).off("mousedown.dtSelect",t).off("mouseup.dtSelect",t).off("click.dtSelect",t),m("body").off("click.dtSelect"+_(e.table().node()))}function n(o){var a,t=m(o.table().container()),l=o.settings()[0],s=l._select.selector;t.on("mousedown.dtSelect",s,function(e){(e.shiftKey||e.metaKey||e.ctrlKey)&&t.css("-moz-user-select","none").one("selectstart.dtSelect",s,function(){return!1}),i.getSelection&&(a=i.getSelection())}).on("mouseup.dtSelect",s,function(){t.css("-moz-user-select","")}).on("click.dtSelect",s,function(e){var t,l=o.select.items();if(a){var s=i.getSelection();if((!s.anchorNode||m(s.anchorNode).closest("table")[0]===o.table().node())&&s!==a)return}var c,s=o.settings()[0],n=o.table().container();m(e.target).closest("div.dt-container")[0]==n&&(n=o.cell(m(e.target).closest("td, th"))).any()&&(c=m.Event("user-select.dt"),u(o,c,[l,n,e]),c.isDefaultPrevented()||(c=n.index(),"row"===l?(t=c.row,h(e,o,s,"row",t)):"column"===l?(t=n.index().column,h(e,o,s,"column",t)):"cell"===l&&(t=n.index(),h(e,o,s,"cell",t)),s._select_lastCell=c))}),m("body").on("click.dtSelect"+_(o.table().node()),function(e){var t;!l._select.blurable||m(e.target).parents().filter(o.table().container()).length||0===m(e.target).parents("html").length||m(e.target).parents("div.DTE").length||(t=m.Event("select-blur.dt"),u(o,t,[e.target,e]),t.isDefaultPrevented())||f(l,!0)})}function u(e,t,l,s){s&&!e.flatten().length||("string"==typeof t&&(t+=".dt"),l.unshift(e),m(e.table().node()).trigger(t,l))}function b(e){return e.mRender&&"selectCheckbox"===e.mRender._name}function s(s,e){var t,l,c,n,o;"api"!==s.select.style()&&!1!==s.select.info()&&(o=s.settings()[0]._select_set.length||s.rows({selected:!0}).count(),t=s.columns({selected:!0}).count(),l=s.cells({selected:!0}).count(),c=function(e,t,l){e.append(m('<span class="select-item"/>').append(s.i18n("select."+t+"s",{_:"%d "+t+"s selected",0:"",1:"1 "+t+" selected"},l)))},e=m(e),c(n=m('<span class="select-info"/>'),"row",o),c(n,"column",t),c(n,"cell",l),(o=e.children("span.select-info")).length&&o.remove(),""!==n.text())&&e.append(n)}function o(o){var r,a=new p.Api(o);o._select_init=!0,o._select_set=[],o.aoRowCreatedCallback.push(function(e,t,l){var s,c,n=o.aoData[l],l=a.row(l).id();for((n._select_selected||"undefined"!==l&&o._select_set.includes(l))&&(n._select_selected=!0,m(e).addClass(o._select.className).find("input."+v(!0)).prop("checked",!0)),s=0,c=o.aoColumns.length;s<c;s++)(o.aoColumns[s]._select_selected||n._selected_cells&&n._selected_cells[s])&&m(n.anCells[s]).addClass(o._select.className)}),(r=a).on("select",function(e,t,l,s){if("row"===l)for(var l=r.settings()[0],c=r,n=l._select_set,o=s,a=0;a<o.length;a++){var i=c.row(o[a]).id();i&&"undefined"!==i&&!n.includes(i)&&n.push(i)}}),r.on("deselect",function(e,t,l,s){if("row"===l)for(var l=r.settings()[0],c=r,n=l._select_set,o=s,a=0;a<o.length;a++){var i=c.row(o[a]).id(),i=n.indexOf(i);-1!==i&&n.splice(i,1)}}),a.on("info.dt",function(e,t,l){t._select.infoEls.includes(l)||t._select.infoEls.push(l),s(a,l)}),a.on("select.dtSelect.dt deselect.dtSelect.dt",function(){o._select.infoEls.forEach(function(e){s(a,e)}),a.state.save()}),a.on("destroy.dtSelect",function(){m(a.rows({selected:!0}).nodes()).removeClass(a.settings()[0]._select.className),m("input."+v(!0),a.table().header()).remove(),c(a),a.off(".dtSelect"),m("body").off(".dtSelect"+_(a.table().node()))})}function d(e,t,l,s){var c,n=e[t+"s"]({search:"applied"}).indexes(),s=n.indexOf(s),o=n.indexOf(l);e[t+"s"]({selected:!0}).any()||-1!==s?(o<s&&(c=o,o=s,s=c),n.splice(o+1,n.length),n.splice(0,s)):n.splice(n.indexOf(l)+1,n.length),e[t](l,{selected:!0}).any()?(n.splice(n.indexOf(l),1),e[t+"s"](n).deselect()):e[t+"s"](n).select()}function f(e,t){!t&&"single"!==e._select.style||((t=new p.Api(e)).rows({selected:!0}).deselect(),t.columns({selected:!0}).deselect(),t.cells({selected:!0}).deselect())}function h(e,t,l,s,c){var n=t.select.style(),o=t.select.toggleable(),a=t[s](c,{selected:!0}).any();a&&!o||("os"===n?e.ctrlKey||e.metaKey?t[s](c).select(!a):e.shiftKey?"cell"===s?r(t,c,l._select_lastCell||null):d(t,s,c,l._select_lastCell?l._select_lastCell[s]:null):(o=t[s+"s"]({selected:!0}),a&&1===o.flatten().length?t[s](c).deselect():(o.deselect(),t[s](c).select())):"multi+shift"==n&&e.shiftKey?"cell"===s?r(t,c,l._select_lastCell||null):d(t,s,c,l._select_lastCell?l._select_lastCell[s]:null):t[s](c).select(!a))}function _(e){return e.id.replace(/[^a-zA-Z0-9\-\_]/g,"-")}p.select={},p.select.classes={checkbox:"dt-select-checkbox"},p.select.version="2.1.0",p.select.init=function(o){var e,t,l,s,c,n,a,i,r,u,d,f,h,_=o.settings()[0];if(!p.versionCheck("2"))throw"Warning: Select requires DataTables 2 or newer";!_._select&&(e=o.state.loaded(),t=function(e,t,l){if(null!==l&&void 0!==l.select){if(o.rows({selected:!0}).any()&&o.rows().deselect(),void 0!==l.select.rows&&o.rows(l.select.rows).select(),o.columns({selected:!0}).any()&&o.columns().deselect(),void 0!==l.select.columns&&o.columns(l.select.columns).select(),o.cells({selected:!0}).any()&&o.cells().deselect(),void 0!==l.select.cells)for(var s=0;s<l.select.cells.length;s++)o.cell(l.select.cells[s].row,l.select.cells[s].column).select();o.state.save()}},o.on("stateSaveParams",function(e,t,l){l.select={},l.select.rows=o.rows({selected:!0}).ids(!0).toArray(),l.select.columns=o.columns({selected:!0})[0],l.select.cells=o.cells({selected:!0})[0].map(function(e){return{row:o.row(e.row).id(!0),column:e.column}})}).on("stateLoadParams",t).one("init",function(){t(0,0,e)}),s=_.oInit.select,l=p.defaults.select,l=void 0===s?l:s,s="row",a=!(n=!(c="api")),u="td, th",d="selected",h=!(f=r=!(i=null)),_._select={infoEls:[]},!0===l?(c="os",h=!0):"string"==typeof l?(c=l,h=!0):m.isPlainObject(l)&&(void 0!==l.blurable&&(n=l.blurable),void 0!==l.toggleable&&(a=l.toggleable),void 0!==l.info&&(r=l.info),void 0!==l.items&&(s=l.items),h=(c=void 0!==l.style?l.style:"os",!0),void 0!==l.selector&&(u=l.selector),void 0!==l.className&&(d=l.className),void 0!==l.headerCheckbox&&(f=l.headerCheckbox),void 0!==l.selectable)&&(i=l.selectable),o.select.selector(u),o.select.items(s),o.select.style(c),o.select.blurable(n),o.select.toggleable(a),o.select.info(r),o.select.selectable(i),_._select.className=d,!h&&m(o.table().node()).hasClass("selectable")&&o.select.style("os"),f||"select-page"===f||"select-all"===f)&&o.ready(function(){var c,n,l;n=f,l=(c=o).settings()[0].aoColumns,c.columns().iterator("column",function(e,t){var s;b(l[t])&&(t=c.column(t).header(),m("input",t).length||(s=m("<input>").attr({class:v(!0),type:"checkbox","aria-label":c.i18n("select.aria.headerCheckbox")||"Select all rows"}).appendTo(t).on("change",function(){this.checked?("select-page"==n?c.rows({page:"current"}):c.rows({search:"applied"})).select():("select-page"==n?c.rows({page:"current",selected:!0}):c.rows({selected:!0})).deselect()}).on("click",function(e){e.stopPropagation()}),c.on("draw select deselect",function(e,t,l){"row"!==l&&l||((l=function(e,t){var l=e.settings()[0],s=l._select.selectable,c=0,n=("select-page"==t?e.rows({page:"current",selected:!0}):e.rows({selected:!0})).count(),o=("select-page"==t?e.rows({page:"current",selected:!0}):e.rows({search:"applied",selected:!0})).count();if(s)for(var a=("select-page"==t?e.rows({page:"current"}):e.rows({search:"applied"})).indexes(),i=0;i<a.length;i++){var r=l.aoData[a[i]];s(r._aData,r.nTr,a[i])&&c++}else c=("select-page"==t?e.rows({page:"current"}):e.rows({search:"applied"})).count();return{available:c,count:n,search:o}}(c,n)).search&&l.search<=l.count&&l.search===l.available?s.prop("checked",!0).prop("indeterminate",!1):0===l.search&&0===l.count?s.prop("checked",!1).prop("indeterminate",!1):s.prop("checked",!1).prop("indeterminate",!0))})))})})},m.each([{type:"row",prop:"aoData"},{type:"column",prop:"aoColumns"}],function(e,i){p.ext.selector[i.type].push(function(e,t,l){var s,c=t.selected,n=[];if(!0!==c&&!1!==c)return l;for(var o=0,a=l.length;o<a;o++)(s=e[i.prop][l[o]])&&(!0===c&&!0===s._select_selected||!1===c&&!s._select_selected)&&n.push(l[o]);return n})}),p.ext.selector.cell.push(function(e,t,l){var s,c=t.selected,n=[];if(void 0===c)return l;for(var o=0,a=l.length;o<a;o++)(s=e.aoData[l[o].row])&&(!0===c&&s._selected_cells&&!0===s._selected_cells[l[o].column]||!1===c&&(!s._selected_cells||!s._selected_cells[l[o].column]))&&n.push(l[o]);return n});var t=p.Api.register,l=p.Api.registerPlural;function a(t,l){return function(e){return e.i18n("buttons."+t,l)}}function w(e){e=e._eventNamespace;return"draw.dt.DT"+e+" select.dt.DT"+e+" deselect.dt.DT"+e}t("select()",function(){return this.iterator("table",function(e){p.select.init(new p.Api(e))})}),t("select.blurable()",function(t){return void 0===t?this.context[0]._select.blurable:this.iterator("table",function(e){e._select.blurable=t})}),t("select.toggleable()",function(t){return void 0===t?this.context[0]._select.toggleable:this.iterator("table",function(e){e._select.toggleable=t})}),t("select.info()",function(t){return void 0===t?this.context[0]._select.info:this.iterator("table",function(e){e._select.info=t})}),t("select.items()",function(t){return void 0===t?this.context[0]._select.items:this.iterator("table",function(e){e._select.items=t,u(new p.Api(e),"selectItems",[t])})}),t("select.style()",function(l){return void 0===l?this.context[0]._select.style:this.iterator("table",function(e){e._select||p.select.init(new p.Api(e)),e._select_init||o(e),e._select.style=l;var t=new p.Api(e);"api"!==l?t.ready(function(){c(t),n(t)}):c(t),u(new p.Api(e),"selectStyle",[l])})}),t("select.selector()",function(s){return void 0===s?this.context[0]._select.selector:this.iterator("table",function(e){var t=new p.Api(e),l=e._select.style;c(t),e._select.selector=s,l&&"api"!==l?t.ready(function(){c(t),n(t)}):c(t)})}),t("select.selectable()",function(e){var t=this.context[0];return e?(t._select.selectable=e,this):t._select.selectable}),t("select.last()",function(e){var t=this.context[0];return e?(t._select_lastCell=e,this):t._select_lastCell}),t("select.cumulative()",function(){var e=this.context[0];return e&&e._select_set?e._select_set:[]}),l("rows().select()","row().select()",function(e){var o=this,a=[];return!1===e?this.deselect():(this.iterator("row",function(e,t){f(e);var l=e.aoData[t],s=e.aoColumns;if(e._select.selectable&&!1===e._select.selectable(l._aData,l.nTr,t))return;m(l.nTr).addClass(e._select.className),l._select_selected=!0,a.push(t);for(var c=0;c<s.length;c++){var n=s[c];null===n.sType&&o.columns().types(),b(n)&&((n=l.anCells)&&n[c]&&m("input."+v(!0),n[c]).prop("checked",!0),null!==l._aSortData)&&(l._aSortData[c]=null)}}),this.iterator("table",function(e){u(o,"select",["row",a],!0)}),this)}),t("row().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]]._select_selected)}),l("columns().select()","column().select()",function(e){var l=this;return!1===e?this.deselect():(this.iterator("column",function(e,t){f(e),e.aoColumns[t]._select_selected=!0;t=new p.Api(e).column(t);m(t.header()).addClass(e._select.className),m(t.footer()).addClass(e._select.className),t.nodes().to$().addClass(e._select.className)}),this.iterator("table",function(e,t){u(l,"select",["column",l[t]],!0)}),this)}),t("column().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoColumns[this[0]]&&e.aoColumns[this[0]]._select_selected)}),l("cells().select()","cell().select()",function(e){var l=this;return!1===e?this.deselect():(this.iterator("cell",function(e,t,l){f(e);t=e.aoData[t];void 0===t._selected_cells&&(t._selected_cells=[]),t._selected_cells[l]=!0,t.anCells&&m(t.anCells[l]).addClass(e._select.className)}),this.iterator("table",function(e,t){u(l,"select",["cell",l.cells(l[t]).indexes().toArray()],!0)}),this)}),t("cell().selected()",function(){var e=this.context[0];if(e&&this.length){e=e.aoData[this[0][0].row];if(e&&e._selected_cells&&e._selected_cells[this[0][0].column])return!0}return!1}),l("rows().deselect()","row().deselect()",function(){var o=this;return this.iterator("row",function(e,t){var l=e.aoData[t],s=e.aoColumns;m(l.nTr).removeClass(e._select.className),l._select_selected=!1,e._select_lastCell=null;for(var c=0;c<s.length;c++){var n=s[c];null===n.sType&&o.columns().types(),b(n)&&((n=l.anCells)&&n[c]&&m("input."+v(!0),l.anCells[c]).prop("checked",!1),null!==l._aSortData)&&(l._aSortData[c]=null)}}),this.iterator("table",function(e,t){u(o,"deselect",["row",o[t]],!0)}),this}),l("columns().deselect()","column().deselect()",function(){var l=this;return this.iterator("column",function(s,e){s.aoColumns[e]._select_selected=!1;var t=new p.Api(s),l=t.column(e);m(l.header()).removeClass(s._select.className),m(l.footer()).removeClass(s._select.className),t.cells(null,e).indexes().each(function(e){var t=s.aoData[e.row],l=t._selected_cells;!t.anCells||l&&l[e.column]||m(t.anCells[e.column]).removeClass(s._select.className)})}),this.iterator("table",function(e,t){u(l,"deselect",["column",l[t]],!0)}),this}),l("cells().deselect()","cell().deselect()",function(){var l=this;return this.iterator("cell",function(e,t,l){t=e.aoData[t];void 0!==t._selected_cells&&(t._selected_cells[l]=!1),t.anCells&&!e.aoColumns[l]._select_selected&&m(t.anCells[l]).removeClass(e._select.className)}),this.iterator("table",function(e,t){u(l,"deselect",["cell",l[t]],!0)}),this});var g=0;return m.extend(p.ext.buttons,{selected:{text:a("selected","Selected"),className:"buttons-selected",limitTo:["rows","columns","cells"],init:function(l,e,s){var c=this;s._eventNamespace=".select"+g++,l.on(w(s),function(){var e,t;c.enable((e=l,!(-1===(t=s).limitTo.indexOf("rows")||!e.rows({selected:!0}).any())||!(-1===t.limitTo.indexOf("columns")||!e.columns({selected:!0}).any())||!(-1===t.limitTo.indexOf("cells")||!e.cells({selected:!0}).any())))}),this.disable()},destroy:function(e,t,l){e.off(l._eventNamespace)}},selectedSingle:{text:a("selectedSingle","Selected single"),className:"buttons-selected-single",init:function(t,e,l){var s=this;l._eventNamespace=".select"+g++,t.on(w(l),function(){var e=t.rows({selected:!0}).flatten().length+t.columns({selected:!0}).flatten().length+t.cells({selected:!0}).flatten().length;s.enable(1===e)}),this.disable()},destroy:function(e,t,l){e.off(l._eventNamespace)}},selectAll:{text:a("selectAll","Select all"),className:"buttons-select-all",action:function(e,t,l,s){var c=this.select.items(),n=s.selectorModifier;(n?("function"==typeof n&&(n=n.call(t,e,t,l,s)),this[c+"s"](n)):this[c+"s"]()).select()}},selectNone:{text:a("selectNone","Deselect all"),className:"buttons-select-none",action:function(){f(this.settings()[0],!0)},init:function(t,e,l){var s=this;l._eventNamespace=".select"+g++,t.on(w(l),function(){var e=t.rows({selected:!0}).flatten().length+t.columns({selected:!0}).flatten().length+t.cells({selected:!0}).flatten().length;s.enable(0<e)}),this.disable()},destroy:function(e,t,l){e.off(l._eventNamespace)}},showSelected:{text:a("showSelected","Show only selected"),className:"buttons-show-selected",action:function(e,t){var s;t.search.fixed("dt-select")?(t.search.fixed("dt-select",null),this.active(!1)):(s=t.settings()[0].aoData,t.search.fixed("dt-select",function(e,t,l){return s[l]._select_selected}),this.active(!0)),t.draw()}}}),m.each(["Row","Column","Cell"],function(e,t){var c=t.toLowerCase();p.ext.buttons["select"+t+"s"]={text:a("select"+t+"s","Select "+c+"s"),className:"buttons-select-"+c+"s",action:function(){this.select.items(c)},init:function(e){var s=this;this.active(e.select.items()===c),e.on("selectItems.dt.DT",function(e,t,l){s.active(l===c)})}}}),p.type("select-checkbox",{className:"dt-select",detect:p.versionCheck("2.1")?{oneOf:function(){return!1},allOf:function(){return!1},init:function(e,t,l){return b(t)}}:function(e){return"select-checkbox"===e&&e},order:{pre:function(e){return"X"===e?-1:0}}}),m.extend(!0,p.defaults.oLanguage,{select:{aria:{rowCheckbox:"Select row"}}}),p.render.select=function(e,t){function l(e,t,l,s){var c=s.settings.aoData[s.row],n=c._select_selected,o=s.settings.oLanguage.select.aria.rowCheckbox,a=s.settings._select.selectable;return"display"!==t?"type"===t?"select-checkbox":"filter"!==t&&n?"X":"":a&&!1===a(l,c.nTr,s.row)?"":m("<input>").attr({"aria-label":o,class:v(),name:r?r(l):null,type:"checkbox",value:i?i(l):null,checked:n}).on("input",function(e){e.preventDefault(),this.checked=m(this).closest("tr").hasClass("selected")})[0]}var i=e?p.util.get(e):null,r=t?p.util.get(t):null;return l._name="selectCheckbox",l},p.ext.order["select-checkbox"]=function(t,e){return this.api().column(e,{order:"index"}).nodes().map(function(e){return"row"===t._select.items?m(e).parent().hasClass(t._select.className).toString():"cell"===t._select.items&&m(e).hasClass(t._select.className).toString()})},m.fn.DataTable.select=p.select,m(e).on("i18n.dt.dtSelect preInit.dt.dtSelect",function(e,t){"dt"===e.namespace&&p.select.init(new p.Api(t))}),p});