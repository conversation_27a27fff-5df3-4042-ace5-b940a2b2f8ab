/*! Bootstrap 5 integration for DataTables' Responsive
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var o,t;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-responsive"],function(e){return n(e,window,document)}):"object"==typeof exports?(o=require("jquery"),t=function(e,d){d.fn.dataTable||require("datatables.net-bs5")(e,d),d.fn.dataTable.Responsive||require("datatables.net-responsive")(e,d)},"undefined"==typeof window?module.exports=function(e,d){return e=e||window,d=d||o(e),t(e,d),n(d,e,e.document)}:(t(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(r,e,l){"use strict";var u,d=r.fn.dataTable,n=d.Responsive.display,f=n.modal,p=r('<div class="modal fade dtr-bs-modal" role="dialog"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"/></div></div></div>'),o=e.bootstrap;return d.Responsive.bootstrap=function(e){o=e},n.modal=function(s){return!u&&o.Modal&&(u=new o.Modal(p[0])),function(e,d,n,o){if(u){var t,a,i=n();if(!1===i)return!1;if(d){if(!r.contains(l,p[0])||e.index()!==p.data("dtr-row-idx"))return null;p.find("div.modal-body").empty().append(i)}else s&&s.header&&(a=(t=p.find("div.modal-header")).find("button").detach(),t.empty().append('<h4 class="modal-title">'+s.header(e)+"</h4>").append(a)),p.find("div.modal-body").empty().append(i),p.data("dtr-row-idx",e.index()).one("hidden.bs.modal",o).appendTo("body"),u.show();return!0}return f(e,d,n,o)}},d});