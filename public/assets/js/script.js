"use strict";var flg="0";function add_scroller(){menu_click();var e=document.querySelector(".navbar-content");e&&!e.SimpleBar&&new SimpleBar(e)}function menu_click(){window.innerWidth;document.querySelectorAll(".pc-navbar li").forEach(e=>{e.removeEventListener("click",function(){})});document.querySelectorAll(".pc-navbar li:not(.pc-trigger) .pc-submenu").forEach(e=>{e.style.display="none"});var e=document.querySelectorAll(".pc-navbar");e&&e.forEach(function(e){e.addEventListener("click",function(e){var t,r=e.target.closest("li.pc-hasmenu");r&&(e.stopPropagation(),(t=r).classList.contains("pc-trigger")?(t.classList.remove("pc-trigger"),window.slideUp(t.children[1],200),setTimeout(()=>{t.children[1].removeAttribute("style"),t.children[1].style.display="none"},200)):(document.querySelectorAll("li.pc-trigger").forEach(e=>{e.classList.remove("pc-trigger"),window.slideUp(e.children[1],200),setTimeout(()=>{e.children[1].removeAttribute("style"),e.children[1].style.display="none"},200)}),t.classList.add("pc-trigger"),window.slideDown(t.children[1],200)))})}),document.querySelectorAll(".pc-navbar > li:not(.pc-caption) li").forEach(e=>{e.addEventListener("click",function(e){var t=e.target.closest("li");t&&(e.stopPropagation(),(e=t).classList.contains("pc-hasmenu"))&&(e.classList.contains("pc-trigger")?(e.classList.remove("pc-trigger"),window.slideUp):(function(e){Array.from(e).forEach(e=>{e.classList.contains("pc-trigger")&&(e.classList.remove("pc-trigger"),window.slideUp(e.children[1],200))})}(e.parentNode.children),e.classList.add("pc-trigger"),window.slideDown))(e.children[1],200)})})}function rm_menu(){var e=document.querySelector(".pc-sidebar"),t=document.querySelector(".topbar"),r=document.querySelector(".pc-sidebar .pc-menu-overlay"),o=document.querySelector(".topbar .pc-menu-overlay");e&&e.classList.remove("mob-sidebar-active"),t&&t.classList.remove("mob-sidebar-active"),r&&r.remove(),o&&o.remove()}function remove_overlay_menu(){var e=document.querySelector(".pc-sidebar"),t=document.querySelector(".topbar"),r=document.querySelector(".pc-sidebar .pc-menu-overlay"),o=document.querySelector(".topbar .pc-menu-overlay");e&&e.classList.remove("pc-over-menu-active"),t&&t.classList.remove("mob-sidebar-active"),r&&r.remove(),o&&o.remove()}document.addEventListener("DOMContentLoaded",function(){feather.replace(),setTimeout(function(){var e=document.querySelector(".loader-bg");e&&e.remove()},400),(!document.body.hasAttribute("data-pc-layout")||"horizontal"!==document.body.getAttribute("data-pc-layout")||window.innerWidth<=1024)&&add_scroller();var e=document.querySelector(".hamburger"),t=(e&&!e.classList.contains("is-active")&&e.addEventListener("click",function(){e.classList.toggle("is-active")}),document.querySelector("#overlay-menu")),t=(t&&t.addEventListener("click",function(){var e=document.querySelector(".pc-sidebar");menu_click(),e.classList.contains("pc-over-menu-active")?remove_overlay_menu():(e.classList.add("pc-over-menu-active"),document.querySelector(".pc-menu-overlay")||(e.insertAdjacentHTML("beforeend",'<div class="pc-menu-overlay"></div>'),document.querySelector(".pc-menu-overlay").addEventListener("click",function(){remove_overlay_menu(),document.querySelector(".hamburger").classList.remove("is-active")})))}),document.querySelector("#mobile-collapse")),t=(t&&t.addEventListener("click",function(){var e=document.querySelector(".pc-sidebar");e&&(e.classList.contains("mob-sidebar-active")?rm_menu():(e.classList.add("mob-sidebar-active"),document.querySelector(".pc-menu-overlay")||(e.insertAdjacentHTML("beforeend",'<div class="pc-menu-overlay"></div>'),document.querySelector(".pc-menu-overlay").addEventListener("click",function(){rm_menu()}))))}),document.querySelectorAll(".pc-horizontal .topbar .pc-navbar > li > a"));function r(e){e=document.querySelector(e);e&&new SimpleBar(e)}t.length&&t.forEach(e=>{e.addEventListener("click",function(e){var t=e.target;setTimeout(function(){var e=t.parentNode.children[1];e&&e.removeAttribute("style")},1e3)})}),(t=document.querySelectorAll(".pc-horizontal .topbar .pc-navbar > li > a"))&&t.forEach(e=>{e.addEventListener("click",function(e){var t=e.target;setTimeout(function(){t.parentNode.children[1].removeAttribute("style")},1e3)})}),r(".profile-notification-scroll"),r(".header-notification-scroll");t=document.querySelector(".component-list-card .card-body"),t&&new SimpleBar(t),t=document.querySelector("#sidebar-hide");const o=document.querySelector(".pc-sidebar");t&&o&&t.addEventListener("click",()=>{o.classList.toggle("pc-sidebar-hide")});t=document.querySelector(".trig-drp-search");t&&t.addEventListener("shown.bs.dropdown",()=>{var e=document.querySelector(".drp-search input");e&&e.focus()})}),window.addEventListener("load",function(){[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(e){return new bootstrap.Tooltip(e)}),[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map(function(e){return new bootstrap.Popover(e)}),[].slice.call(document.querySelectorAll(".toast")).map(function(e){return new bootstrap.Toast(e)})});for(var elem=document.querySelectorAll(".pc-sidebar .pc-navbar a"),l=0;l<elem.length;l++){var pageUrl=window.location.href.split(/[?#]/)[0];elem[l].href==pageUrl&&""!=elem[l].getAttribute("href")&&(elem[l].parentNode.classList.add("active"),elem[l].parentNode.parentNode.parentNode.classList.add("pc-trigger"),elem[l].parentNode.parentNode.parentNode.classList.add("active"),elem[l].parentNode.parentNode.style.display="block",elem[l].parentNode.parentNode.parentNode.parentNode.parentNode.classList.add("pc-trigger"),elem[l].parentNode.parentNode.parentNode.parentNode.style.display="block")}for(var likeInputs=document.querySelectorAll(".prod-likes .form-check-input"),tc=(likeInputs.forEach(function(e){e.addEventListener("change",function(e){var t=e.target.parentNode;e.target.checked?(t.insertAdjacentHTML("beforeend",`<div class="pc-like">
          <div class="like-wrapper">
            <span>
              <span class="pc-group">
                <span class="pc-dots"></span>
                <span class="pc-dots"></span>
                <span class="pc-dots"></span>
                <span class="pc-dots"></span>
              </span>
            </span>
          </div>
        </div>`),t.querySelector(".pc-like").classList.add("pc-like-animate"),setTimeout(function(){var e=t.querySelector(".pc-like");e&&e.remove()},3e3)):(e=t.querySelector(".pc-like"))&&e.remove()})}),document.querySelectorAll(".auth-main.v2 .img-brand")),t=0;t<tc.length;t++)tc[t].setAttribute("src","../assets/images/logo-white.svg");function removeClassByPrefix(t,r){for(let e=0;e<t.classList.length;e++){var o=t.classList[e];o.startsWith(r)&&t.classList.remove(o)}}if (typeof window.slideUp === 'undefined') {
window.slideUp=(e,t=0)=>{e.style.transitionProperty="height, margin, padding",e.style.transitionDuration=t+"ms",e.style.boxSizing="border-box",e.style.height=e.offsetHeight+"px",e.offsetHeight,e.style.overflow="hidden",e.style.height=0,e.style.paddingTop=0,e.style.paddingBottom=0,e.style.marginTop=0,e.style.marginBottom=0};
window.slideDown=(e,t=0)=>{e.style.removeProperty("display");let r=window.getComputedStyle(e).display;"none"===r&&(r="block"),e.style.display=r;var o=e.offsetHeight;e.style.overflow="hidden",e.style.height=0,e.style.paddingTop=0,e.style.paddingBottom=0,e.style.marginTop=0,e.style.marginBottom=0,e.offsetHeight,e.style.boxSizing="border-box",e.style.transitionProperty="height, margin, padding",e.style.transitionDuration=t+"ms",e.style.height=o+"px",e.style.removeProperty("padding-top"),e.style.removeProperty("padding-bottom"),e.style.removeProperty("margin-top"),e.style.removeProperty("margin-bottom"),window.setTimeout(()=>{e.style.removeProperty("height"),e.style.removeProperty("overflow"),e.style.removeProperty("transition-duration"),e.style.removeProperty("transition-property")},t)};
window.slideToggle=(e,t=0)=>("none"===window.getComputedStyle(e).display?window.slideDown:window.slideUp)(e,t);
}
