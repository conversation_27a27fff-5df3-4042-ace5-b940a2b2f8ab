"use strict";
document.addEventListener("DOMContentLoaded", function() {
	setTimeout(function() {
		new ApexCharts(document.querySelector("#overview-chart-1"), {
			chart: {
				type: "line",
				height: 230,
				toolbar: {
					show: !1
				}
			},
			colors: ["#0d6efd", "#748892"],
			dataLabels: {
				enabled: !1
			},
			stroke: {
				width: 2
			},
			plotOptions: {
				bar: {
					columnWidth: "45%",
					borderRadius: 4
				}
			},
			grid: {
				strokeDashArray: 4
			},
			series: [{
				name: "This month",
				data: [30, 60, 40, 70, 50, 90, 50, 55, 45, 60, 50, 65]
			}, {
				name: "Last month",
				data: [50, 55, 45, 60, 50, 65, 30, 60, 40, 70, 50, 90]
			}],
			xaxis: {
				labels: {
					hideOverlappingLabels: !0
				},
				axisBorder: {
					show: !1
				},
				axisTicks: {
					show: !1
				}
			}
		}).render();
		new ApexCharts(document.querySelector("#overview-chart-2"), {
			chart: {
				type: "line",
				height: 230,
				toolbar: {
					show: !1
				}
			},
			colors: ["#0d6efd", "#748892"],
			dataLabels: {
				enabled: !1
			},
			stroke: {
				width: 2
			},
			plotOptions: {
				bar: {
					columnWidth: "45%",
					borderRadius: 4
				}
			},
			grid: {
				strokeDashArray: 4
			},
			series: [{
				name: "This month",
				data: [70, 50, 90, 50, 55, 45, 30, 60, 40, 60, 50, 65]
			}, {
				name: "Last month",
				data: [50, 65, 30, 60, 40, 50, 55, 45, 60, 70, 50, 90]
			}],
			xaxis: {
				labels: {
					hideOverlappingLabels: !0
				},
				axisBorder: {
					show: !1
				},
				axisTicks: {
					show: !1
				}
			}
		}).render()
	}, 500)
});