"use strict";document.addEventListener("DOMContentLoaded",function(){setTimeout(function(){new ApexCharts(document.querySelector("#yearly-summary-chart"),{chart:{height:250,type:"bar",toolbar:{show:!1}},plotOptions:{bar:{horizontal:!1,columnWidth:"75%",borderRadius:2,borderRadiusApplication:"end"}},legend:{show:!0,position:"bottom"},dataLabels:{enabled:!1},colors:["#1DE9B6","#0398F2"],stroke:{show:!0,width:1,colors:["transparent"]},fill:{type:"gradient",gradient:{type:"vertical",stops:[0,100],shadeIntensity:.5,gradientToColors:["#1DC4E9","#38B9E7"]}},grid:{strokeDashArray:4},series:[{name:"Net Profit",data:[76,85,101,98,87]},{name:"Revenue",data:[44,55,57,56,61]}],xaxis:{categories:["Feb","Mar","Apr","May","Jun"]},tooltip:{y:{formatter:function(t){return"$ "+t}}}}).render()},500)});