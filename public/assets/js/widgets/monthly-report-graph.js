"use strict";document.addEventListener("DOMContentLoaded",function(){setTimeout(function(){new ApexCharts(document.querySelector("#monthly-report-graph"),{series:[{name:"Deal<PERSON>",data:[44,55,41,67,52,53,13]},{name:"Income Report",data:[13,3,20,8,13,27,21]},{name:"Customer",data:[11,17,15,15,21,14,11]},{name:"Profits",data:[21,7,25,13,22,3,44]}],chart:{type:"bar",height:250,stacked:!0,toolbar:{show:!1}},colors:["#04A9F5","#04A9F5","#04A9F5","#7C57C1"],fill:{opacity:[.6,1,.6,1]},grid:{strokeDashArray:4},dataLabels:{enabled:!1},plotOptions:{bar:{horizontal:!1}},xaxis:{categories:["Mon","<PERSON><PERSON>","<PERSON>","Thu","Fri","Sat","Sun"]},legend:{show:!1}}).render()},500)});