"use strict";!function(){document.getElementsByTagName("body")[0].setAttribute("data-pc-layout","horizontal");const e=document.querySelector(".pc-navbar").innerHTML;var n=window.innerWidth;function t(){var c=document.querySelectorAll(".pc-navbar > li.pc-item"),a="",s="",i="",r="",l=0,o=!1,p=!1,t=(c.forEach(function(e,n){var t;e.classList.contains("pc-caption")?(!0===p&&r.insertAdjacentHTML&&(t="",r.children[1]&&(t='<span class="pc-micon">'+r.children[1].outerHTML+"</span>"),r.insertAdjacentHTML("afterend",'<li class="pc-item pc-hasmenu">                <a href="#!" class="pc-link ">'+t+'<span class="pc-mtext">'+r.children[0].innerHTML+'</span>                  <span class="pc-arrow"><i data-feather="chevron-right"></i></span>                </a>                <ul class="pc-submenu">'+a+"                </ul>            </li>"),r.remove()),o=!(p=!(a="")),r=e,c[n+1].classList.contains("pc-caption")&&(r.remove(),r=i=s=a="",l=0,p=o=!1)):(!1===o&&(s=i,o=!0),a+=e.outerHTML,n+1===c.length&&(!0===p&&r.insertAdjacentHTML&&(r.insertAdjacentHTML("afterend",'<li class="pc-item pc-hasmenu">                            <a href="#!" class="pc-link ">                                <span class="pc-micon">'+r.children[1].outerHTML+'</span>                                <span class="pc-mtext">'+r.children[0].innerHTML+'</span>                                <span class="pc-arrow"><i data-feather="chevron-right"></i></span>                            </a>                            <ul class="pc-submenu">'+a+"                            </ul>                        </li>"),r.remove()),o=!(p=!(a="")),r=e),!0===p&&e.remove())}),document.querySelectorAll(".pc-navbar > li.pc-item"));t.forEach(function(e,n){(l+=e.getBoundingClientRect().width+49)>window.innerWidth?(!1===o&&(s=i,o=!0),!0===o&&(a+=e.outerHTML,e.remove())):i=e,n+1===t.length&&s.insertAdjacentHTML&&s.insertAdjacentHTML("afterend",'<li class="pc-item pc-hasmenu">                          <a href="#!" class="pc-link ">                              <span class="pc-micon"><svg class="pc-icon"><use xlink:href="#custom-clipboard"></use></svg></span>                              <span class="pc-mtext">Other</span>                              <span class="pc-arrow"><i data-feather="chevron-right"></i></span>                          </a>                          <ul class="pc-submenu">'+a+"                          </ul>                      </li>")}),(t=document.querySelectorAll(".pc-navbar .pc-trigger")).forEach(function(e){e.classList.remove("pc-trigger"),e.children[1].removeAttribute("style"),e.classList.contains("active")&&e.classList.remove("active")})}function c(){for(var e=document.querySelectorAll(".pc-sidebar .pc-navbar .pc-hasmenu"),n=0;n<e.length;n++)e[n].addEventListener("mouseenter",function(e){var n,t,c,a,s=window.innerHeight,i=window.innerWidth;1024<i&&(e=e.target.children[1],c=e.getBoundingClientRect(),a=c.left,n=c.top,t=c.width,c=c.height,a+t<=i||e.classList.add("edge"),n+c<=s||(e.classList.add("edge-alt"),c<=n)||(e.classList.add("edge-alt-full"),a="top: 100%; bottom: -"+(s-n-140)+"px",e.setAttribute("style",a)))},function(e){e.children[1].classList.remove("edge"),e.children[1].classList.remove("edge-alt")})}1024<n&&(t(),c()),window.addEventListener("resize",function(){1024<n&&(document.querySelector(".pc-navbar").innerHTML=e,t(),feather.replace(),c())})}();