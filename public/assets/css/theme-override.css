/* ==========================================================================
   LIGHT ABLE THEME OVERRIDES
   Custom CSS overrides for Light Able Bootstrap template
   ========================================================================== */

/* ==========================================================================
   CSS CUSTOM PROPERTIES (VARIABLES) OVERRIDES
   ========================================================================== */

:root {
  /* Box Shadow Overrides */
  --pc-card-box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08) !important;
  --pc-sidebar-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06) !important;
  --pc-header-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05) !important;
  
  /* Bootstrap Shadow Overrides */
  --bs-box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1) !important;
  --bs-box-shadow-sm: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05) !important;
  --bs-box-shadow-lg: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.12) !important;
  
  /* Color Overrides */
  --pc-primary: #007bff !important;
  --bs-primary: #007bff !important;
  --pc-sidebar-background: #ffffff !important;
  --pc-header-background: #f8f9fa !important;
  
  /* Typography Overrides */
  --bs-body-font-size: 0.9rem !important;
  --pc-heading-color: #2c3e50 !important;
  
  /* Border Radius Overrides */
  --bs-border-radius: 6px !important;
  --bs-border-radius-sm: 4px !important;
  --bs-border-radius-lg: 8px !important;
}

/* Dark Theme Overrides */
[data-bs-theme="dark"] {
  --pc-card-box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.25) !important;
  --pc-sidebar-shadow: 0px 4px 16px rgba(0, 0, 0, 0.2) !important;
  --pc-header-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* ==========================================================================
   COMPONENT SPECIFIC OVERRIDES
   ========================================================================== */

/* Card Overrides */
.card {
  box-shadow: var(--pc-card-box-shadow) !important;
  border-radius: var(--bs-border-radius) !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
}

.card-header {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06) !important;
}

/* Button Overrides */
.btn {
  border-radius: var(--bs-border-radius-sm) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
}

.btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
}

/* Sidebar Overrides */
.pc-sidebar {
  box-shadow: var(--pc-sidebar-shadow) !important;
  background: var(--pc-sidebar-background) !important;
}

/* Header Overrides */
.pc-header {
  box-shadow: var(--pc-header-shadow) !important;
  background: var(--pc-header-background) !important;
  backdrop-filter: blur(10px) !important;
}

/* Modal Overrides */
.modal-content {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
  border-radius: var(--bs-border-radius-lg) !important;
  border: none !important;
}

/* Table Overrides */
.table {
  box-shadow: var(--bs-box-shadow-sm) !important;
  border-radius: var(--bs-border-radius) !important;
  overflow: hidden !important;
}

/* Form Overrides */
.form-control {
  border-radius: var(--bs-border-radius-sm) !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.form-control:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* ==========================================================================
   UTILITY CLASS OVERRIDES
   ========================================================================== */

/* Custom Shadow Utilities */
.shadow-custom {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.shadow-custom-lg {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.shadow-subtle {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06) !important;
}

/* Remove all shadows */
.no-shadow {
  box-shadow: none !important;
}

/* ==========================================================================
   RESPONSIVE OVERRIDES
   ========================================================================== */

@media (max-width: 768px) {
  :root {
    --pc-card-box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.06) !important;
    --pc-sidebar-shadow: 0px 1px 4px rgba(0, 0, 0, 0.04) !important;
  }
}

/* ==========================================================================
   ANIMATION OVERRIDES
   ========================================================================== */

/* Smooth transitions for shadow changes */
.card,
.btn,
.modal-content,
.form-control {
  transition: box-shadow 0.3s ease, transform 0.3s ease !important;
}

/* ==========================================================================
   DISABLE HOVER ANIMATIONS GLOBALLY
   ========================================================================== */

/* Remove all card hover animations globally */
.card:hover {
  transform: none !important;
  box-shadow: var(--pc-card-box-shadow) !important;
}

/* Remove specific commission card hover animations globally */
.commission-stats-card:hover {
  transform: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.approval-stats-card:hover {
  transform: none !important;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.1) !important;
}

.approval-user-card:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Remove button hover animations globally */
.btn:hover {
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
}

.commission-btn:hover {
  transform: none !important;
}

.approval-action-btn:hover {
  transform: none !important;
}

/* Remove table row hover animations globally */
.table tbody tr:hover {
  transform: none !important;
  box-shadow: none !important;
}

.commission-table .table tbody tr:hover {
  transform: none !important;
  box-shadow: none !important;
}

.approval-table .table tbody tr:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Remove avatar hover animations globally */
.avtar:hover {
  transform: none !important;
}

/* Remove change item hover animations globally */
.approval-change-item:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* ==========================================================================
   DISABLE CARD SHADOWS GLOBALLY
   ========================================================================== */

/* Remove all card shadows globally */
.card {
  box-shadow: none !important;
}

/* Remove specific commission card shadows globally */
.commission-stats-card {
  box-shadow: none !important;
}

.approval-stats-card {
  box-shadow: none !important;
}

.commission-form-card {
  box-shadow: none !important;
}

.commission-table {
  box-shadow: none !important;
}

.approval-table {
  box-shadow: none !important;
}

/* Remove modal shadows globally */
.modal-content {
  box-shadow: none !important;
}

.commission-modal .modal-content {
  box-shadow: none !important;
}

.approval-modal .modal-content {
  box-shadow: none !important;
}

/* Remove button shadows globally */
.btn {
  box-shadow: none !important;
}

.commission-btn {
  box-shadow: none !important;
}

.approval-action-btn {
  box-shadow: none !important;
}

/* Remove table shadows globally */
.table {
  box-shadow: none !important;
}

/* Remove form control shadows globally */
.form-control {
  box-shadow: none !important;
}

.form-control:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Remove commission alert shadows globally */
.commission-alert {
  box-shadow: none !important;
}

.approval-alert {
  box-shadow: none !important;
}

/* Remove avatar icon shadows globally */
.commission-stats-icon {
  box-shadow: none !important;
}

.approval-stats-icon {
  box-shadow: none !important;
}

.approval-user-avatar {
  box-shadow: none !important;
}

.approval-empty-state .approval-empty-icon {
  box-shadow: none !important;
}

/* Remove utility shadow classes globally */
.shadow-custom,
.shadow-custom-lg,
.shadow-subtle {
  box-shadow: none !important;
}

/* Optional: Enable shadows with a specific class */
.enable-shadows .card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.enable-shadows .commission-stats-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.enable-shadows .approval-stats-card {
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.1) !important;
}

.enable-shadows .btn {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
}

.enable-shadows .modal-content {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

/* Optional: Enable hover animations with a specific class */
.enable-hover-animation .card:hover {
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.12) !important;
  transform: translateY(-2px) !important;
}

.enable-hover-animation .commission-stats-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.enable-hover-animation .approval-stats-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(255, 193, 7, 0.2) !important;
}

.enable-hover-animation .btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
}

/* ==========================================================================
   CUSTOM COMPONENT STYLES
   ========================================================================== */

/* Custom card variant */
.card-elevated {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  border: none !important;
}

/* Custom sidebar style */
.sidebar-minimal {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06) !important;
}

/* Custom header style */
.header-glass {
  background: rgba(248, 249, 250, 0.8) !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

/* ==========================================================================
   COMMISSION-SPECIFIC STYLES
   ========================================================================== */

/* Commission Statistics Cards */
.commission-stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

.commission-stats-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

.commission-stats-card .card-body {
  padding: 1.5rem !important;
}

.commission-stats-icon {
  width: 48px !important;
  height: 48px !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.5rem !important;
  margin-right: 1rem !important;
}

.commission-stats-icon.primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
}

.commission-stats-icon.warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  color: #333 !important;
}

.commission-stats-icon.success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
  color: white !important;
}

/* Commission Table Enhancements */
.commission-table {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.commission-table .table {
  margin-bottom: 0 !important;
}

.commission-table .table th {
  background: #f8f9fa !important;
  border-bottom: 2px solid #e9ecef !important;
  font-weight: 600 !important;
  padding: 1rem 0.75rem !important;
  color: #495057 !important;
}

.commission-table .table td {
  padding: 1rem 0.75rem !important;
  border-bottom: 1px solid #f1f3f4 !important;
  vertical-align: middle !important;
}

.commission-table .table tbody tr:hover {
  background: #f8f9fa !important;
  transform: scale(1.002) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

/* Commission Form Enhancements */
.commission-form-card {
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
}

.commission-form-card .card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem !important;
  border-radius: 12px 12px 0 0 !important;
}

.commission-form-card .card-body {
  padding: 2rem !important;
}

/* Commission Input Groups */
.commission-input-group {
  position: relative !important;
  margin-bottom: 1.5rem !important;
}

.commission-input-group .form-label {
  font-weight: 600 !important;
  color: #495057 !important;
  margin-bottom: 0.5rem !important;
}

.commission-input-group .form-control {
  border: 2px solid #e9ecef !important;
  border-radius: 8px !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.9rem !important;
  transition: all 0.3s ease !important;
}

.commission-input-group .form-control:focus {
  border-color: #007bff !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
  transform: translateY(-1px) !important;
}

.commission-input-group .form-control.is-invalid {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.commission-input-group .input-group-text {
  background: #f8f9fa !important;
  border: 2px solid #e9ecef !important;
  border-left: none !important;
  color: #6c757d !important;
  font-weight: 500 !important;
}

/* Commission Alert Enhancements */
.commission-alert {
  border-radius: 12px !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem !important;
}

.commission-alert.alert-primary {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
  color: #0d47a1 !important;
}

.commission-alert.alert-warning {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%) !important;
  color: #e65100 !important;
}

.commission-alert.alert-success {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
  color: #2e7d32 !important;
}

.commission-alert.alert-danger {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%) !important;
  color: #c62828 !important;
}

/* Commission Badge Enhancements */
.commission-badge {
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  font-weight: 500 !important;
  font-size: 0.85rem !important;
  border: 1px solid transparent !important;
}

.commission-badge.badge-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
  color: white !important;
}

.commission-badge.badge-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  color: #333 !important;
}

.commission-badge.badge-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  color: white !important;
}

/* Commission Button Enhancements */
.commission-btn {
  border-radius: 8px !important;
  padding: 0.65rem 1.5rem !important;
  font-weight: 500 !important;
  border: none !important;
  transition: all 0.3s ease !important;
  text-transform: none !important;
}

.commission-btn:hover {
  transform: translateY(-1px) !important;
}

.commission-btn.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
}

.commission-btn.btn-primary:hover {
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4) !important;
}

.commission-btn.btn-outline-primary {
  border: 2px solid #007bff !important;
  color: #007bff !important;
  background: transparent !important;
}

.commission-btn.btn-outline-primary:hover {
  background: #007bff !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
}

/* Commission Modal Enhancements */
.commission-modal .modal-content {
  border-radius: 16px !important;
  border: none !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.commission-modal .modal-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  border-radius: 16px 16px 0 0 !important;
  padding: 1.5rem 2rem !important;
}

.commission-modal .modal-body {
  padding: 2rem !important;
}

.commission-modal .modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
}

/* Commission Avatar Enhancements */
.commission-avatar {
  width: 40px !important;
  height: 40px !important;
  border-radius: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.2rem !important;
  margin-right: 0.75rem !important;
}

.commission-avatar.bg-light-primary {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
  color: #1976d2 !important;
}

.commission-avatar.bg-light-secondary {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
  color: #6b7280 !important;
}

/* Commission Page Header */
.commission-page-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
}

.commission-page-header h2 {
  color: #2c3e50 !important;
  font-weight: 700 !important;
  margin-bottom: 0 !important;
}

/* Commission Empty State */
.commission-empty-state {
  text-align: center !important;
  padding: 3rem 2rem !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border-radius: 12px !important;
  border: 2px dashed #dee2e6 !important;
}

.commission-empty-state .commission-empty-icon {
  width: 80px !important;
  height: 80px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 2rem !important;
  margin: 0 auto 1.5rem !important;
  color: #6c757d !important;
}

.commission-empty-state h5 {
  color: #495057 !important;
  font-weight: 600 !important;
  margin-bottom: 1rem !important;
}

.commission-empty-state p {
  color: #6c757d !important;
  margin-bottom: 2rem !important;
}

/* Commission Action Buttons */
.commission-action-group {
  display: flex !important;
  gap: 0.5rem !important;
  justify-content: center !important;
}

/* Commission Filter Panel */
.commission-filter-panel {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
}

.commission-filter-panel .form-label {
  font-weight: 600 !important;
  color: #495057 !important;
  margin-bottom: 0.5rem !important;
}

.commission-filter-panel .form-control,
.commission-filter-panel .form-select {
  border: 2px solid #e9ecef !important;
  border-radius: 8px !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.9rem !important;
  transition: all 0.3s ease !important;
}

.commission-filter-panel .form-control:focus,
.commission-filter-panel .form-select:focus {
  border-color: #007bff !important;
  outline: none !important;
}

.commission-filter-panel .input-group-text {
  background: #f8f9fa !important;
  border: 2px solid #e9ecef !important;
  border-left: none !important;
  border-right: none !important;
  color: #6c757d !important;
  font-weight: 500 !important;
}

/* Commission Table Sortable Headers */
.commission-sortable {
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.commission-sortable:hover {
  background: #f8f9fa !important;
  color: #007bff !important;
}

.commission-sortable.active {
  background: #e7f3ff !important;
  color: #0056b3 !important;
}

/* Livewire Loading States */
.commission-btn[wire\\:loading] {
  opacity: 0.6 !important;
  pointer-events: none !important;
}

.commission-btn .spinner-border {
  width: 1rem !important;
  height: 1rem !important;
}

/* Commission Modal Loading */
.commission-modal .spinner-border-sm {
  width: 0.875rem !important;
  height: 0.875rem !important;
}

/* Commission Stats Icons Enhancement */
.commission-stats-icon.info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  color: white !important;
}

.commission-action-btn {
  border-radius: 6px !important;
  padding: 0.4rem 0.8rem !important;
  font-size: 0.85rem !important;
  border: 1px solid transparent !important;
  transition: all 0.3s ease !important;
}

.commission-action-btn:hover {
  transform: translateY(-1px) !important;
}

.commission-action-btn.btn-outline-primary:hover {
  background: #007bff !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3) !important;
}

.commission-action-btn.btn-outline-info:hover {
  background: #17a2b8 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(23, 162, 184, 0.3) !important;
}

.commission-action-btn.btn-outline-danger:hover {
  background: #dc3545 !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3) !important;
}

/* ==========================================================================
   APPROVAL-SPECIFIC STYLES
   ========================================================================== */

/* Approval Statistics */
.approval-stats-card {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%) !important;
  border: 1px solid rgba(255, 193, 7, 0.2) !important;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.1) !important;
  transition: all 0.3s ease !important;
  border-radius: 12px !important;
}

.approval-stats-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(255, 193, 7, 0.2) !important;
}

.approval-stats-card .card-body {
  padding: 1.5rem !important;
}

.approval-stats-icon {
  width: 56px !important;
  height: 56px !important;
  border-radius: 14px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.75rem !important;
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3) !important;
}

/* Approval Table Enhancements */
.approval-table {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.approval-table .table th {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%) !important;
  border-bottom: 2px solid #ffc107 !important;
  font-weight: 600 !important;
  padding: 1rem 0.75rem !important;
  color: #e65100 !important;
}

.approval-table .table td {
  padding: 1rem 0.75rem !important;
  border-bottom: 1px solid #f5f5f5 !important;
  vertical-align: middle !important;
}

.approval-table .table tbody tr:hover {
  background: #fffbf0 !important;
  transform: scale(1.002) !important;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.1) !important;
}

/* Approval Change Comparison */
.approval-change-item {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%) !important;
  border: 1px solid rgba(255, 152, 0, 0.2) !important;
  border-radius: 8px !important;
  padding: 0.75rem !important;
  margin-bottom: 0.5rem !important;
  transition: all 0.3s ease !important;
}

.approval-change-item:hover {
  transform: translateX(4px) !important;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15) !important;
}

.approval-change-item .change-label {
  font-weight: 600 !important;
  color: #e65100 !important;
  margin-bottom: 0.25rem !important;
}

.approval-change-item .change-value {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.approval-change-item .current-value {
  background: #f5f5f5 !important;
  color: #666 !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 4px !important;
  font-size: 0.85rem !important;
}

.approval-change-item .new-value {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
  color: white !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 4px !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
}

.approval-change-item .change-arrow {
  color: #ff9800 !important;
  font-weight: bold !important;
}

/* Approval User Card */
.approval-user-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%) !important;
  border: 1px solid rgba(156, 39, 176, 0.2) !important;
  border-radius: 12px !important;
  padding: 1rem !important;
  transition: all 0.3s ease !important;
}

.approval-user-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.15) !important;
}

.approval-user-avatar {
  width: 48px !important;
  height: 48px !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.2rem !important;
  font-weight: 600 !important;
  margin-right: 0.75rem !important;
}

.approval-user-info h6 {
  margin-bottom: 0.25rem !important;
  color: #4a148c !important;
  font-weight: 600 !important;
}

.approval-user-info p {
  margin-bottom: 0 !important;
  color: #7b1fa2 !important;
  font-size: 0.85rem !important;
}

/* Approval Action Buttons */
.approval-action-group {
  display: flex !important;
  gap: 0.5rem !important;
  justify-content: flex-end !important;
}

.approval-action-btn {
  border-radius: 8px !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  border: none !important;
  transition: all 0.3s ease !important;
  font-size: 0.9rem !important;
}

.approval-action-btn:hover {
  transform: translateY(-1px) !important;
}

.approval-action-btn.btn-success {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
}

.approval-action-btn.btn-success:hover {
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;
}

.approval-action-btn.btn-danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3) !important;
}

.approval-action-btn.btn-danger:hover {
  box-shadow: 0 6px 16px rgba(244, 67, 54, 0.4) !important;
}

/* Approval Status Badge */
.approval-status-badge {
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  font-weight: 500 !important;
  font-size: 0.85rem !important;
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
  color: white !important;
  border: 1px solid rgba(255, 152, 0, 0.3) !important;
  animation: pulse 2s infinite !important;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(255, 152, 0, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0); }
}

/* Approval Date Card */
.approval-date-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
  border: 1px solid rgba(76, 175, 80, 0.2) !important;
  border-radius: 8px !important;
  padding: 0.75rem !important;
  text-align: center !important;
}

.approval-date-card h6 {
  color: #2e7d32 !important;
  font-weight: 600 !important;
  margin-bottom: 0.25rem !important;
}

.approval-date-card p {
  color: #4caf50 !important;
  font-size: 0.85rem !important;
  margin-bottom: 0 !important;
}

/* Approval Modal Enhancements */
.approval-modal .modal-content {
  border-radius: 16px !important;
  border: none !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.approval-modal .modal-header.bg-success {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
  border-radius: 16px 16px 0 0 !important;
  padding: 1.5rem 2rem !important;
}

.approval-modal .modal-header.bg-danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
  border-radius: 16px 16px 0 0 !important;
  padding: 1.5rem 2rem !important;
}

.approval-modal .modal-body {
  padding: 2rem !important;
}

.approval-modal .modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
}

.approval-modal .form-control {
  border: 2px solid #e9ecef !important;
  border-radius: 8px !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.3s ease !important;
}

.approval-modal .form-control:focus {
  border-color: #f44336 !important;
  box-shadow: 0 0 0 0.2rem rgba(244, 67, 54, 0.25) !important;
}

/* Approval Empty State */
.approval-empty-state {
  text-align: center !important;
  padding: 4rem 2rem !important;
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
  border-radius: 16px !important;
  border: 2px solid rgba(76, 175, 80, 0.2) !important;
}

.approval-empty-state .approval-empty-icon {
  width: 100px !important;
  height: 100px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
  color: white !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 3rem !important;
  margin: 0 auto 2rem !important;
  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3) !important;
}

.approval-empty-state h4 {
  color: #2e7d32 !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
}

.approval-empty-state p {
  color: #4caf50 !important;
  font-size: 1.1rem !important;
  margin-bottom: 2rem !important;
}

/* Approval Alert Enhancement */
.approval-alert {
  border-radius: 12px !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem !important;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
  border: 1px solid rgba(33, 150, 243, 0.2) !important;
}

.approval-alert .alert-heading {
  color: #0d47a1 !important;
  font-weight: 600 !important;
}

.approval-alert p {
  color: #1565c0 !important;
  margin-bottom: 0 !important;
}

/* Approval Variety Badge */
.approval-variety-badge {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
  color: white !important;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  font-weight: 500 !important;
  font-size: 0.85rem !important;
  border: 1px solid rgba(33, 150, 243, 0.3) !important;
}

/* Responsive Design for Approval Page */
@media (max-width: 768px) {
  .approval-stats-card {
    margin-bottom: 1rem !important;
  }
  
  .approval-table .table th,
  .approval-table .table td {
    padding: 0.75rem 0.5rem !important;
    font-size: 0.85rem !important;
  }
  
  .approval-action-group {
    flex-direction: column !important;
    gap: 0.25rem !important;
  }
  
  .approval-action-btn {
    width: 100% !important;
    padding: 0.75rem !important;
  }
  
  .approval-change-item {
    margin-bottom: 0.75rem !important;
  }
  
  .approval-user-card {
    padding: 0.75rem !important;
  }
  
  .approval-user-avatar {
    width: 40px !important;
    height: 40px !important;
    font-size: 1rem !important;
  }
}