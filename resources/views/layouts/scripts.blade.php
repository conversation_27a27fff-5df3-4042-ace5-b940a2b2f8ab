<script src="{{asset('assets/js/plugins/popper.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/simplebar.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/bootstrap.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/i18next.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/i18nextHttpBackend.min.js')}}"></script>
<script src="{{asset('assets/js/script.js')}}"></script>
<script src="{{asset('assets/js/theme.js')}}"></script>
<script src="{{asset('assets/js/multi-lang.js')}}"></script>
<script src="{{asset('assets/js/plugins/feather.min.js')}}"></script>
<script>layout_change('light');</script>
<script>layout_sidebar_change('dark');</script>
<script>change_box_container('false');</script>
<script>layout_caption_change('true');</script>
<script>layout_rtl_change('false');</script>
<script>preset_change('preset-1');</script>

@stack('scripts')
<script>
   // Copy to clipboard functionality for Livewire
   window.addEventListener('copy-to-clipboard', event => {
      if (navigator.clipboard) {
         navigator.clipboard.writeText(event.detail).then(function() {
            // Simple alert for success
            alert('Link copied to clipboard!');
         }).catch(function() {
            // Fallback for older browsers
            alert('Link copied: ' + event.detail);
         });
      } else {
         // Fallback for older browsers
         alert('Link: ' + event.detail);
      }
   });

   // Handle redirect to login for password creation
   window.addEventListener('redirect-to-login', event => {
      setTimeout(() => {
         window.location.href = '/login';
      }, 3000);
   });

   // Ensure slideUp and slideDown functions are available
   if (typeof window.slideUp === 'undefined') {
      window.slideUp = (e, t = 0) => {
         e.style.transitionProperty = "height, margin, padding";
         e.style.transitionDuration = t + "ms";
         e.style.boxSizing = "border-box";
         e.style.height = e.offsetHeight + "px";
         e.offsetHeight;
         e.style.overflow = "hidden";
         e.style.height = 0;
         e.style.paddingTop = 0;
         e.style.paddingBottom = 0;
         e.style.marginTop = 0;
         e.style.marginBottom = 0;
         setTimeout(() => {
            e.style.display = "none";
            e.style.removeProperty("height");
            e.style.removeProperty("padding-top");
            e.style.removeProperty("padding-bottom");
            e.style.removeProperty("margin-top");
            e.style.removeProperty("margin-bottom");
            e.style.removeProperty("overflow");
            e.style.removeProperty("transition-duration");
            e.style.removeProperty("transition-property");
         }, t);
      };

      window.slideDown = (e, t = 0) => {
         e.style.removeProperty("display");
         let display = window.getComputedStyle(e).display;
         if (display === "none") display = "block";
         e.style.display = display;
         const height = e.offsetHeight;
         e.style.overflow = "hidden";
         e.style.height = 0;
         e.style.paddingTop = 0;
         e.style.paddingBottom = 0;
         e.style.marginTop = 0;
         e.style.marginBottom = 0;
         e.offsetHeight;
         e.style.boxSizing = "border-box";
         e.style.transitionProperty = "height, margin, padding";
         e.style.transitionDuration = t + "ms";
         e.style.height = height + "px";
         e.style.removeProperty("padding-top");
         e.style.removeProperty("padding-bottom");
         e.style.removeProperty("margin-top");
         e.style.removeProperty("margin-bottom");
         setTimeout(() => {
            e.style.removeProperty("height");
            e.style.removeProperty("overflow");
            e.style.removeProperty("transition-duration");
            e.style.removeProperty("transition-property");
         }, t);
      };
   }

   // Custom menu click handler for sidebar dropdowns
   function customMenuClick() {
      // Remove existing event listeners first
      document.querySelectorAll(".pc-navbar li.pc-hasmenu").forEach(item => {
         item.removeEventListener("click", handleDropdownClick);
      });

      // Add fresh event listeners
      document.querySelectorAll(".pc-navbar li.pc-hasmenu").forEach(item => {
         item.addEventListener("click", handleDropdownClick);
      });
   }

   function handleDropdownClick(e) {
      e.preventDefault();
      e.stopPropagation();

      const menuItem = e.currentTarget;
      const submenu = menuItem.querySelector('.pc-submenu');

      if (!submenu) return;

      if (menuItem.classList.contains('pc-trigger')) {
         // Close dropdown
         menuItem.classList.remove('pc-trigger');
         window.slideUp(submenu, 200);
      } else {
         // Close other open dropdowns first
         document.querySelectorAll('.pc-navbar li.pc-trigger').forEach(openItem => {
            if (openItem !== menuItem) {
               openItem.classList.remove('pc-trigger');
               const openSubmenu = openItem.querySelector('.pc-submenu');
               if (openSubmenu) {
                  window.slideUp(openSubmenu, 200);
               }
            }
         });

         // Open this dropdown
         menuItem.classList.add('pc-trigger');
         window.slideDown(submenu, 200);
      }
   }

   // Fix sidebar dropdown initialization for Livewire
   function initializeSidebar() {
      // Re-initialize Feather icons
      if (typeof feather !== 'undefined') {
         feather.replace();
      }

      // Initialize custom menu click handling
      customMenuClick();

      // Re-initialize Bootstrap dropdowns (for header dropdowns, not sidebar)
      var dropdownElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));
      dropdownElementList.map(function (dropdownToggleEl) {
         return new bootstrap.Dropdown(dropdownToggleEl);
      });

      // Re-initialize tooltips
      var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      tooltipTriggerList.map(function (tooltipTriggerEl) {
         return new bootstrap.Tooltip(tooltipTriggerEl);
      });
   }

   // Initialize sidebar on Livewire load
   document.addEventListener('livewire:loaded', function () {
      setTimeout(function() {
         initializeSidebar();
      }, 100);
   });

   // Re-initialize sidebar after Livewire navigation
   document.addEventListener('livewire:navigated', function () {
      setTimeout(function() {
         initializeSidebar();
      }, 100);
   });

   // Re-initialize sidebar after Livewire component updates
   Livewire.hook('morph.updated', ({ el, component }) => {
      setTimeout(function() {
         initializeSidebar();
      }, 50);
   });

   // Fallback initialization for first page load
   window.addEventListener('load', function() {
      setTimeout(function() {
         initializeSidebar();
      }, 200);
   });

   // Additional fallback for immediate initialization
   document.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
         initializeSidebar();
      }, 500);
   });

   // Handle refresh after login redirect
//    @if(session()->has('refresh_after_redirect'))
//       // Refresh the page after a short delay to ensure proper loading
//       setTimeout(function() {
//          window.location.reload();
//       }, 100);
//    @endif
</script>
<!-- [Livewire Scripts] -->
@livewireScripts




