@push('styles')
    <style>
        .modal.show {
            display: block !important;
        }

        /* Statistics Cards - Equal Height */
        .stats-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .stats-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .stats-card .row {
            align-items: center;
            height: 100%;
        }

        .stats-card h3 {
            margin-bottom: 0.5rem;
        }

        .stats-card h6 {
            margin-bottom: 0;
            line-height: 1.2;
        }

        /* View Modal Styles */
        .info-group {
            margin-bottom: 1rem;
        }

        .info-label {
            display: block;
            font-weight: 600;
            color: #495057;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            color: #212529;
            font-size: 1rem;
            line-height: 1.5;
        }

        .content-box {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 200px;
            overflow-y: auto;
        }

        .file-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .file-info i {
            font-size: 1.5rem;
            margin-right: 0.75rem;
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-weight: 500;
            color: #212529;
            margin-bottom: 0.25rem;
        }

        .file-size {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .modal-header .modal-title {
            display: flex;
            align-items: center;
            font-weight: 600;
        }

        .modal-header .modal-title i {
            font-size: 1.25rem;
        }
    </style>
@endpush

<div>

    {{-- Page Header --}}
    <div class="page-header commission-page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Agreement Management</h2>
                        <p class="text-muted mb-0 mt-2">Manage terms and conditions agreements for different user roles with PDF file attachments</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('success') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="mb-4 row">
        <div class="col-sm-6 col-xl-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h3 class="text-primary">{{ $statistics['total'] }}</h3>
                            <h6 class="text-muted m-b-0">Total Agreements</h6>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="ph-duotone ph-file-text text-primary" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h3 class="text-success">{{ $statistics['active'] }}</h3>
                            <h6 class="text-muted m-b-0">Active Agreements</h6>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="ph-duotone ph-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h3 class="text-warning">{{ $statistics['inactive'] }}</h3>
                            <h6 class="text-muted m-b-0">Inactive Agreements</h6>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="ph-duotone ph-pause-circle text-warning" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h3 class="text-info">{{ $statistics['with_files'] }}</h3>
                            <h6 class="text-muted m-b-0">With PDF Files</h6>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <i class="ph-duotone ph-paperclip text-info" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Card -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="page-header-title">
                            <h2 class="mb-0">Agreement Directory</h2>
                            @if ($agreements->total() > 0)
                                <p class="text-muted mb-0 mt-1">Showing {{ $agreements->firstItem() }} to
                                    {{ $agreements->lastItem() }} of {{ $agreements->total() }} results</p>
                            @endif
                        </div>
                        <div class="page-header-breadcrumb">
                            <button wire:click="toggleFilters" class="btn btn-outline-secondary me-2">
                                <i class="ph-duotone ph-funnel me-2"></i>
                                {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
                            </button>
                            <button type="button" class="btn btn-primary" wire:click="$dispatch('open-create-modal')">
                                <i class="ph-duotone ph-plus me-2"></i>Create Agreement
                            </button>
                        </div>
                    </div>
                </div>
                {{-- Advanced Filters Panel --}}
                @if ($showFilters)
                    <div class="card-body border-bottom">
                        <div>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">Search Agreements</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="ph-duotone ph-magnifying-glass"></i>
                                        </span>
                                        <input wire:model.live="search" type="text" class="form-control" placeholder="Search by title, content, or role...">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Filter by Role</label>
                                    <select wire:model.live="roleFilter" class="form-select">
                                        <option value="">All Roles</option>
                                        @foreach($availableRoles as $role)
                                            <option value="{{ $role->id }}">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Status Filter</label>
                                    <select wire:model.live="statusFilter" class="form-select">
                                        <option value="">All Status</option>
                                        <option value="1">Active</option>
                                        <option value="0">Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Per Page</label>
                                    <select wire:model.live="perPage" class="form-select">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <button wire:click="clearFilters" class="btn btn-outline-secondary">
                                        <i class="ph-duotone ph-x me-2"></i>Clear Filters
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="card-body" style="overflow: visible;">
                    {{-- Agreement Table --}}
                    <div class="table-responsive" style="overflow: visible;">
                        <table class="table table-hover" id="agreementTable">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Role</th>
                                    <th>Main Agent Type</th>
                                    <th>Status</th>
                                    <th>File</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($agreements as $agreement)
                                    <tr id="agreement-row-{{ $agreement->id }}" class="align-middle">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-primary">
                                                    <i class="ph-duotone ph-file-text"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $agreement->title }}</h6>
                                                    @if($agreement->content)
                                                        <small class="text-muted">{{ Str::limit($agreement->content, 80) }}</small>
                                                    @else
                                                        <small class="text-muted">No description provided</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-info">
                                                    <i class="ph-duotone ph-user-circle"></i>
                                                </div>
                                                <div class="ms-2">
                                                    @php
                                                        $roleName = $agreement->role->name;
                                                        $formattedRole = ucfirst(str_replace('-', ' ', $roleName));
                                                    @endphp
                                                    @if($roleName === 'agent')
                                                        <span class="badge bg-warning">{{ $formattedRole }}</span>
                                                    @elseif($roleName === 'main-agent')
                                                        <span class="badge bg-info">{{ $formattedRole }}</span>
                                                    @else
                                                        <span class="badge bg-primary">{{ $formattedRole }}</span>
                                                    @endif
                                                    <small class="text-muted d-block">Target role</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($agreement->main_agent_type === 'specific')
                                                <span class="badge bg-success">Specific</span>
                                                @if($agreement->main_agent_email)
                                                    <br><small class="text-muted">{{ $agreement->main_agent_email }}</small>
                                                @endif
                                            @else
                                                <span class="badge bg-secondary">All</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $agreement->status ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $agreement->status_label }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-medium">{{ $agreement->created_at->format('M d, Y') }}</span>
                                            <small class="text-muted d-block">{{ $agreement->created_at->format('H:i') }}</small>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ $agreement->created_at->format('M d, Y') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button wire:click="openViewModal({{ $agreement->id }})" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#viewModal" title="View Details">
                                                    <i class="feather icon-eye"></i>
                                                </button>
                                                <button wire:click="openEditModal({{ $agreement->id }})" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editModal" title="Edit Agreement">
                                                    <i class="feather icon-edit"></i>
                                                </button>
                                                @if($agreement->main_agent_type === 'specific')
                                                    <a href="{{ route('admin.specific-main-agent-agreement', ['agreement_id' => $agreement->id]) }}" class="btn btn-sm btn-outline-success" title="Manage Emails">
                                                        <i class="feather icon-mail"></i>
                                                    </a>
                                                @endif
                                                <button wire:click="openDeleteModal({{ $agreement->id }})" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal" title="Delete Agreement">
                                                    <i class="feather icon-trash-2"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li>
                                                        <button wire:click="$dispatch('open-view-modal', {agreementId: {{ $agreement->id }}})"
                                                            class="dropdown-item">
                                                            <i class="ph-duotone ph-eye me-2"></i>View Details
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button wire:click="$dispatch('open-edit-modal', {agreementId: {{ $agreement->id }}})"
                                                            class="dropdown-item">
                                                            <i class="ph-duotone ph-pencil me-2"></i>Edit
                                                        </button>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    @if($agreement->file_path)
                                                        <li>
                                                            <button wire:click="downloadFile('{{ $agreement->file_path }}')"
                                                                class="dropdown-item text-info">
                                                                <i class="ph-duotone ph-download me-2"></i>Download PDF
                                                            </button>
                                                        </li>
                                                    @endif
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button wire:click="$dispatch('open-delete-modal', {agreementId: {{ $agreement->id }}})"
                                                            class="dropdown-item text-danger">
                                                            <i class="ph-duotone ph-trash me-2"></i>Delete
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="py-4 text-center">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="mb-2 feather icon-file-text text-muted" style="font-size: 3rem;"></i>
                                                <h6 class="text-muted">No agreements found</h6>
                                                <p class="mb-0 text-muted">Try adjusting your search criteria or add a new agreement.</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    {{-- Pagination --}}
                    @if ($agreements->hasPages())
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div class="text-muted">
                                Showing {{ $agreements->firstItem() }} to {{ $agreements->lastItem() }} of
                                {{ $agreements->total() }} results
                            </div>
                            <div>
                                {{ $agreements->links('pagination::bootstrap-4') }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Create Modal -->

    <div wire:ignore.self class="modal fade" id="createModal" tabindex="-1" aria-labelledby="createModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createModalLabel">Create new agreement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                    wire:click="closeCreateModal" onclick="console.log('Create modal close button clicked')"></button>
            </div>
            <form wire:submit.prevent="store">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                <input type="text" wire:model="title" class="form-control @error('title') is-invalid @enderror" id="title">
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="acs_role_id" class="form-label">Role <span class="text-danger">*</span></label>
                                <select wire:model.live="acs_role_id" class="form-select @error('acs_role_id') is-invalid @enderror" id="acs_role_id">
                                    <option value="">Select Role</option>
                                    @foreach($availableRoles as $role)
                                        <option value="{{ $role->id }}">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</option>
                                    @endforeach
                                </select>
                                @error('acs_role_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    @if($this->acs_role_id == 3)
                    <div class="mb-3">
                        <label for="select_main_agent" class="form-label">Main Agent Selection</label>
                        <select wire:model='select_main_agent' class="form-select @error('select_main_agent') is-invalid @enderror" id="select_main_agent">
                            <option value="1">For all main agents</option>
                            <option value="2">For specific main agent</option>
                        </select>
                        @error('select_main_agent')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            <strong>For all main agents:</strong> This agreement will apply to all main agents.<br>
                            <strong>For specific main agent:</strong> You will be redirected to a new page to select a specific main agent and send them an email.
                        </div>
                    </div>
                    @endif
                    <div class="mb-3">
                        <label for="content" class="form-label">Content Description</label>
                        <textarea wire:model="content" class="form-control @error('content') is-invalid @enderror" id="content" rows="3" placeholder="Optional description or summary"></textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="mb-3">
                        <label for="file_upload" class="form-label">PDF File</label>
                        <input type="file" wire:model="file_upload" class="form-control @error('file_upload') is-invalid @enderror" id="file_upload" accept=".pdf">
                        @error('file_upload')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Upload a PDF file (max 10MB). Leave empty if no file is needed.</div>
                    </div>
                                            <div class="mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select wire:model="status" class="form-select @error('status') is-invalid @enderror" id="status">
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Multiple agreements can be active at the same time for KYC purposes.</div>
                        </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeCreateModal" onclick="console.log('Create modal cancel button clicked')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Agreement</button>
                </div>
            </form>
        </div>
    </div>
</div>


    <!-- Edit Modal -->
    <div wire:ignore.self class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Agreement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="console.log('Edit modal close button clicked')"></button>
                </div>
                <form wire:submit.prevent="update">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_title" class="form-label">Title <span class="text-danger">*</span></label>
                                    <input type="text" wire:model="title" class="form-control @error('title') is-invalid @enderror" id="edit_title">
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_acs_role_id" class="form-label">Role <span class="text-danger">*</span></label>
                                    <select wire:model="acs_role_id" class="form-select @error('acs_role_id') is-invalid @enderror" id="edit_acs_role_id">
                                        <option value="">Select Role</option>
                                        @foreach($availableRoles as $role)
                                            <option value="{{ $role->id }}">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</option>
                                        @endforeach
                                    </select>
                                    @error('acs_role_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        @if($this->acs_role_id == 3)
                        <div class="mb-3">
                            <label for="edit_select_main_agent" class="form-label">Main Agent Selection</label>
                            <select wire:model='select_main_agent' class="form-select @error('select_main_agent') is-invalid @enderror" id="edit_select_main_agent">
                                <option value="1">For all main agents</option>
                                <option value="2">For specific main agent</option>
                            </select>
                            @error('select_main_agent')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <strong>For all main agents:</strong> This agreement will apply to all main agents.<br>
                                <strong>For specific main agent:</strong> This agreement is currently assigned to a specific main agent.
                            </div>
                        </div>
                        @endif
                        <div class="mb-3">
                            <label for="edit_content" class="form-label">Content Description</label>
                            <textarea wire:model="content" class="form-control @error('content') is-invalid @enderror" id="edit_content" rows="3" placeholder="Optional description or summary"></textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Current File Display -->
                        @if($currentFile)
                            <div class="mb-3">
                                <label class="form-label">Current File</label>
                                <div class="p-2 rounded border d-flex align-items-center">
                                    <i class="feather icon-file-text text-primary me-2"></i>
                                    <span class="me-auto">{{ basename($currentFile) }}</span>
                                    <button type="button" wire:click="downloadFile('{{ $currentFile }}')" class="btn btn-sm btn-outline-primary me-2">
                                        <i class="feather icon-download"></i>
                                    </button>
                                    <button type="button" wire:click="removeCurrentFile" class="btn btn-sm btn-outline-danger">
                                        <i class="feather icon-x"></i>
                                    </button>
                                </div>
                            </div>
                        @endif

                        <div class="mb-3">
                            <label for="edit_file_upload" class="form-label">{{ $currentFile ? 'Replace PDF File' : 'PDF File' }}</label>
                            <input type="file" wire:model="file_upload" class="form-control @error('file_upload') is-invalid @enderror" id="edit_file_upload" accept=".pdf">
                            @error('file_upload')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload a PDF file (max 10MB). {{ $currentFile ? 'Leave empty to keep current file.' : 'Leave empty if no file is needed.' }}</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select wire:model="status" class="form-select @error('status') is-invalid @enderror" id="edit_status">
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Multiple agreements can be active at the same time for KYC purposes.</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" aria-label="Close" wire:click="closeEditModal" onclick="console.log('Edit modal cancel button clicked')">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Agreement</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Modal -->
    <div wire:ignore.self class="modal fade" id="viewModal" tabindex="-1" role="dialog" aria-labelledby="viewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="feather icon-file-text me-2"></i>
                        Agreement Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="closeViewModal" onclick="console.log('View modal close button clicked')"></button>
                </div>
                <div class="modal-body">
                    @if($viewingAgreement)
                        <!-- Basic Information -->
                        <div class="mb-4 row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="info-label">Title</label>
                                    <div class="info-value">{{ $viewingAgreement->title }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="info-label">Role</label>
                                    <div class="info-value">
                                        <span class="badge bg-primary">{{ ucfirst(str_replace('-', ' ', $viewingAgreement->role->name)) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4 row">
                            <div class="col-md-4">
                                <div class="info-group">
                                    <label class="info-label">Main Agent Type</label>
                                    <div class="info-value">
                                        @if($viewingAgreement->main_agent_type === 'specific')
                                            <span class="badge bg-success">Specific</span>
                                        @else
                                            <span class="badge bg-secondary">All</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-group">
                                    <label class="info-label">Status</label>
                                    <div class="info-value">
                                        <span class="badge {{ $viewingAgreement->status ? 'bg-success' : 'bg-secondary' }}">
                                            {{ $viewingAgreement->status_label }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-group">
                                    <label class="info-label">Created Date</label>
                                    <div class="info-value">{{ $viewingAgreement->created_at->format('M d, Y H:i') }}</div>
                                </div>
                            </div>
                        </div>

                        @if($viewingAgreement->main_agent_type === 'specific' && $viewingAgreement->main_agent_email)
                            <div class="mb-4 row">
                                <div class="col-12">
                                    <div class="info-group">
                                        <label class="info-label">Specific Main Agent</label>
                                        <div class="info-value">
                                            <div class="content-box">
                                                <strong>Email:</strong> {{ $viewingAgreement->main_agent_email }}<br>
                                                @if($viewingAgreement->mainAgent)
                                                    <strong>Name:</strong> {{ $viewingAgreement->mainAgent->name }}<br>
                                                    <strong>Phone:</strong> {{ $viewingAgreement->mainAgent->phone ?? 'N/A' }}
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Content Description -->
                        @if($viewingAgreement->content)
                            <div class="mb-4 row">
                                <div class="col-12">
                                    <div class="info-group">
                                        <label class="info-label">Content Description</label>
                                        <div class="info-value">
                                            <div class="content-box">
                                                {{ $viewingAgreement->content }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- PDF File -->
                        @if($viewingAgreement->file_path)
                            <div class="row">
                                <div class="col-12">
                                    <div class="info-group">
                                        <label class="info-label">PDF File</label>
                                        <div class="info-value">
                                            <div class="file-display">
                                                <div class="file-info">
                                                    <i class="feather icon-file-text text-primary"></i>
                                                    <div class="file-details">
                                                        <span class="file-name">{{ basename($viewingAgreement->file_path) }}</span>
                                                        <span class="file-size">{{ $viewingAgreement->file_size ?? 'N/A' }}</span>
                                                    </div>
                                                </div>
                                                <button type="button" wire:click="downloadFile('{{ $viewingAgreement->file_path }}')" class="btn btn-primary btn-sm">
                                                    <i class="feather icon-download me-1"></i>
                                                    Download
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="row">
                                <div class="col-12">
                                    <div class="info-group">
                                        <label class="info-label">PDF File</label>
                                        <div class="info-value">
                                            <span class="text-muted">No file attached</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @else
                        <div class="py-4 text-center">
                            <i class="mb-3 feather icon-alert-circle text-muted" style="font-size: 3rem;"></i>
                            <h6 class="text-muted">No agreement data available</h6>
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    @if($viewingAgreement && $viewingAgreement->main_agent_type === 'specific')
                        <a href="{{ route('admin.specific-main-agent-agreement', ['agreement_id' => $viewingAgreement->id]) }}" class="btn btn-success">
                            <i class="feather icon-mail me-1"></i>
                            Manage Emails
                        </a>
                    @endif
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" aria-label="Close" wire:click="closeViewModal" onclick="console.log('View modal close button clicked')">
                        <i class="feather icon-x me-1"></i>
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div wire:ignore.self class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="closeDeleteModal" onclick="console.log('Delete modal close button clicked')"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this agreement? This action cannot be undone.</p>
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> If this agreement has an associated PDF file, it will also be permanently deleted.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" aria-label="Close" wire:click="closeDeleteModal" onclick="console.log('Delete modal cancel button clicked')">Cancel</button>
                    <button type="button" class="btn btn-danger" wire:click="delete">Delete Agreement</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Listen for Livewire events to show/hide modals
        document.addEventListener('livewire:init', () => {
            Livewire.on('close-modal', () => {
                const modals = ['createModal', 'editModal', 'viewModal', 'deleteModal'];
                modals.forEach(modalId => {
                    const modal = document.getElementById(modalId);
                    if (modal) {
                        const bootstrapModal = bootstrap.Modal.getInstance(modal);
                        if (bootstrapModal) {
                            bootstrapModal.hide();
                        } else {
                            if (typeof $ !== 'undefined') {
                                $(`#${modalId}`).modal('hide');
                            }
                        }
                    }
                });
            });
        });

        document.addEventListener('DOMContentLoaded', () => {
            const modals = ['createModal', 'editModal', 'viewModal', 'deleteModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.addEventListener('hidden.bs.modal', () => {});
                }
            });
        });
    </script>


</div>

