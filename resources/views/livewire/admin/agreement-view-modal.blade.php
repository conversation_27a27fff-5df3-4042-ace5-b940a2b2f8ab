<div>
    @if($isOpen && $agreement)
        <!-- View Modal -->
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1" role="dialog" aria-labelledby="viewModalLabel" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button type="button" class="btn-close" wire:click="close" aria-label="Close" style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body px-4 py-5">
                        <div class="text-center mb-4">
                            <div class="mx-auto mb-4" style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="ph-duotone ph-eye-circle" style="font-size: 3rem; color: #0288d1;"></i>
                            </div>
                            <h4 class="mb-3 fw-bold text-dark">Agreement Details</h4>
                            <p class="text-muted mb-4">View comprehensive information about this agreement</p>
                        </div>
                        
                        <!-- Basic Information -->
                        <div class="mb-4 row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="info-label">Title</label>
                                    <div class="info-value">{{ $agreement->title }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="info-label">Role</label>
                                    <div class="info-value">
                                        <span class="badge bg-primary">{{ ucfirst(str_replace('-', ' ', $agreement->role->name)) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4 row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="info-label">Status</label>
                                    <div class="info-value">
                                        <span class="badge {{ $agreement->status ? 'bg-success' : 'bg-secondary' }}">
                                            {{ $agreement->status ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="info-label">Created Date</label>
                                    <div class="info-value">{{ $agreement->created_at->format('M d, Y H:i') }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Content Description -->
                        @if($agreement->content)
                            <div class="mb-4 row">
                                <div class="col-12">
                                    <div class="info-group">
                                        <label class="info-label">Content Description</label>
                                        <div class="info-value">
                                            <div class="content-box">
                                                {{ $agreement->content }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- PDF File -->
                        @if($agreement->file_path)
                            <div class="row">
                                <div class="col-12">
                                    <div class="info-group">
                                        <label class="info-label">PDF File</label>
                                        <div class="info-value">
                                            <div class="file-display">
                                                <div class="file-info">
                                                    <i class="ph-duotone ph-file-text text-primary"></i>
                                                    <div class="file-details">
                                                        <span class="file-name">{{ basename($agreement->file_path) }}</span>
                                                        <span class="file-size">{{ $agreement->file_size ?? 'N/A' }}</span>
                                                    </div>
                                                </div>
                                                <button type="button" wire:click="downloadFile('{{ $agreement->file_path }}')" class="btn btn-primary btn-sm">
                                                    <i class="ph-duotone ph-download me-1"></i>
                                                    Download
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="row">
                                <div class="col-12">
                                    <div class="info-group">
                                        <label class="info-label">PDF File</label>
                                        <div class="info-value">
                                            <span class="text-muted">No file attached</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        
                        <div class="d-flex gap-3 justify-content-center mt-4">
                            <button type="button" class="btn btn-light px-4" wire:click="close" style="border-radius: 10px; font-weight: 500;">
                                <i class="ph-duotone ph-x me-2"></i>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

@push('styles')
<style>
    .info-group {
        margin-bottom: 1rem;
    }

    .info-label {
        display: block;
        font-weight: 600;
        color: #495057;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        color: #212529;
        font-size: 1rem;
        line-height: 1.5;
    }

    .content-box {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
        white-space: pre-wrap;
        word-wrap: break-word;
        max-height: 200px;
        overflow-y: auto;
    }

    .file-display {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
    }

    .file-info {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .file-info i {
        font-size: 1.5rem;
        margin-right: 0.75rem;
    }

    .file-details {
        display: flex;
        flex-direction: column;
    }

    .file-name {
        font-weight: 500;
        color: #212529;
        margin-bottom: 0.25rem;
    }

    .file-size {
        font-size: 0.875rem;
        color: #6c757d;
    }
</style>
@endpush