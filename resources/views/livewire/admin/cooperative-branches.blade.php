<div>
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Branches - {{ $cooperative->name }}</h2>
                    </div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.cooperatives') }}">Cooperatives</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Branches</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">Branches for {{ $cooperative->name }}</h5>
                            <small class="text-muted">Total: {{ $branches->total() }} branches</small>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('admin.cooperatives') }}" class="btn btn-secondary">
                                <i class="feather icon-arrow-left"></i> Back to Cooperatives
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search -->
                    <div class="mb-3 row">
                        <div class="col-md-6">
                            <input type="text" wire:model.live="search" class="form-control" placeholder="Search branches...">
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Branch Name</th>
                                    <th>Business Registration No</th>
                                    <th>Status</th>
                                    <th>Created By</th>
                                    <th>Verified By</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($branches as $branch)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $branch->name }}</td>
                                    <td>{{ $branch->business_registration_no ?? 'N/A' }}</td>
                                    <td>
                                        @if($branch->status === 'active')
                                            <span class="badge bg-success">Active</span>
                                        @elseif($branch->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($branch->status === 'inactive')
                                            <span class="badge bg-danger">Inactive</span>
                                        @else
                                            <span class="badge bg-secondary">{{ ucfirst($branch->status) }}</span>
                                        @endif
                                    </td>
                                    <td>{{ $branch->creator->name ?? 'N/A' }}</td>
                                    <td>{{ $branch->verifier->name ?? 'N/A' }}</td>
                                    <td>{{ $branch->created_at->format('d M Y') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center text-muted">No branches found for this cooperative.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {{ $branches->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
