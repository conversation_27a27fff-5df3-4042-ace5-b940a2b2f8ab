<div>
    <style>
        .table-responsive {
            overflow-x: auto !important;
            overflow-y: visible !important;
        }

        .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }

        .dropdown {
            position: static !important;
        }

        .card {
            overflow: visible !important;
        }
    </style>
    {{-- <PERSON> Header --}}
    <div class="page-header commission-page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Cooperative Management</h2>
                        <p class="text-muted mb-0 mt-2">Manage cooperative organizations and their information</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('message') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Main Content Card --}}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="page-header-title">
                    <h2 class="mb-0">Cooperative Organizations</h2>
                    @if ($cooperatives->total() > 0)
                        <p class="text-muted mb-0 mt-1">Showing {{ $cooperatives->firstItem() }} to
                            {{ $cooperatives->lastItem() }} of {{ $cooperatives->total() }} results</p>
                    @endif
                </div>
                <div class="page-header-breadcrumb">
                    <button wire:click="openCreateModal" class="btn btn-primary">
                        <i class="ph-duotone ph-plus me-2"></i>Add New Cooperative
                    </button>
                </div>
            </div>
        </div>

        <div class="card-body" style="overflow: visible;">
            {{-- Search Section --}}
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="ph-duotone ph-magnifying-glass"></i>
                            </span>
                            <input type="text" wire:model.live="search" class="form-control" placeholder="Search cooperatives...">
                        </div>
                    </div>
                </div>
            </div>

            {{-- Cooperative Table --}}
            <div class="table-responsive" style="overflow: visible;">
                <table class="table table-hover" id="cooperativeTable">
                    <thead>
                        <tr>
                            <th>
                                <div class="d-flex align-items-center">
                                    ID
                                    <i class="ph-duotone ph-hash ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Cooperative Name
                                    <i class="ph-duotone ph-buildings ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Created Date
                                    <i class="ph-duotone ph-calendar ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($cooperatives as $cooperative)
                            <tr id="cooperative-row-{{ $cooperative->id }}" class="align-middle">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avtar avtar-s bg-light-primary">
                                            <i class="ph-duotone ph-hash"></i>
                                        </div>
                                        <div class="ms-2">
                                            <h6 class="mb-0">{{ $cooperative->id }}</h6>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avtar avtar-s bg-light-info">
                                            <i class="ph-duotone ph-buildings"></i>
                                        </div>
                                        <div class="ms-2">
                                            <h6 class="mb-0">{{ $cooperative->name }}</h6>
                                            <small class="text-muted">Cooperative organization</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-medium">{{ $cooperative->created_at->format('M d, Y') }}</span>
                                    <small class="text-muted d-block">{{ $cooperative->created_at->format('H:i') }}</small>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="ph-duotone ph-dots-three-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <button wire:click="openEditModal({{ $cooperative->id }})"
                                                    class="dropdown-item">
                                                    <i class="ph-duotone ph-pencil me-2"></i>Edit
                                                </button>
                                            </li>
                                            <li>
                                                <hr class="dropdown-divider">
                                            </li>
                                            <li>
                                                <button wire:click="openDeleteModal({{ $cooperative->id }})"
                                                    class="dropdown-item text-danger">
                                                    <i class="ph-duotone ph-trash me-2"></i>Delete
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <div class="py-4 text-center">
                                        <div class="mx-auto mb-3 avtar avtar-xl bg-light-secondary">
                                            <i class="ph-duotone ph-buildings"></i>
                                        </div>
                                        <h5>No Cooperatives Found</h5>
                                        <p class="text-muted">Start by creating your first cooperative organization to manage cooperative data.</p>
                                        <button wire:click="openCreateModal" class="btn btn-primary">
                                            <i class="ph-duotone ph-plus me-2"></i>Create Cooperative
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            @if ($cooperatives->hasPages())
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        Showing {{ $cooperatives->firstItem() }} to {{ $cooperatives->lastItem() }} of
                        {{ $cooperatives->total() }} results
                    </div>
                    <div>
                        {{ $cooperatives->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    {{-- Create Cooperative Modal --}}
    @if ($showCreateModal)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button wire:click="closeCreateModal" type="button" class="btn-close"
                            style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body px-4 py-5">
                        <div class="text-center mb-4">
                            <div class="mx-auto mb-4"
                                style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="ph-duotone ph-plus-circle" style="font-size: 3rem; color: #1976d2;"></i>
                            </div>
                            <h4 class="mb-3 fw-bold text-dark">Create New Cooperative</h4>
                            <p class="text-muted mb-4">Add a new cooperative organization to the system</p>
                        </div>

                        <form wire:submit="store">
                            <div class="mb-4">
                                <label for="createName" class="form-label fw-semibold text-dark">
                                    Cooperative Name <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       id="createName"
                                       class="form-control @error('name') is-invalid @enderror"
                                       wire:model="name"
                                       placeholder="Enter cooperative name"
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Enter the full name of the cooperative organization</small>
                            </div>

                            <div class="d-flex gap-3 justify-content-center mt-4">
                                <button wire:click="closeCreateModal" type="button" class="btn btn-light px-4"
                                    style="border-radius: 10px; font-weight: 500;">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary px-4"
                                    style="border-radius: 10px; font-weight: 500;">
                                    <span wire:loading wire:target="store"
                                        class="spinner-border spinner-border-sm me-2"></span>
                                    <span wire:loading.remove wire:target="store">
                                        <i class="ph-duotone ph-check me-2"></i>
                                    </span>
                                    Create Cooperative
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Edit Cooperative Modal --}}
    @if ($showEditModal)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button wire:click="closeEditModal" type="button" class="btn-close"
                            style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body px-4 py-5">
                        <div class="text-center mb-4">
                            <div class="mx-auto mb-4"
                                style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="ph-duotone ph-pencil" style="font-size: 3rem; color: #ff6348;"></i>
                            </div>
                            <h4 class="mb-3 fw-bold text-dark">Edit Cooperative</h4>
                            <p class="text-muted mb-4">Update the cooperative organization information</p>
                        </div>

                        <form wire:submit="update">
                            <div class="mb-4">
                                <label for="editName" class="form-label fw-semibold text-dark">
                                    Cooperative Name <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       id="editName"
                                       class="form-control @error('name') is-invalid @enderror"
                                       wire:model="name"
                                       placeholder="Enter cooperative name"
                                       style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Update the cooperative organization name</small>
                            </div>

                            <div class="d-flex gap-3 justify-content-center mt-4">
                                <button wire:click="closeEditModal" type="button" class="btn btn-light px-4"
                                    style="border-radius: 10px; font-weight: 500;">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-warning px-4"
                                    style="border-radius: 10px; font-weight: 500;">
                                    <span wire:loading wire:target="update"
                                        class="spinner-border spinner-border-sm me-2"></span>
                                    <span wire:loading.remove wire:target="update">
                                        <i class="ph-duotone ph-check me-2"></i>
                                    </span>
                                    Update Cooperative
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Delete Confirmation Modal --}}
    @if ($showDeleteModal)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button wire:click="closeDeleteModal" type="button" class="btn-close"
                            style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body text-center px-4 py-5">
                        <div class="mx-auto mb-4"
                            style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #f8d7da 0%, #f1c2c7 100%); display: flex; align-items: center; justify-content: center;">
                            <i class="ph-duotone ph-trash" style="font-size: 3rem; color: #dc3545;"></i>
                        </div>
                        <h4 class="mb-3 fw-bold text-dark">Delete Cooperative</h4>
                        <p class="text-muted mb-4 lh-base">
                            Are you sure you want to delete this cooperative organization?
                            <br>This action cannot be undone and will permanently remove the cooperative.
                        </p>

                        <div class="d-flex gap-3 justify-content-center">
                            <button wire:click="closeDeleteModal" type="button" class="btn btn-light px-4"
                                style="border-radius: 10px; font-weight: 500;">
                                Cancel
                            </button>
                            <button wire:click="delete" type="button" class="btn btn-danger px-4"
                                style="border-radius: 10px; font-weight: 500;">
                                <span wire:loading wire:target="delete"
                                    class="spinner-border spinner-border-sm me-2"></span>
                                <span wire:loading.remove wire:target="delete">
                                    <i class="ph-duotone ph-trash me-2"></i>
                                </span>
                                Delete Cooperative
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
