<div>
    <style>
        .table-responsive {
            overflow-x: auto !important;
            overflow-y: visible !important;
        }

        .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }

        .dropdown {
            position: static !important;
        }

        .card {
            overflow: visible !important;
        }
    </style>
    {{-- <PERSON> Header --}}
    <div class="page-header commission-page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Admin Management</h2>
                        <p class="text-muted mb-0 mt-2">Manage system administrators and their access privileges</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('success') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Main Content Card --}}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="page-header-title">
                    <h2 class="mb-0">System Administrators</h2>
                    @if ($admins->total() > 0)
                        <p class="text-muted mb-0 mt-1">Showing {{ $admins->firstItem() }} to
                            {{ $admins->lastItem() }} of {{ $admins->total() }} results</p>
                    @endif
                </div>
                <div class="page-header-breadcrumb">
                    <button wire:click="openCreateModal" class="btn btn-primary">
                        <i class="ph-duotone ph-plus me-2"></i>Add New Admin
                    </button>
                </div>
            </div>
        </div>

        <div class="card-body" style="overflow: visible;">
            {{-- Search Section --}}
            <div class="row mb-4">
                <div class="col-md-6">
                    <label class="form-label fw-semibold">Search Administrators</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="ph-duotone ph-magnifying-glass"></i>
                        </span>
                        <input type="text" wire:model.live="search" class="form-control" placeholder="Search by name or email...">
                    </div>
                </div>
            </div>

            {{-- Admin Table --}}
            <div class="table-responsive" style="overflow: visible;">
                <table class="table table-hover" id="adminTable">
                    <thead>
                        <tr>
                            <th>
                                <div class="d-flex align-items-center">
                                    Administrator
                                    <i class="ph-duotone ph-user ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Email Address
                                    <i class="ph-duotone ph-envelope ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Role
                                    <i class="ph-duotone ph-shield ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Status
                                    <i class="ph-duotone ph-info ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th>
                                <div class="d-flex align-items-center">
                                    Created Date
                                    <i class="ph-duotone ph-calendar ms-1 text-muted"></i>
                                </div>
                            </th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($admins as $admin)
                            <tr id="admin-row-{{ $admin->id }}" class="align-middle">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avtar avtar-s bg-light-primary">
                                            <i class="ph-duotone ph-user"></i>
                                        </div>
                                        <div class="ms-2">
                                            <h6 class="mb-0">{{ $admin->name }}</h6>
                                            <small class="text-muted">System administrator</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avtar avtar-s bg-light-info">
                                            <i class="ph-duotone ph-envelope"></i>
                                        </div>
                                        <div class="ms-2">
                                            <h6 class="mb-0">{{ $admin->email }}</h6>
                                            <small class="text-muted">Contact email</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $admin->role->name ?? 'No Role' }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $admin->status === 'active' ? 'success' : 'warning' }}">
                                        {{ ucfirst($admin->status ?? 'pending') }}
                                    </span>
                                </td>
                                <td>
                                    <span class="fw-medium">{{ $admin->created_at->format('M d, Y') }}</span>
                                    <small class="text-muted d-block">{{ $admin->created_at->format('H:i') }}</small>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="ph-duotone ph-dots-three-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <button wire:click="openEditModal('{{ $admin->id }}')"
                                                    class="dropdown-item">
                                                    <i class="ph-duotone ph-pencil me-2"></i>Edit
                                                </button>
                                            </li>
                                            <li>
                                                <hr class="dropdown-divider">
                                            </li>
                                            <li>
                                                <button wire:click="openDeleteModal('{{ $admin->id }}')"
                                                    class="dropdown-item text-danger">
                                                    <i class="ph-duotone ph-trash me-2"></i>Delete
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="py-4 text-center">
                                        <div class="mx-auto mb-3 avtar avtar-xl bg-light-secondary">
                                            <i class="ph-duotone ph-user"></i>
                                        </div>
                                        <h5>No Administrators Found</h5>
                                        <p class="text-muted">Start by creating your first system administrator to manage the platform.</p>
                                        <button wire:click="openCreateModal" class="btn btn-primary">
                                            <i class="ph-duotone ph-plus me-2"></i>Create Admin
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            @if ($admins->hasPages())
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        Showing {{ $admins->firstItem() }} to {{ $admins->lastItem() }} of
                        {{ $admins->total() }} results
                    </div>
                    <div>
                        {{ $admins->links('pagination::bootstrap-4') }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    {{-- Create Admin Modal --}}
    @if ($showCreateModal)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button wire:click="closeCreateModal" type="button" class="btn-close"
                            style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body px-4 py-5">
                        <div class="text-center mb-4">
                            <div class="mx-auto mb-4"
                                style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="ph-duotone ph-plus-circle" style="font-size: 3rem; color: #1976d2;"></i>
                            </div>
                            <h4 class="mb-3 fw-bold text-dark">Create New Administrator</h4>
                            <p class="text-muted mb-4">Add a new system administrator with full access privileges</p>
                        </div>

                        <form wire:submit="store">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="createName" class="form-label fw-semibold text-dark">
                                        Full Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           id="createName"
                                           class="form-control @error('name') is-invalid @enderror"
                                           wire:model="name"
                                           placeholder="Enter full name"
                                           style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-12">
                                    <label for="createEmail" class="form-label fw-semibold text-dark">
                                        Email Address <span class="text-danger">*</span>
                                    </label>
                                    <input type="email"
                                           id="createEmail"
                                           class="form-control @error('email') is-invalid @enderror"
                                           wire:model="email"
                                           placeholder="Enter email address"
                                           style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="createPassword" class="form-label fw-semibold text-dark">
                                        Password <span class="text-danger">*</span>
                                    </label>
                                    <input type="password"
                                           id="createPassword"
                                           class="form-control @error('password') is-invalid @enderror"
                                           wire:model="password"
                                           placeholder="Enter password"
                                           style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="createPasswordConfirmation" class="form-label fw-semibold text-dark">
                                        Confirm Password <span class="text-danger">*</span>
                                    </label>
                                    <input type="password"
                                           id="createPasswordConfirmation"
                                           class="form-control @error('password_confirmation') is-invalid @enderror"
                                           wire:model="password_confirmation"
                                           placeholder="Confirm password"
                                           style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                    @error('password_confirmation')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="d-flex gap-3 justify-content-center mt-4">
                                <button wire:click="closeCreateModal" type="button" class="btn btn-light px-4"
                                    style="border-radius: 10px; font-weight: 500;">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary px-4"
                                    style="border-radius: 10px; font-weight: 500;">
                                    <span wire:loading wire:target="store"
                                        class="spinner-border spinner-border-sm me-2"></span>
                                    <span wire:loading.remove wire:target="store">
                                        <i class="ph-duotone ph-check me-2"></i>
                                    </span>
                                    Create Administrator
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Edit Admin Modal --}}
    @if ($showEditModal)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button wire:click="closeEditModal" type="button" class="btn-close"
                            style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body px-4 py-5">
                        <div class="text-center mb-4">
                            <div class="mx-auto mb-4"
                                style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="ph-duotone ph-pencil" style="font-size: 3rem; color: #ff6348;"></i>
                            </div>
                            <h4 class="mb-3 fw-bold text-dark">Edit Administrator</h4>
                            <p class="text-muted mb-4">Update administrator information and access credentials</p>
                        </div>

                        <form wire:submit="update">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="editName" class="form-label fw-semibold text-dark">
                                        Full Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           id="editName"
                                           class="form-control @error('name') is-invalid @enderror"
                                           wire:model="name"
                                           placeholder="Enter full name"
                                           style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-12">
                                    <label for="editEmail" class="form-label fw-semibold text-dark">
                                        Email Address <span class="text-danger">*</span>
                                    </label>
                                    <input type="email"
                                           id="editEmail"
                                           class="form-control @error('email') is-invalid @enderror"
                                           wire:model="email"
                                           placeholder="Enter email address"
                                           style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="editPassword" class="form-label fw-semibold text-dark">
                                        New Password <small class="text-muted">(optional)</small>
                                    </label>
                                    <input type="password"
                                           id="editPassword"
                                           class="form-control @error('password') is-invalid @enderror"
                                           wire:model="password"
                                           placeholder="Enter new password"
                                           style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Leave blank to keep current password</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="editPasswordConfirmation" class="form-label fw-semibold text-dark">
                                        Confirm Password
                                    </label>
                                    <input type="password"
                                           id="editPasswordConfirmation"
                                           class="form-control @error('password_confirmation') is-invalid @enderror"
                                           wire:model="password_confirmation"
                                           placeholder="Confirm password"
                                           style="border-radius: 10px; border: 2px solid #e9ecef; padding: 0.75rem 1rem;">
                                    @error('password_confirmation')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="d-flex gap-3 justify-content-center mt-4">
                                <button wire:click="closeEditModal" type="button" class="btn btn-light px-4"
                                    style="border-radius: 10px; font-weight: 500;">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-warning px-4"
                                    style="border-radius: 10px; font-weight: 500;">
                                    <span wire:loading wire:target="update"
                                        class="spinner-border spinner-border-sm me-2"></span>
                                    <span wire:loading.remove wire:target="update">
                                        <i class="ph-duotone ph-check me-2"></i>
                                    </span>
                                    Update Administrator
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Delete Confirmation Modal --}}
    @if ($showDeleteModal)
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button wire:click="closeDeleteModal" type="button" class="btn-close"
                            style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body text-center px-4 py-5">
                        <div class="mx-auto mb-4"
                            style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #f8d7da 0%, #f1c2c7 100%); display: flex; align-items: center; justify-content: center;">
                            <i class="ph-duotone ph-trash" style="font-size: 3rem; color: #dc3545;"></i>
                        </div>
                        <h4 class="mb-3 fw-bold text-dark">Delete Administrator</h4>
                        <p class="text-muted mb-4 lh-base">
                            Are you sure you want to delete this administrator account?
                            <br>This action cannot be undone and will permanently remove their access to the system.
                        </p>

                        <div class="alert alert-warning border-0 d-flex align-items-start mb-4"
                            style="border-radius: 12px; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);">
                            <div class="avtar avtar-s bg-warning me-3 mt-1" style="border-radius: 8px;">
                                <i class="ph-duotone ph-warning"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-2 text-warning fw-bold">Security Warning</h6>
                                <div class="text-dark">
                                    Deleting an administrator will immediately revoke all their system access and permissions.
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-3 justify-content-center">
                            <button wire:click="closeDeleteModal" type="button" class="btn btn-light px-4"
                                style="border-radius: 10px; font-weight: 500;">
                                Cancel
                            </button>
                            <button wire:click="delete" type="button" class="btn btn-danger px-4"
                                style="border-radius: 10px; font-weight: 500;">
                                <span wire:loading wire:target="delete"
                                    class="spinner-border spinner-border-sm me-2"></span>
                                <span wire:loading.remove wire:target="delete">
                                    <i class="ph-duotone ph-trash me-2"></i>
                                </span>
                                Delete Administrator
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
