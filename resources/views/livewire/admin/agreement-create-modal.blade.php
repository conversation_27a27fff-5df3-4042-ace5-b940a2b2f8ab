<div>
    @if($isOpen)
        <!-- Create Modal -->
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="false">
            <div class="modal-dialog modal-lg">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button type="button" class="btn-close" wire:click="close" aria-label="Close" style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body px-4 py-5">
                        <div class="text-center mb-4">
                            <div class="mx-auto mb-4" style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="ph-duotone ph-plus-circle" style="font-size: 3rem; color: #1976d2;"></i>
                            </div>
                            <h4 class="mb-3 fw-bold text-dark">Create New Agreement</h4>
                            <p class="text-muted mb-4">Set up terms and conditions agreement for specific user roles with optional PDF attachment</p>
                        </div>
                        <form wire:submit.prevent="store">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                        <input type="text" wire:model="title" class="form-control @error('title') is-invalid @enderror" id="title">
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="acs_role_id" class="form-label">Role <span class="text-danger">*</span></label>
                                        <select wire:model="acs_role_id" class="form-select @error('acs_role_id') is-invalid @enderror" id="acs_role_id">
                                            <option value="">Select Role</option>
                                            @foreach($availableRoles as $role)
                                                <option value="{{ $role->id }}">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</option>
                                            @endforeach
                                        </select>
                                        @error('acs_role_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="content" class="form-label">Content Description</label>
                                <textarea wire:model="content" class="form-control @error('content') is-invalid @enderror" id="content" rows="3" placeholder="Optional description or summary"></textarea>
                                @error('content')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label for="file_upload" class="form-label">PDF File</label>
                                <input type="file" wire:model="file_upload" class="form-control @error('file_upload') is-invalid @enderror" id="file_upload" accept=".pdf">
                                @error('file_upload')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Upload a PDF file (max 10MB). Leave empty if no file is needed.</div>
                            </div>
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select wire:model="status" class="form-select @error('status') is-invalid @enderror" id="status">
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Only one agreement can be active per role at a time.</div>
                            </div>

                            <div class="d-flex gap-3 justify-content-center mt-4">
                                <button type="button" class="btn btn-light px-4" wire:click="close" style="border-radius: 10px; font-weight: 500;">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary px-4" style="border-radius: 10px; font-weight: 500;">
                                    <i class="ph-duotone ph-check me-2"></i>
                                    Create Agreement
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>