<div>
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Branch List</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="mb-4 row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Branches Summary</h5>
                    <button class="btn btn-sm btn-outline-secondary" type="button" onclick="toggleSummaryDetails()" id="summaryToggleBtn">
                        <i class="fas fa-chevron-down" id="summaryChevron"></i> Details
                    </button>
                </div>
                <div class="card-body">
                    <!-- Overall Summary Stats -->
                    <div class="mb-3 text-center row">
                        <div class="col-md-3">
                            <div class="p-3 rounded border">
                                <h4 class="mb-1 text-primary">{{ $branchesSummary->sum('total') }}</h4>
                                <small class="text-muted">Total Branches</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3 rounded border">
                                <h4 class="mb-1 text-warning">{{ $branchesSummary->sum('pending') }}</h4>
                                <small class="text-muted">Pending</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3 rounded border">
                                <h4 class="mb-1 text-success">{{ $branchesSummary->sum('active') }}</h4>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3 rounded border">
                                <h4 class="mb-1 text-danger">{{ $branchesSummary->sum('inactive') }}</h4>
                                <small class="text-muted">Inactive</small>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Breakdown (Collapsible) -->
                    <div style="display: none;" id="summaryDetails">
                        <hr>
                        <h6 class="mb-3">By Cooperative</h6>
                        <div class="row">
                            @foreach($branchesSummary as $summary)
                            <div class="mb-3 col-lg-6 col-xl-4">
                                <div class="border card">
                                    <div class="p-3 card-body">
                                        <h6 class="mb-2 card-title">{{ $summary->cooperative_name }}</h6>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">Total: {{ $summary->total }}</small>
                                            <div>
                                                @if($summary->pending > 0)
                                                    <span class="badge bg-warning me-1">{{ $summary->pending }}</span>
                                                @endif
                                                @if($summary->active > 0)
                                                    <span class="badge bg-success me-1">{{ $summary->active }}</span>
                                                @endif
                                                @if($summary->inactive > 0)
                                                    <span class="badge bg-danger">{{ $summary->inactive }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">Branches</h5>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="mb-3 row">
                        <div class="col-md-4">
                            <input type="text"
                                wire:model.live="search"
                                class="form-control"
                                placeholder="Search branches...">
                        </div>
                        <div class="col-md-4">
                            <select wire:model.live="selectedCooperative" class="form-select">
                                <option value="">All Cooperatives</option>
                                @foreach($cooperatives as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <div class="btn-group" role="group">
                                <input type="radio"
                                    class="btn-check"
                                    name="status"
                                    id="all"
                                    wire:model.live="selectedStatus"
                                    value=""
                                    autocomplete="off">
                                <label class="btn btn-outline-secondary" for="all">All</label>

                                <input type="radio"
                                    class="btn-check"
                                    name="status"
                                    id="pending"
                                    wire:model.live="selectedStatus"
                                    value="pending"
                                    autocomplete="off">
                                <label class="btn btn-outline-warning" for="pending">Pending</label>

                                <input type="radio"
                                    class="btn-check"
                                    name="status"
                                    id="active"
                                    wire:model.live="selectedStatus"
                                    value="active"
                                    autocomplete="off">
                                <label class="btn btn-outline-success" for="active">Active</label>

                                <input type="radio"
                                    class="btn-check"
                                    name="status"
                                    id="inactive"
                                    wire:model.live="selectedStatus"
                                    value="inactive"
                                    autocomplete="off">
                                <label class="btn btn-outline-danger" for="inactive">Inactive</label>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Cooperative</th>
                                    <th>Status</th>
                                    <th>Created By</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($branches as $branch)
                                <tr>
                                    <td>{{ $branch->name }}</td>
                                    <td>{{ $branch->cooperative->name ?? 'N/A' }}</td>
                                    <td>
                                        @if($branch->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($branch->status === 'active')
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>{{ $branch->creator->name ?? 'N/A' }}</td>
                                    <td>{{ $branch->created_at->format('d M Y') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button"
                                                    class="btn btn-sm btn-info"
                                                    wire:click="showBranchDetails('{{ $branch->id }}')"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @if($branch->status === 'pending')
                                            <button type="button"
                                                    class="btn btn-sm btn-success"
                                                    wire:click="openApproval({{ $branch->id }})"
                                                    wire:loading.attr="disabled"
                                                    title="Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-sm btn-danger"
                                                    wire:click="openRejection({{ $branch->id }})"
                                                    wire:loading.attr="disabled"
                                                    title="Decline"
                                                    onclick="console.log('Decline button clicked for branch {{ $branch->id }}')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No branches found.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {{ $branches->links() }}
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Details Modal -->
    @if($showModal && $selectedBranch)
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog" wire:click="closeModal">
        <div class="modal-dialog modal-lg" role="document" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Branch Details</h5>
                    <button type="button" class="btn-close" wire:click="closeModal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Branch Name</label>
                                <div>
                                    <span>{{ $selectedBranch->name }}</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Business Registration No</label>
                                <div>
                                    <span>{{ $selectedBranch->business_registration_no ?? 'N/A' }}</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Cooperative</label>
                                <div>
                                    <span>{{ $selectedBranch->cooperative->name ?? 'N/A' }}</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Status</label>
                                <div>
                                    @if($selectedBranch->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($selectedBranch->status === 'active')
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-danger">Inactive</span>
                                    @endif
                                </div>
                            </div>

                            @if($selectedBranch->status === 'active' && $selectedBranch->registration_token)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Registration Token</label>
                                <div class="input-group">
                                    <input type="text"
                                           class="form-control"
                                           value="{{ $selectedBranch->registration_token }}"
                                           readonly
                                           id="registration-token-{{ $selectedBranch->id }}">
                                    <button class="btn btn-outline-secondary"
                                            type="button"
                                            onclick="copyToClipboard('registration-token-{{ $selectedBranch->id }}')"
                                            title="Copy to clipboard">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <small class="text-muted">This token is generated when the branch is approved and can be used for registration purposes.</small>
                            </div>
                            @endif
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Address</label>
                                <div>
                                    <span>{{ $selectedBranch->address ?? 'N/A' }}</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Created By</label>
                                <div>
                                    <span>{{ $selectedBranch->creator->name ?? 'N/A' }}</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Created At</label>
                                <div>
                                    <span>{{ $selectedBranch->created_at->format('d M Y H:i:s') }}</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Verified By</label>
                                <div>
                                    <span>{{ $selectedBranch->verifier->name ?? 'N/A' }}</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Verified At</label>
                                <div>
                                    <span>{{ $selectedBranch->verified_at ? $selectedBranch->verified_at->format('d M Y H:i:s') : 'N/A' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Approval History Section -->
                    @if($selectedBranch->approvalHistory && $selectedBranch->approvalHistory->count() > 0)
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3">Approval History</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Action</th>
                                            <th>By</th>
                                            <th>Reason</th>
                                            <th>Status Change</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($selectedBranch->approvalHistory->sortByDesc('created_at') as $history)
                                        <tr>
                                            <td>{{ $history->created_at->format('d M Y H:i:s') }}</td>
                                            <td>
                                                @if($history->action === 'approve')
                                                    <span class="badge bg-success">{{ ucfirst($history->action) }}</span>
                                                @else
                                                    <span class="badge bg-danger">{{ ucfirst($history->action) }}</span>
                                                @endif
                                            </td>
                                            <td>{{ $history->actionBy->name ?? 'N/A' }}</td>
                                            <td>
                                                @if($history->reason)
                                                    <span class="text-muted">{{ Str::limit($history->reason, 50) }}</span>
                                                @else
                                                    <span class="text-muted">No reason provided</span>
                                                @endif
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    {{ ucfirst($history->previous_status) }} → {{ ucfirst($history->new_status) }}
                                                </small>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif

    <!-- Approval Modal -->
    @if($showApprovalModal && $pendingActionBranch)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1" wire:click="closeApprovalModal">
        <div class="modal-dialog" role="document" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Approve Branch</h5>
                    <button type="button" class="btn-close" wire:click="closeApprovalModal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to approve the branch <strong>{{ $pendingActionBranch->name }}</strong>?</p>

                    <div class="mb-3">
                        <label for="approvalReason" class="form-label">Approval Reason (Optional)</label>
                        <textarea
                            wire:model="approvalReason"
                            class="form-control"
                            id="approvalReason"
                            rows="3"
                            placeholder="Enter approval reason (optional)..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeApprovalModal">Cancel</button>
                    <button type="button"
                            class="btn btn-success"
                            wire:click="approveBranch('{{ $pendingActionBranch->id }}')"
                            wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="approveBranch('{{ $pendingActionBranch->id }}')">
                            Approve Branch
                        </span>
                        <span wire:loading wire:target="approveBranch('{{ $pendingActionBranch->id }}')">
                            Approving...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif

    <!-- Rejection Modal -->
    <!-- Debug: showRejectionModal = {{ $showRejectionModal ? 'true' : 'false' }}, pendingActionBranch = {{ $pendingActionBranch ? 'exists' : 'null' }} -->
    @if($showRejectionModal && $pendingActionBranch)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4); z-index: 1050;" tabindex="-1" wire:click="closeRejectionModal">
        <div class="modal-dialog" role="document" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Decline Branch</h5>
                    <button type="button" class="btn-close" wire:click="closeRejectionModal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to decline the branch <strong>{{ $pendingActionBranch->name }}</strong>?</p>

                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea
                            wire:model="rejectionReason"
                            class="form-control"
                            id="rejectionReason"
                            rows="3"
                            placeholder="Please provide a reason for rejection..."
                            required></textarea>
                        @error('rejectionReason')
                            <div class="mt-1 text-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeRejectionModal">Cancel</button>
                    <button type="button"
                            class="btn btn-danger"
                            wire:click="declineBranch('{{ $pendingActionBranch->id }}')"
                            wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="declineBranch('{{ $pendingActionBranch->id }}')">
                            Decline Branch
                        </span>
                        <span wire:loading wire:target="declineBranch('{{ $pendingActionBranch->id }}')">
                            Declining...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show" style="z-index: 1040;"></div>
    @endif

    <script>
        // Add keyboard event listener for Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // Check if any modal is open and close it
                if (document.querySelector('.modal.show')) {
                    // Trigger the appropriate close method based on which modal is open
                    if (document.querySelector('[wire\\:click="closeModal"]')) {
                        @this.closeModal();
                    } else if (document.querySelector('[wire\\:click="closeApprovalModal"]')) {
                        @this.closeApprovalModal();
                    } else if (document.querySelector('[wire\\:click="closeRejectionModal"]')) {
                        @this.closeRejectionModal();
                    }
                }
            }
        });

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');
                // Show success message
                const button = element.nextElementSibling;
                const originalIcon = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.remove('btn-outline-secondary');
                button.classList.add('btn-success');

                setTimeout(() => {
                    button.innerHTML = originalIcon;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-outline-secondary');
                }, 2000);
            } catch (err) {
                console.error('Failed to copy: ', err);
            }
        }

        function toggleSummaryDetails() {
            const details = document.getElementById('summaryDetails');
            const chevron = document.getElementById('summaryChevron');

            if (details.style.display === 'none') {
                details.style.display = 'block';
                chevron.classList.remove('fa-chevron-down');
                chevron.classList.add('fa-chevron-up');
            } else {
                details.style.display = 'none';
                chevron.classList.remove('fa-chevron-up');
                chevron.classList.add('fa-chevron-down');
            }
        }
    </script>
</div>
