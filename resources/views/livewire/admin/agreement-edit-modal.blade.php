<div>
    @if($isOpen)
        <!-- Edit Modal -->
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.4);" tabindex="-1" role="dialog" aria-labelledby="editModalLabel" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content border-0" style="border-radius: 16px;">
                    <div class="modal-header border-0 pb-0">
                        <button type="button" class="btn-close" wire:click="close" aria-label="Close" style="position: absolute; top: 1rem; right: 1rem; z-index: 10;"></button>
                    </div>
                    <div class="modal-body px-4 py-5">
                        <div class="text-center mb-4">
                            <div class="mx-auto mb-4" style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="ph-duotone ph-pencil-circle" style="font-size: 3rem; color: #f59e0b;"></i>
                            </div>
                            <h4 class="mb-3 fw-bold text-dark">Edit Agreement</h4>
                            <p class="text-muted mb-4">Update the terms and conditions agreement details and PDF attachment</p>
                        </div>
                        <form wire:submit.prevent="update">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_title" class="form-label">Title <span class="text-danger">*</span></label>
                                        <input type="text" wire:model="title" class="form-control @error('title') is-invalid @enderror" id="edit_title">
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_acs_role_id" class="form-label">Role <span class="text-danger">*</span></label>
                                        <select wire:model="acs_role_id" class="form-select @error('acs_role_id') is-invalid @enderror" id="edit_acs_role_id">
                                            <option value="">Select Role</option>
                                            @foreach($availableRoles as $role)
                                                <option value="{{ $role->id }}">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</option>
                                            @endforeach
                                        </select>
                                        @error('acs_role_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="edit_content" class="form-label">Content Description</label>
                                <textarea wire:model="content" class="form-control @error('content') is-invalid @enderror" id="edit_content" rows="3" placeholder="Optional description or summary"></textarea>
                                @error('content')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Current File Display -->
                            @if($currentFile)
                                <div class="mb-3">
                                    <label class="form-label">Current File</label>
                                    <div class="p-2 rounded border d-flex align-items-center">
                                        <i class="ph-duotone ph-file-text text-primary me-2"></i>
                                        <span class="me-auto">{{ basename($currentFile) }}</span>
                                        <button type="button" wire:click="downloadFile('{{ $currentFile }}')" class="btn btn-sm btn-outline-primary me-2">
                                            <i class="ph-duotone ph-download"></i>
                                        </button>
                                        <button type="button" wire:click="removeCurrentFile" class="btn btn-sm btn-outline-danger">
                                            <i class="ph-duotone ph-x"></i>
                                        </button>
                                    </div>
                                </div>
                            @endif

                            <div class="mb-3">
                                <label for="edit_file_upload" class="form-label">{{ $currentFile ? 'Replace PDF File' : 'PDF File' }}</label>
                                <input type="file" wire:model="file_upload" class="form-control @error('file_upload') is-invalid @enderror" id="edit_file_upload" accept=".pdf">
                                @error('file_upload')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Upload a PDF file (max 10MB). {{ $currentFile ? 'Leave empty to keep current file.' : 'Leave empty if no file is needed.' }}</div>
                            </div>
                            <div class="mb-3">
                                <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select wire:model="status" class="form-select @error('status') is-invalid @enderror" id="edit_status">
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Only one agreement can be active per role at a time.</div>
                            </div>

                            <div class="d-flex gap-3 justify-content-center mt-4">
                                <button type="button" class="btn btn-light px-4" wire:click="close" style="border-radius: 10px; font-weight: 500;">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-warning px-4" style="border-radius: 10px; font-weight: 500;">
                                    <i class="ph-duotone ph-check me-2"></i>
                                    Update Agreement
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>