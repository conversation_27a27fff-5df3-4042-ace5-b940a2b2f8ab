<div>
    <style>
        .email-status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .email-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .email-info {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .email-info h6 {
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .email-info p {
            margin-bottom: 0.25rem;
            color: #6c757d;
        }

        .stats-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .stats-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .stats-card .row {
            align-items: center;
            height: 100%;
        }

        .stats-card h3 {
            margin-bottom: 0.5rem;
        }

        .stats-card h6 {
            margin-bottom: 0;
            line-height: 1.2;
        }

        /* Search dropdown styles */
        .search-input-container {
            position: relative;
        }

        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1050;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            max-height: 300px;
            overflow-y: auto;
            margin-top: 2px;
            min-height: 50px;
        }

        .search-dropdown:empty {
            display: none;
        }

        .search-result-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background-color 0.15s ease-in-out;
        }

        .search-result-item:hover {
            background-color: #f8f9fa;
        }

        .search-result-item.disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #f8f9fa;
        }

        .search-result-item.disabled:hover {
            background-color: #f8f9fa;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .search-result-item .agent-name {
            font-weight: 600;
            color: #495057;
        }

        .search-result-item .agent-email {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .selected-agent-display {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 1rem;
        }

        .search-input-container .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>

    @if(!$agreement)
        <!-- Loading or Error State -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h2 class="mb-0">Agreement Not Found</h2>
                            <p class="mt-2 mb-0 text-muted">The requested agreement could not be found.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="py-5 text-center card-body">
                        <i class="mb-3 feather icon-alert-circle text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted">Agreement not found</h6>
                        <p class="mb-3 text-muted">The agreement you're looking for doesn't exist or has been removed.</p>
                        <a href="{{ route('admin.agreement-list') }}" class="btn btn-primary">
                            <i class="feather icon-arrow-left me-1"></i>
                            Back to Agreements
                        </a>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Page Header -->
        <div class="page-header">
            <div class="page-block">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="page-header-title">
                            <h2 class="mb-0">Manage Email Notifications</h2>
                            <p class="mt-2 mb-0 text-muted">Manage email notifications for: {{ $agreement->title }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        @if (session()->has('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if (session()->has('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if (session()->has('warning'))
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                {{ session('warning') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <!-- Agreement Information -->
        <div class="email-info">
            <h6><i class="feather icon-file-text me-1"></i> Agreement Details</h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Title:</strong> {{ $agreement->title }}</p>
                    <p><strong>Role:</strong> {{ ucfirst(str_replace('-', ' ', $agreement->role->name)) }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Status:</strong>
                        <span class="badge {{ $agreement->status ? 'bg-success' : 'bg-secondary' }}">
                            {{ $agreement->status_label }}
                        </span>
                    </p>
                    <p><strong>Created:</strong> {{ $agreement->created_at->format('M d, Y H:i') }}</p>
                </div>
            </div>
            @if($agreement->content)
                <p><strong>Description:</strong> {{ $agreement->content }}</p>
            @endif
            @if($agreement->file_path)
                <p><strong>PDF File:</strong>
                    <button wire:click="downloadFile('{{ $agreement->file_path }}')" class="btn btn-sm btn-outline-primary">
                        <i class="feather icon-download me-1"></i>
                        Download PDF
                    </button>
                </p>
            @endif
        </div>

        <!-- Statistics Cards -->
        <div class="mb-4 row">
            <div class="col-sm-6 col-xl-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h3 class="text-primary">{{ $statistics['total'] }}</h3>
                                <h6 class="text-muted m-b-0">Total Emails</h6>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="feather icon-mail text-primary" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xl-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h3 class="text-warning">{{ $statistics['pending'] }}</h3>
                                <h6 class="text-muted m-b-0">Pending</h6>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="feather icon-clock text-warning" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xl-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h3 class="text-success">{{ $statistics['sent'] }}</h3>
                                <h6 class="text-muted m-b-0">Sent</h6>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="feather icon-check-circle text-success" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-xl-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h3 class="text-danger">{{ $statistics['failed'] }}</h3>
                                <h6 class="text-muted m-b-0">Failed</h6>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="feather icon-x-circle text-danger" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">Email Notifications</h5>
                            </div>
                            <div class="col-auto">
                                <div class="gap-2 d-flex">
                                    @if($statistics['pending'] > 0)
                                        <button wire:click="sendAllEmails" class="btn btn-success">
                                            <i class="feather icon-send me-1"></i>
                                            Send All Pending
                                        </button>
                                    @endif
                                    <a href="{{ route('admin.agreement-list') }}" class="btn btn-secondary">
                                        <i class="feather icon-arrow-left me-1"></i>
                                        Back to Agreements
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Add Email Form -->
                        <div class="mb-4">
                            <div class="row">
                                <div class="col-md-8">
                                    <!-- Search Input -->
                                    <div class="search-input-container">
                                        <div class="mb-2 input-group">
                                            <input type="text"
                                                   wire:model.live="searchQuery"
                                                   class="form-control"
                                                   placeholder="Search main agents by name or email...">
                                            <button class="btn btn-outline-secondary" type="button" wire:click="clearSelection">
                                                <i class="feather icon-x"></i>
                                            </button>
                                        </div>

                                        <!-- Search Results Dropdown -->
                                        @if(count($searchResults) > 0)
                                            <div class="search-dropdown">
                                                @foreach($searchResults as $result)
                                                    @php
                                                        $isAlreadyAdded = $this->isMainAgentAlreadyAdded($result['id']);
                                                    @endphp
                                                    <div class="search-result-item {{ $isAlreadyAdded ? 'disabled' : '' }}"
                                                         wire:click="{{ $isAlreadyAdded ? '' : "selectMainAgent('{$result['id']}')" }}">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <div class="agent-name">{{ $result['name'] }}</div>
                                                                <div class="agent-email">{{ $result['email'] }}</div>
                                                            </div>
                                                            @if($isAlreadyAdded)
                                                                <span class="badge bg-secondary">Already Added</span>
                                                            @else
                                                                <i class="feather icon-check text-success"></i>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @elseif($isSearching && strlen($searchQuery) >= 2)
                                            <div class="search-dropdown">
                                                <div class="search-result-item">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </div>
                                                        <span class="text-muted">Searching...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        @elseif(strlen($searchQuery) >= 2 && !$isSearching)
                                            <div class="search-dropdown">
                                                <div class="search-result-item">
                                                    <div class="text-center text-muted">
                                                        <i class="feather icon-search me-2"></i>
                                                        No main agents found matching "{{ $searchQuery }}"
                                                        <br><small>Try a different search term or check if main agents exist</small>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Selected User Display -->
                                    @if($selectedMainAgent)
                                        @php
                                            $isAlreadyAdded = $this->isMainAgentAlreadyAdded($selectedMainAgent->id);
                                        @endphp
                                        <div class="selected-agent-display">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>Selected:</strong> {{ $selectedMainAgent->name }} ({{ $selectedMainAgent->email }})
                                                    @if($isAlreadyAdded)
                                                        <span class="badge bg-secondary ms-2">Already Added</span>
                                                    @endif
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" wire:click="clearSelection">
                                                    <i class="feather icon-x"></i>
                                                </button>
                                            </div>
                                            @if(!$isAlreadyAdded)
                                                <div class="mt-2">
                                                    <button wire:click="addEmail" class="btn btn-primary" type="button">
                                                        <i class="feather icon-plus me-1"></i>
                                                        Add Main Agent
                                                    </button>
                                                </div>
                                            @else
                                                <div class="mt-2">
                                                    <button class="btn btn-secondary" type="button" disabled>
                                                        <i class="feather icon-check me-1"></i>
                                                        Already Added
                                                    </button>
                                                </div>
                                            @endif
                                        </div>
                                    @endif

                                    <div class="form-text">
                                        Search for main agents above to add them to the notification list.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Email List -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Email</th>
                                        <th>User Name</th>
                                        <th>Status</th>
                                        <th>Sent At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($emailNotifications as $notification)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="feather icon-mail text-muted me-2"></i>
                                                    <div>
                                                        <strong>{{ $notification->email }}</strong>
                                                        @if($notification->mainAgent)
                                                            <br><small class="text-muted">Registered User</small>
                                                        @else
                                                            <br><small class="text-warning">Not a registered user</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($notification->mainAgent)
                                                    {{ $notification->mainAgent->name }}
                                                @elseif($notification->main_agent_name)
                                                    {{ $notification->main_agent_name }}
                                                @else
                                                    <span class="text-muted">Unknown</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($notification->status === 'pending')
                                                    <span class="badge bg-warning email-status-badge">Pending</span>
                                                @elseif($notification->status === 'sent')
                                                    <span class="badge bg-success email-status-badge">Sent</span>
                                                @elseif($notification->status === 'failed')
                                                    <span class="badge bg-danger email-status-badge">Failed</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($notification->sent_at)
                                                    <small class="text-muted">{{ $notification->sent_at->format('M d, Y H:i') }}</small>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="email-actions">
                                                    @if($notification->status === 'sent')
                                                        <button wire:click="resendEmail({{ $notification->id }})" class="btn btn-sm btn-outline-primary" title="Resend Email">
                                                            <i class="feather icon-refresh-cw"></i>
                                                        </button>
                                                    @elseif($notification->status === 'failed')
                                                        <button wire:click="resendEmail({{ $notification->id }})" class="btn btn-sm btn-outline-warning" title="Retry Failed Email">
                                                            <i class="feather icon-rotate-ccw"></i>
                                                        </button>
                                                    @endif
                                                    <button wire:click="removeEmail({{ $notification->id }})" class="btn btn-sm btn-outline-danger" title="Remove Email" onclick="return confirm('Are you sure you want to remove this email?')">
                                                        <i class="feather icon-trash-2"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        @if($notification->status === 'failed' && $notification->error_message)
                                            <tr>
                                                <td colspan="5">
                                                    <div class="mb-0 alert alert-danger">
                                                        <strong>Error:</strong> {{ $notification->error_message }}
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                    @empty
                                        <tr>
                                            <td colspan="5" class="py-4 text-center">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="mb-2 feather icon-mail text-muted" style="font-size: 3rem;"></i>
                                                    <h6 class="text-muted">No email notifications found</h6>
                                                    <p class="mb-0 text-muted">Add user email addresses above to send notifications.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($emailNotifications->hasPages())
                            <div class="mt-3 d-flex justify-content-center">
                                {{ $emailNotifications->links() }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle clicking outside search dropdown
            document.addEventListener('click', function(event) {
                const searchContainer = document.querySelector('.search-input-container');
                const searchDropdown = document.querySelector('.search-dropdown');

                if (searchContainer && !searchContainer.contains(event.target) && searchDropdown) {
                    // Clear search results when clicking outside
                    @this.call('clearSearchResults');
                }
            });

            // Handle escape key to clear search
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    @this.call('clearSearchResults');
                }
            });
        });
    </script>
</div>
