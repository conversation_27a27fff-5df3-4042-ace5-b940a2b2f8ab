<div>
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Change Password</h2>
                    </div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url('admin/dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">Change Password</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Card -->
    <div class="row">
        <div class="col-lg-8 col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ph-duotone ph-lock-key me-2"></i>
                        Update Your Password
                    </h5>
                </div>
                <div class="card-body">
                    @if (session()->has('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="ph-duotone ph-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session()->has('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="ph-duotone ph-warning-circle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form wire:submit.prevent="changePassword">
                        <!-- Current Password -->
                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                Current Password <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="ph-duotone ph-lock"></i>
                                </span>
                                <input type="password"
                                       class="form-control @error('current_password') is-invalid @enderror"
                                       id="current_password"
                                       wire:model="current_password"
                                       placeholder="Enter your current password">
                            </div>
                            @error('current_password')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- New Password -->
                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                New Password <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="ph-duotone ph-lock-key"></i>
                                </span>
                                <input type="password"
                                       class="form-control @error('new_password') is-invalid @enderror"
                                       id="new_password"
                                       wire:model="new_password"
                                       placeholder="Enter your new password">
                            </div>
                            @error('new_password')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <i class="ph-duotone ph-info me-1"></i>
                                Password must be at least 8 characters long
                            </div>
                        </div>

                        <!-- Confirm New Password -->
                        <div class="mb-4">
                            <label for="new_password_confirmation" class="form-label">
                                Confirm New Password <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="ph-duotone ph-lock-key"></i>
                                </span>
                                <input type="password"
                                       class="form-control @error('new_password_confirmation') is-invalid @enderror"
                                       id="new_password_confirmation"
                                       wire:model="new_password_confirmation"
                                       placeholder="Confirm your new password">
                            </div>
                            @error('new_password_confirmation')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex gap-2">
                            <button type="submit"
                                    class="btn btn-primary"
                                    wire:loading.attr="disabled">
                                <span wire:loading.remove>
                                    <i class="ph-duotone ph-check-circle me-2"></i>
                                    Change Password
                                </span>
                                <span wire:loading>
                                    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    Changing Password...
                                </span>
                            </button>
                            <a href="{{ url('admin/dashboard') }}" class="btn btn-secondary">
                                <i class="ph-duotone ph-arrow-left me-2"></i>
                                Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tips Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="ph-duotone ph-shield-check me-2"></i>
                        Password Security Tips
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="ph-duotone ph-check-circle text-success me-2"></i>
                            Use at least 8 characters
                        </li>
                        <li class="mb-2">
                            <i class="ph-duotone ph-check-circle text-success me-2"></i>
                            Include uppercase and lowercase letters
                        </li>
                        <li class="mb-2">
                            <i class="ph-duotone ph-check-circle text-success me-2"></i>
                            Add numbers and special characters
                        </li>
                        <li class="mb-2">
                            <i class="ph-duotone ph-check-circle text-success me-2"></i>
                            Avoid using personal information
                        </li>
                        <li class="mb-0">
                            <i class="ph-duotone ph-check-circle text-success me-2"></i>
                            Don't reuse passwords from other accounts
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
