<div>
    <style>
        .table-responsive {
            overflow-x: auto !important;
            overflow-y: visible !important;
        }

        .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }

        .dropdown {
            position: static !important;
        }

        .card {
            overflow: visible !important;
        }
    </style>
    {{-- <PERSON> Header --}}
    <div class="page-header commission-page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Organization Hierarchy</h2>
                        <p class="text-muted mb-0 mt-2">Manage cooperatives, branches, and their hierarchical agent structure</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('message') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- Statistics Cards --}}
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-primary">
                            <i class="ph-duotone ph-handshake"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Total Cooperatives</p>
                            <h4 class="mb-0 fw-bold">{{ number_format($totalCooperatives) }}</h4>
                            <small class="text-primary">Active organizations</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-warning">
                            <i class="ph-duotone ph-buildings"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Total Branches</p>
                            <h4 class="mb-0 fw-bold">{{ number_format($totalBranches) }}</h4>
                            <small class="text-warning">Registered locations</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-success">
                            <i class="ph-duotone ph-users"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Total Agents</p>
                            <h4 class="mb-0 fw-bold">{{ number_format($totalAgents) }}</h4>
                            <small class="text-success">Main agents & downlines</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Main Content Card --}}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="page-header-title">
                    <h2 class="mb-0">Organization Tree Structure</h2>
                    <p class="text-muted mb-0 mt-1">Hierarchical view of cooperatives, branches, and agent networks</p>
                </div>
                <div class="page-header-breadcrumb">
                    <button wire:click="toggleFilters" class="btn btn-outline-secondary me-2">
                        <i class="ph-duotone ph-funnel me-2"></i>
                        {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
                    </button>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" wire:click="expandAll">
                            <i class="ph-duotone ph-plus-circle me-2"></i>Expand All
                        </button>
                        <button type="button" class="btn btn-outline-secondary" wire:click="collapseAll">
                            <i class="ph-duotone ph-minus-circle me-2"></i>Collapse All
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {{-- Advanced Filters Panel --}}
        @if ($showFilters)
            <div class="card-body border-bottom">
                <div>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Search Organization</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="ph-duotone ph-magnifying-glass"></i>
                                </span>
                                <input type="text" wire:model.live="search" class="form-control"
                                       placeholder="Search cooperatives, branches, or agents...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Filter by Cooperative</label>
                            <select wire:model.live="selectedCooperativeId" class="form-select">
                                <option value="">All Cooperatives</option>
                                @foreach(App\Models\AcsCooperative::orderBy('name')->get() as $coop)
                                    <option value="{{ $coop->id }}">{{ $coop->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Agent Status Filter</label>
                            <select wire:model.live="statusFilter" class="form-select">
                                @foreach($userStatuses as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button wire:click="clearFilters" class="btn btn-outline-secondary">
                                <i class="ph-duotone ph-x me-2"></i>Clear Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <div class="card-body" style="overflow: visible;">

            {{-- Organization Tree --}}
            <div class="organization-tree">
                        @if($cooperatives->count() > 0)
                            @foreach($cooperatives as $cooperative)
                                <div class="mb-3 hierarchy-item cooperative-item" wire:key="coop-{{ $cooperative->id }}">
                                    <!-- Cooperative Header -->
                                    <div class="border card">
                                        <div class="card-header bg-light">
                                            <div class="d-flex align-items-center">
                                                <button type="button"
                                                        class="p-0 btn btn-sm btn-link me-2"
                                                        wire:click="toggleCooperative({{ $cooperative->id }})">
                                                    <i class="ph-duotone {{ in_array($cooperative->id, $expandedCooperatives) ? 'ph-caret-down' : 'ph-caret-right' }} f-16"></i>
                                                </button>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-0">
                                                        <i class="ph-duotone ph-handshake text-primary me-2"></i>
                                                        Cooperative : {{ $cooperative->name }}
                                                    </h6>
                                                    <small class="text-muted">
                                                        {{ $cooperative->branches_count }} {{ Str::plural('branch', $cooperative->branches_count) }} •
                                                        {{ $cooperative->users_count }} main {{ Str::plural('agent', $cooperative->users_count) }}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>

                                        @if(in_array($cooperative->id, $expandedCooperatives))
                                            <div class="card-body">
                                                @php
                                                    $branches = $this->getBranchesForCooperative($cooperative->id);
                                                    $mainAgents = $this->getMainAgentsForCooperative($cooperative->id);
                                                @endphp

                                                <!-- Main Agents Section for Cooperative -->
                                                @if($mainAgents->count() > 0)
                                                    <div class="mb-4 main-agent-section">
                                                        <div class="mb-3 d-flex align-items-center">
                                                            <h6 class="mb-0 text-primary">
                                                                <i class="ph-duotone ph-crown me-2"></i>
                                                                {{-- Main {{ Str::plural('Agent', $mainAgents->count()) }} for {{ $cooperative->name }} --}}
                                                                PIC for {{ $cooperative->name }}
                                                            </h6>
                                                            <span class="badge bg-primary ms-2">{{ $mainAgents->count() }}</span>
                                                        </div>

                                                        <div class="row">
                                                            @foreach($mainAgents as $mainAgent)
                                                                <div class="mb-2 col-md-6" wire:key="main-agent-{{ $mainAgent->id }}">
                                                                    <div class="border-2 card border-primary main-agent-card">
                                                                        <div class="p-3 card-body">
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="avtar avtar-s bg-primary me-3">
                                                                                    <i class="text-white ph-duotone ph-crown f-16"></i>
                                                                                </div>
                                                                                <div class="flex-grow-1">
                                                                                    <h6 class="mb-1 fw-bold">{{ $mainAgent->name }}</h6>
                                                                                    <p class="mb-0 text-muted small">{{ $mainAgent->email }}</p>
                                                                                    <small class="text-muted">{{ $mainAgent->phone }}</small>
                                                                                </div>
                                                                                <div class="flex-shrink-0">
                                                                                    <span class="badge {{ $this->getStatusBadgeClass($mainAgent->status) }} mb-1">
                                                                                        {{ str_replace('_', ' ', ucfirst($mainAgent->status)) }}
                                                                                    </span>

                                                                                    <div class="dropdown">
                                                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                                                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                            <i class="ph-duotone ph-dots-three-vertical"></i>
                                                                                        </button>
                                                                                        <ul class="dropdown-menu dropdown-menu-end">
                                                                                            <li>
                                                                                                <button wire:click="viewAgent({{ $mainAgent->id }})"
                                                                                                    class="dropdown-item">
                                                                                                    <i class="ph-duotone ph-eye me-2"></i>View Details
                                                                                                </button>
                                                                                            </li>
                                                                                            <li>
                                                                                                <button wire:click="editAgent({{ $mainAgent->id }})"
                                                                                                    class="dropdown-item">
                                                                                                    <i class="ph-duotone ph-pencil me-2"></i>Edit Agent
                                                                                                </button>
                                                                                            </li>
                                                                                        </ul>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @endif

                                                @if($branches->count() > 0)
                                                    @foreach($branches as $branch)
                                                        @if($branch->users_count > 0)
                                                            <div class="mb-2 hierarchy-item branch-item ms-3" wire:key="branch-{{ $branch->id }}">
                                                            <!-- Branch Header -->
                                                            <div class="card border-start border-3">
                                                                <div class="card-header bg-light">
                                                                    <div class="d-flex align-items-center">
                                                                        <button type="button"
                                                                                class="p-0 btn btn-sm btn-link me-2"
                                                                                wire:click="toggleBranch({{ $branch->id }})">
                                                                            <i class="ph-duotone {{ in_array($branch->id, $expandedBranches) ? 'ph-caret-down' : 'ph-caret-right' }} f-16"></i>
                                                                        </button>
                                                                        <div class="flex-grow-1">
                                                                            <h6 class="mb-0">
                                                                                <i class="ph-duotone ph-buildings text-warning me-2"></i>
                                                                                Branch : {{ $branch->name }}
                                                                            </h6>
                                                                            <small class="text-muted">
                                                                                {{ $branch->users_count }} {{ Str::plural('agent', $branch->users_count) }}
                                                                                @if($branch->business_registration_no)
                                                                                    • Reg: {{ $branch->business_registration_no }}
                                                                                @endif
                                                                            </small>
                                                                        </div>
                                                                        <div class="flex-shrink-0">
                                                                            <span class="badge {{ $branch->status == 'active' ? 'bg-success' : ($branch->status == 'pending' ? 'bg-warning' : 'bg-secondary') }}">
                                                                                {{ ucfirst($branch->status) }}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                @if(in_array($branch->id, $expandedBranches))
                                                                    <div class="card-body">
                                                                        @php
                                                                            $agentsData = $this->getAgentsForBranch($branch->id);
                                                                            $agents = $agentsData['agents'];
                                                                            $agentsCount = $agentsData['agents_count'];
                                                                            $isShowingAll = $this->isShowingAllAgents($branch->id);
                                                                        @endphp

                                                                        @if($agentsCount > 0)
                                                                            <!-- Agent Summary Bar -->
                                                                            <div class="p-2 mb-3 rounded d-flex justify-content-between align-items-center bg-light">
                                                                                <div>
                                                                                    <small class="text-muted">
                                                                                        Showing {{ $agents->count() }} of {{ number_format($agentsCount) }} agents
                                                                                    </small>
                                                                                </div>
                                                                                <div class="btn-group btn-group-sm">
                                                                                    @if($agentsCount > 5 && !$isShowingAll)
                                                                                        <button type="button"
                                                                                                class="btn btn-outline-primary btn-sm"
                                                                                                wire:click="showAllAgents({{ $branch->id }})">
                                                                                            <i class="ph-duotone ph-eye"></i> View All
                                                                                        </button>
                                                                                    @elseif($isShowingAll)
                                                                                        <button type="button"
                                                                                                class="btn btn-outline-secondary btn-sm"
                                                                                                wire:click="showLimitedAgents({{ $branch->id }})">
                                                                                            <i class="ph-duotone ph-eye-slash"></i> Show Less
                                                                                        </button>
                                                                                    @endif

                                                                                    <button type="button"
                                                                                            class="btn btn-outline-info btn-sm"
                                                                                            wire:click="toggleAgentSearch({{ $branch->id }})">
                                                                                        <i class="ph-duotone ph-magnifying-glass"></i> Search
                                                                                    </button>
                                                                                </div>
                                                                            </div>

                                                                            <!-- Agent Search (conditionally shown) -->
                                                                            @if($this->isAgentSearchVisible($branch->id))
                                                                                <div class="mb-3">
                                                                                    <div class="row">
                                                                                        <div class="col-md-8">
                                                                                            <input type="text"
                                                                                                   wire:model.live="agentSearch.{{ $branch->id }}"
                                                                                                   class="form-control form-control-sm"
                                                                                                   placeholder="Search agents in this branch...">
                                                                                        </div>
                                                                                        <div class="col-md-4">
                                                                                            <select wire:model.live="agentStatusFilter.{{ $branch->id }}"
                                                                                                    class="form-select form-select-sm">
                                                                                                <option value="">All Status</option>
                                                                                                @foreach($userStatuses as $value => $label)
                                                                                                    <option value="{{ $value }}">{{ $label }}</option>
                                                                                                @endforeach
                                                                                            </select>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            @endif

                                                                            <!-- Regular Agents Section -->
                                                                            @if($agentsCount > 0)
                                                                                <div class="mb-3 regular-agent-section">
                                                                                    <div class="mb-2 d-flex align-items-center">
                                                                                        <h6 class="mb-0 text-info">
                                                                                            <i class="ph-duotone ph-users me-2"></i>
                                                                                            {{ Str::plural('Agent', $agentsCount) }} for {{ $branch->name }}
                                                                                        </h6>
                                                                                        <span class="badge bg-info ms-2">{{ $agentsCount }}</span>
                                                                                    </div>
                                                                                </div>
                                                                            @endif

                                                                            <!-- Agents Grid/List -->
                                                                            @if($agentsCount > 0 && ($agentsCount <= 20 || $isShowingAll))
                                                                                <!-- Table view for detailed information -->
                                                                                <div class="table-responsive">
                                                                                    <table class="table table-sm table-hover">
                                                                                        <thead class="table-light">
                                                                                            <tr>
                                                                                                <th>Agent</th>
                                                                                                <th>Role</th>
                                                                                                <th>Status</th>
                                                                                                <th>Contact</th>
                                                                                                <th>Actions</th>
                                                                                            </tr>
                                                                                        </thead>
                                                                                        <tbody>
                                                                                            @foreach($agents as $agent)
                                                                                                <tr wire:key="agent-{{ $agent->id }}">
                                                                                                    <td>
                                                                                                        <div class="d-flex align-items-center">
                                                                                                            <div class="avtar avtar-xs bg-light-info me-2">
                                                                                                                <i class="ph-duotone ph-user f-12"></i>
                                                                                                            </div>
                                                                                                            <div>
                                                                                                                <h6 class="mb-0">{{ $agent->name }}</h6>
                                                                                                                <small class="text-muted">{{ $agent->username }}</small>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <span class="badge bg-info">
                                                                                                            Agent
                                                                                                        </span>
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <span class="badge {{ $this->getStatusBadgeClass($agent->status) }}">
                                                                                                            {{ ucfirst($agent->status) }}
                                                                                                        </span>
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <div>
                                                                                                            <small>{{ $agent->email }}</small><br>
                                                                                                            <small>{{ $agent->phone ?? 'No phone' }}</small>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <div class="dropdown">
                                                                                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                                                                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                                                                <i class="ph-duotone ph-dots-three-vertical"></i>
                                                                                                            </button>
                                                                                                            <ul class="dropdown-menu dropdown-menu-end">
                                                                                                                <li>
                                                                                                                    <button wire:click="viewAgent({{ $agent->id }})"
                                                                                                                        class="dropdown-item">
                                                                                                                        <i class="ph-duotone ph-eye me-2"></i>View Details
                                                                                                                    </button>
                                                                                                                </li>
                                                                                                                <li>
                                                                                                                    <button wire:click="editAgent({{ $agent->id }})"
                                                                                                                        class="dropdown-item">
                                                                                                                        <i class="ph-duotone ph-pencil me-2"></i>Edit Agent
                                                                                                                    </button>
                                                                                                                </li>
                                                                                                            </ul>
                                                                                                        </div>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            @endforeach
                                                                                        </tbody>
                                                                                    </table>
                                                                                </div>
                                                                            @elseif($agentsCount > 0)
                                                                                <!-- Card view for quick overview -->
                                                                                <div class="row">
                                                                                    @foreach($agents as $agent)
                                                                                        <div class="mb-2 col-md-6" wire:key="agent-card-{{ $agent->id }}">
                                                                                            <div class="p-2 border card card-body">
                                                                                                <div class="d-flex align-items-center">
                                                                                                    <div class="avtar avtar-xs bg-light-info me-2">
                                                                                                        <i class="ph-duotone ph-user f-12"></i>
                                                                                                    </div>
                                                                                                    <div class="flex-grow-1">
                                                                                                        <h6 class="mb-0">{{ $agent->name }}</h6>
                                                                                                        <small class="text-muted">{{ $agent->username }}</small>
                                                                                                        <br><small class="text-muted">{{ $agent->email }}</small>
                                                                                                    </div>
                                                                                                    <div class="flex-shrink-0">
                                                                                                        <span class="badge bg-info badge-sm me-1">
                                                                                                            Agent
                                                                                                        </span>
                                                                                                        <span class="badge {{ $this->getStatusBadgeClass($agent->status) }} badge-sm">
                                                                                                            {{ ucfirst($agent->status) }}
                                                                                                        </span>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    @endforeach
                                                                                </div>
                                                                            @endif

                                                                            <!-- Pagination for large datasets -->
                                                                            @if($agentsCount > 0 && $isShowingAll && $agentsData['pagination'])
                                                                                <div class="mt-3 d-flex justify-content-between align-items-center">
                                                                                    <div>
                                                                                        <small class="text-muted">
                                                                                            Page {{ $agentsData['pagination']['current_page'] }} of {{ $agentsData['pagination']['last_page'] }}
                                                                                        </small>
                                                                                    </div>
                                                                                    <div class="btn-group btn-group-sm">
                                                                                        <button type="button"
                                                                                                class="btn btn-outline-secondary"
                                                                                                wire:click="previousAgentPage({{ $branch->id }})"
                                                                                                {{ $agentsData['pagination']['current_page'] <= 1 ? 'disabled' : '' }}>
                                                                                            <i class="ph-duotone ph-caret-left"></i>
                                                                                        </button>
                                                                                        <button type="button"
                                                                                                class="btn btn-outline-secondary"
                                                                                                wire:click="nextAgentPage({{ $branch->id }})"
                                                                                                {{ $agentsData['pagination']['current_page'] >= $agentsData['pagination']['last_page'] ? 'disabled' : '' }}>
                                                                                            <i class="ph-duotone ph-caret-right"></i>
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            @endif

                                                                        @endif

                                                                        @if($agentsCount == 0)
                                                                            <div class="py-3 text-center">
                                                                                <div class="mx-auto mb-2 avtar avtar-m bg-light-secondary">
                                                                                    <i class="ph-duotone ph-users"></i>
                                                                                </div>
                                                                                <p class="mb-0 text-muted">No agents found in this branch</p>
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        @endif
                                                    @endforeach
                                                @else
                                                    <div class="py-3 text-center">
                                                        <div class="mx-auto mb-2 avtar avtar-m bg-light-secondary">
                                                            <i class="ph-duotone ph-buildings"></i>
                                                        </div>
                                                        <p class="mb-0 text-muted">No branches found in this cooperative</p>
                                                    </div>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="py-5 text-center">
                                <div class="mx-auto mb-3 avtar avtar-xl bg-light-secondary">
                                    <i class="ph-duotone ph-handshake"></i>
                                </div>
                                <h5>No Organization Data Found</h5>
                                <p class="text-muted">No cooperatives match your current search criteria or filters.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.hierarchy-item {
    position: relative;
}

.organization-tree .card {
    margin-bottom: 0;
}

.organization-tree .card .card-header {
    cursor: pointer;
}

.organization-tree .branch-item .card {
    border-left-color: var(--bs-warning) !important;
    border-left-width: 3px !important;
}

.organization-tree .cooperative-item .card-header {
    background-color: var(--bs-light) !important;
}

.organization-tree .branch-item .card-header {
    background-color: var(--bs-gray-100) !important;
}

.btn-link {
    text-decoration: none;
}

.btn-link:hover {
    text-decoration: none;
}

.badge-sm {
    font-size: 0.65em;
}

.table-hover tbody tr:hover {
    background-color: var(--bs-gray-50);
}

.agent-summary-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 3px solid var(--bs-primary);
}

.card-body .card {
    transition: all 0.2s ease;
}

.card-body .card:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.main-agent-card {
    position: relative;
    background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
}

.main-agent-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--bs-primary) 0%, var(--bs-info) 100%);
    border-radius: 0.375rem 0.375rem 0 0;
}

.main-agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
}

.main-agent-section h6 {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.regular-agent-section h6 {
    font-weight: 600;
    letter-spacing: 0.5px;
}
</style>
@endpush
