<div>
    <style>
        .modal.show {
            display: block !important;
        }

        .announcement-content {
            white-space: pre-wrap;
            line-height: 1.6;
        }

        .status-badge {
            font-size: 0.75rem;
        }

        .nav-tabs .nav-link {
            color: #6c757d;
            border: none;
            border-bottom: 2px solid transparent;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            color: #0d6efd;
            border-bottom-color: #0d6efd;
            background-color: transparent;
        }

        .nav-tabs .nav-link:hover {
            color: #0d6efd;
            border-bottom-color: rgba(13, 110, 253, 0.3);
        }

        .zepto-card {
            transition: all 0.3s ease;
        }

        .zepto-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .email-status-delivered {
            color: #198754;
        }

        .email-status-failed {
            color: #dc3545;
        }

        .email-status-pending {
            color: #fd7e14;
        }

        .email-status-bounced {
            color: #6f42c1;
        }
    </style>

    {{-- Page Header --}}
    <div class="page-header commission-page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Announcement Management</h2>
                        <p class="text-muted mb-0 mt-2">Create and manage announcements for different user roles and track email delivery status</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="mb-4 card">
        <div class="card-body">
            <ul class="nav nav-tabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button
                        wire:click="setActiveTab('announcements')"
                        class="nav-link @if($activeTab === 'announcements') active @endif"
                        type="button"
                    >
                        <i class="ph-duotone ph-megaphone me-2"></i>
                        Announcements
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button
                        wire:click="setActiveTab('zepto')"
                        class="nav-link @if($activeTab === 'zepto') active @endif"
                        type="button"
                    >
                        <i class="ph-duotone ph-chart-line me-2"></i>
                        Email Delivery Status (Zepto)
                    </button>
                </li>
            </ul>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-success" style="border-radius: 8px;">
                        <i class="ph-duotone ph-check-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-success mb-1">Success!</div>
                    <div class="text-dark">{{ session('success') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show border-0" role="alert" style="border-radius: 12px;">
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <div class="avtar avtar-s bg-danger" style="border-radius: 8px;">
                        <i class="ph-duotone ph-x-circle"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold text-danger mb-1">Error!</div>
                    <div class="text-dark">{{ session('error') }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    @endif

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Announcements Tab -->
        <div class="tab-pane @if($activeTab === 'announcements') show active @endif">
            {{-- Main Content Card --}}
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="page-header-title">
                            <h2 class="mb-0">Announcement Directory</h2>
                            @if ($announcements->total() > 0)
                                <p class="text-muted mb-0 mt-1">Showing {{ $announcements->firstItem() }} to
                                    {{ $announcements->lastItem() }} of {{ $announcements->total() }} results</p>
                            @endif
                        </div>
                        <div class="page-header-breadcrumb">
                            <button wire:click="toggleFilters" class="btn btn-outline-secondary me-2">
                                <i class="ph-duotone ph-funnel me-2"></i>
                                {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
                            </button>
                            <button wire:click="openBulkEmailModal" class="btn btn-outline-info me-2">
                                <i class="ph-duotone ph-envelope me-2"></i>
                                Send All Active
                            </button>
                            <button wire:click="openCreateModal" class="btn btn-primary">
                                <i class="ph-duotone ph-plus me-2"></i>Create Announcement
                            </button>
                        </div>
                    </div>
                </div>

                {{-- Advanced Filters Panel --}}
                @if ($showFilters)
                    <div class="card-body border-bottom">
                        <div>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">Search Announcements</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="ph-duotone ph-magnifying-glass"></i>
                                        </span>
                                        <input wire:model.live="search" type="text" class="form-control" placeholder="Search announcements...">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Filter by Role</label>
                                    <select wire:model.live="roleFilter" class="form-select">
                                        <option value="">All Roles</option>
                                        @foreach($availableRoles as $role)
                                            <option value="{{ $role->id }}">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Status Filter</label>
                                    <select wire:model.live="statusFilter" class="form-select">
                                        <option value="">All Status</option>
                                        <option value="1">Active</option>
                                        <option value="0">Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Per Page</label>
                                    <select wire:model.live="perPage" class="form-select">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <button wire:click="clearFilters" class="btn btn-outline-secondary">
                                        <i class="ph-duotone ph-x me-2"></i>Clear Filters
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="card-body" style="overflow: visible;">
                    {{-- Announcement Table --}}
                    <div class="table-responsive" style="overflow: visible;">
                        <table class="table table-hover" id="announcementTable">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="d-flex align-items-center">
                                            Announcement
                                            <i class="ph-duotone ph-megaphone ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th>
                                        <div class="d-flex align-items-center">
                                            Target Role
                                            <i class="ph-duotone ph-user-circle ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th>
                                        <div class="d-flex align-items-center">
                                            Status & Delivery
                                            <i class="ph-duotone ph-info ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th>
                                        <div class="d-flex align-items-center">
                                            Created Date
                                            <i class="ph-duotone ph-calendar ms-1 text-muted"></i>
                                        </div>
                                    </th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($announcements as $announcement)
                                    <tr id="announcement-row-{{ $announcement->id }}" class="align-middle">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-primary">
                                                    <i class="ph-duotone ph-megaphone"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <h6 class="mb-0">{{ $announcement->title }}</h6>
                                                    <small class="text-muted">{{ Str::limit($announcement->content, 80) }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avtar avtar-s bg-light-info">
                                                    <i class="ph-duotone ph-user-circle"></i>
                                                </div>
                                                <div class="ms-2">
                                                    <span class="badge bg-primary">
                                                        {{ ucfirst(str_replace('-', ' ', $announcement->role->name)) }}
                                                    </span>
                                                    <small class="text-muted d-block">Target audience</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                @if($announcement->status == 1)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                                @if($announcement->email_sent)
                                                    <span class="badge bg-info">
                                                        <i class="ph-duotone ph-envelope me-1"></i>
                                                        Email Sent
                                                    </span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-medium">{{ $announcement->created_at->format('M d, Y') }}</span>
                                            <small class="text-muted d-block">{{ $announcement->created_at->format('H:i') }}</small>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ph-duotone ph-dots-three-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li>
                                                        <button wire:click="openViewModal({{ $announcement->id }})"
                                                            class="dropdown-item">
                                                            <i class="ph-duotone ph-eye me-2"></i>View Details
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button wire:click="openEditModal({{ $announcement->id }})"
                                                            class="dropdown-item">
                                                            <i class="ph-duotone ph-pencil me-2"></i>Edit
                                                        </button>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button wire:click="openEmailModal({{ $announcement->id }})"
                                                            class="dropdown-item text-info">
                                                            <i class="ph-duotone ph-envelope me-2"></i>Send Email
                                                        </button>
                                                    </li>
                                                    @if($announcement->email_sent)
                                                        <li>
                                                            <button wire:click="openEmailDeliveryModal({{ $announcement->id }})"
                                                                class="dropdown-item text-success">
                                                                <i class="ph-duotone ph-chart-line me-2"></i>Delivery Status
                                                            </button>
                                                        </li>
                                                    @endif
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button wire:click="openDeleteModal({{ $announcement->id }})"
                                                            class="dropdown-item text-danger">
                                                            <i class="ph-duotone ph-trash me-2"></i>Delete
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <div class="py-4 text-center">
                                                <div class="mx-auto mb-3 avtar avtar-xl bg-light-secondary">
                                                    <i class="ph-duotone ph-megaphone"></i>
                                                </div>
                                                <h5>No Announcements Found</h5>
                                                <p class="text-muted">Start by creating your first announcement to communicate with users.</p>
                                                <button wire:click="openCreateModal" class="btn btn-primary">
                                                    <i class="ph-duotone ph-plus me-2"></i>Create Announcement
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    {{-- Pagination --}}
                    @if ($announcements->hasPages())
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div class="text-muted">
                                Showing {{ $announcements->firstItem() }} to {{ $announcements->lastItem() }} of
                                {{ $announcements->total() }} results
                            </div>
                            <div>
                                {{ $announcements->links('pagination::bootstrap-4') }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Zepto Email Status Tab -->
        <div class="tab-pane @if($activeTab === 'zepto') show active @endif">
            <!-- Zepto Filters -->
            <div class="mb-4 card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ph-duotone ph-funnel me-2"></i>
                        Email Delivery Filters
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="zeptoEmailAddress" class="form-label">Email Address (Optional)</label>
                            <input wire:model="zeptoEmailAddress" type="email" class="form-control" id="zeptoEmailAddress" placeholder="Search specific email...">
                        </div>
                        <div class="col-md-3">
                            <label for="zeptoStartDate" class="form-label">Start Date</label>
                            <input wire:model="zeptoStartDate" type="date" class="form-control" id="zeptoStartDate">
                        </div>
                        <div class="col-md-3">
                            <label for="zeptoEndDate" class="form-label">End Date</label>
                            <input wire:model="zeptoEndDate" type="date" class="form-control" id="zeptoEndDate">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="gap-2 d-flex">
                                <button wire:click="loadZeptoData" class="btn btn-primary" @if($zeptoLoading) disabled @endif>
                                    @if($zeptoLoading)
                                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    @else
                                        <i class="ph-duotone ph-magnifying-glass me-2"></i>
                                    @endif
                                    Search
                                </button>
                                <button wire:click="refreshZeptoData" class="btn btn-outline-secondary" @if($zeptoLoading) disabled @endif>
                                    <i class="ph-duotone ph-arrows-clockwise"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            @if($zeptoError)
                <div class="alert alert-danger">
                    <i class="ph-duotone ph-warning-circle me-2"></i>
                    {{ $zeptoError }}
                </div>
            @endif

            @if($zeptoLoading)
                <div class="card">
                    <div class="py-5 text-center card-body">
                        <div class="mb-3 spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mb-0">Loading email delivery data from Zepto...</p>
                    </div>
                </div>
            @elseif($zeptoStats || $zeptoData || $zeptoBounces)
                <!-- Statistics Summary -->
                @if($zeptoStats)
                    <div class="mb-4 row">
                        <div class="col-md-3">
                            <div class="text-white zepto-card card bg-primary">
                                <div class="text-center card-body">
                                    <i class="mb-2 ph-duotone ph-envelope display-6"></i>
                                    <h4 class="card-title">{{ number_format($zeptoStats['total_sent']) }}</h4>
                                    <p class="card-text">Total Sent</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-white zepto-card card bg-success">
                                <div class="text-center card-body">
                                    <i class="mb-2 ph-duotone ph-check-circle display-6"></i>
                                    <h4 class="card-title">{{ number_format($zeptoStats['total_delivered']) }}</h4>
                                    <p class="card-text">Delivered</p>
                                    <small>{{ number_format($zeptoStats['delivery_rate'], 1) }}% rate</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-white zepto-card card bg-danger">
                                <div class="text-center card-body">
                                    <i class="mb-2 ph-duotone ph-x-circle display-6"></i>
                                    <h4 class="card-title">{{ number_format($zeptoStats['total_bounced']) }}</h4>
                                    <p class="card-text">Bounced</p>
                                    <small>{{ number_format($zeptoStats['bounce_rate'], 1) }}% rate</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-white zepto-card card bg-info">
                                <div class="text-center card-body">
                                    <i class="mb-2 ph-duotone ph-eye display-6"></i>
                                    <h4 class="card-title">{{ number_format($zeptoStats['total_opens']) }}</h4>
                                    <p class="card-text">Opens</p>
                                    <small>{{ number_format($zeptoStats['open_rate'], 1) }}% rate</small>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Email Details -->
                @if($zeptoData)
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="ph-duotone ph-list me-2"></i>
                                Email Delivery Details ({{ $zeptoData['total_emails'] }} emails)
                            </h5>
                            <button wire:click="exportZeptoData" class="btn btn-outline-success btn-sm">
                                <i class="ph-duotone ph-download me-1"></i>
                                Export CSV
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="mb-3 row">
                                <div class="col-md-3">
                                    <div class="p-3 text-center rounded bg-light">
                                        <div class="text-success h4">{{ $zeptoData['delivered'] }}</div>
                                        <small>Delivered</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 text-center rounded bg-light">
                                        <div class="text-danger h4">{{ $zeptoData['failed'] }}</div>
                                        <small>Failed</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 text-center rounded bg-light">
                                        <div class="text-warning h4">{{ $zeptoData['pending'] }}</div>
                                        <small>Pending</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="p-3 text-center rounded bg-light">
                                        <div class="text-purple h4">{{ $zeptoData['bounced'] }}</div>
                                        <small>Bounced</small>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light sticky-top">
                                        <tr>
                                            <th>Email</th>
                                            <th>Status</th>
                                            <th>Subject</th>
                                            <th>Sent At</th>
                                            <th>Delivered At</th>
                                            <th>Issues</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($zeptoData['emails'] as $email)
                                            <tr>
                                                <td>{{ $email['email'] }}</td>
                                                <td>
                                                    @php
                                                        $statusClass = match(strtolower($email['status'])) {
                                                            'delivered' => 'email-status-delivered',
                                                            'failed', 'rejected' => 'email-status-failed',
                                                            'pending', 'processing' => 'email-status-pending',
                                                            'bounced' => 'email-status-bounced',
                                                            default => ''
                                                        };
                                                    @endphp
                                                    <span class="fw-bold {{ $statusClass }}">
                                                        {{ ucfirst($email['status']) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <small>{{ Str::limit($email['subject'], 40) }}</small>
                                                </td>
                                                <td>
                                                    @if($email['sent_at'])
                                                        <small>{{ \Carbon\Carbon::parse($email['sent_at'])->format('M d, Y H:i') }}</small>
                                                    @else
                                                        <small class="text-muted">-</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($email['delivered_at'])
                                                        <small>{{ \Carbon\Carbon::parse($email['delivered_at'])->format('M d, Y H:i') }}</small>
                                                    @else
                                                        <small class="text-muted">-</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($email['bounce_reason'] || $email['error_message'])
                                                        <small class="text-danger" title="{{ $email['bounce_reason'] ?? $email['error_message'] }}">
                                                            {{ Str::limit($email['bounce_reason'] ?? $email['error_message'], 30) }}
                                                        </small>
                                                    @else
                                                        <small class="text-muted">-</small>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Bounce Reports -->
                @if($zeptoBounces && $zeptoBounces['total_bounces'] > 0)
                    <div class="mt-4 card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="ph-duotone ph-warning-circle me-2"></i>
                                Bounce Reports ({{ $zeptoBounces['total_bounces'] }} bounces)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3 row">
                                <div class="col-md-6">
                                    <div class="p-3 text-center rounded bg-light">
                                        <div class="text-danger h4">{{ $zeptoBounces['hard_bounces'] }}</div>
                                        <small>Hard Bounces</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="p-3 text-center rounded bg-light">
                                        <div class="text-warning h4">{{ $zeptoBounces['soft_bounces'] }}</div>
                                        <small>Soft Bounces</small>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table table-striped">
                                    <thead class="table-light sticky-top">
                                        <tr>
                                            <th>Email</th>
                                            <th>Type</th>
                                            <th>Reason</th>
                                            <th>Bounced At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($zeptoBounces['bounces'] as $bounce)
                                            <tr>
                                                <td>{{ $bounce['email'] }}</td>
                                                <td>
                                                    <span class="badge @if(strtolower($bounce['bounce_type']) === 'hard') bg-danger @else bg-warning @endif">
                                                        {{ ucfirst($bounce['bounce_type']) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <small>{{ Str::limit($bounce['bounce_reason'], 40) }}</small>
                                                </td>
                                                <td>
                                                    @if($bounce['bounced_at'])
                                                        <small>{{ \Carbon\Carbon::parse($bounce['bounced_at'])->format('M d, Y H:i') }}</small>
                                                    @else
                                                        <small class="text-muted">-</small>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            @else
                <!-- Empty State -->
                <div class="card">
                    <div class="py-5 text-center card-body">
                        <i class="mb-3 ph-duotone ph-chart-line display-4 text-muted"></i>
                        <h5 class="text-muted">No Email Data Available</h5>
                        <p class="mb-4 text-muted">
                            Set your date range and click "Search" to view email delivery status from Zepto Mail.
                        </p>
                        <button wire:click="loadZeptoData" class="btn btn-primary">
                            <i class="ph-duotone ph-magnifying-glass me-2"></i>
                            Load Email Data
                        </button>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Create Modal -->
    <div class="modal fade @if($showCreateModal) show @endif" @if($showCreateModal) style="display: block;" @endif tabindex="-1" wire:click.self="closeCreateModal">
        <div class="modal-dialog modal-lg" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="ph-duotone ph-plus-circle text-primary me-2"></i>
                        Create Announcement
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeCreateModal"></button>
                </div>
                <form wire:submit="store">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input wire:model="title" type="text" class="form-control @error('title') is-invalid @enderror" id="title" placeholder="Enter announcement title">
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="acs_role_id" class="form-label">Role <span class="text-danger">*</span></label>
                            <select wire:model="acs_role_id" class="form-select @error('acs_role_id') is-invalid @enderror" id="acs_role_id">
                                <option value="">Select Role</option>
                                @foreach($availableRoles as $role)
                                    <option value="{{ $role->id }}">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</option>
                                @endforeach
                            </select>
                            @error('acs_role_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                            <textarea wire:model="content" class="form-control @error('content') is-invalid @enderror" id="content" rows="6" placeholder="Enter announcement content"></textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Maximum 1000 characters. You can use line breaks for formatting.</div>
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select wire:model="status" class="form-select @error('status') is-invalid @enderror" id="status">
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeCreateModal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Announcement</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade @if($showEditModal) show @endif" @if($showEditModal) style="display: block;" @endif tabindex="-1" wire:click.self="closeEditModal">
        <div class="modal-dialog modal-lg" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="ph-duotone ph-pencil-circle text-warning me-2"></i>
                        Edit Announcement
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeEditModal"></button>
                </div>
                <form wire:submit="update">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input wire:model="title" type="text" class="form-control @error('title') is-invalid @enderror" id="edit_title" placeholder="Enter announcement title">
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="edit_acs_role_id" class="form-label">Role <span class="text-danger">*</span></label>
                            <select wire:model="acs_role_id" class="form-select @error('acs_role_id') is-invalid @enderror" id="edit_acs_role_id">
                                <option value="">Select Role</option>
                                @foreach($availableRoles as $role)
                                    <option value="{{ $role->id }}">{{ ucfirst(str_replace('-', ' ', $role->name)) }}</option>
                                @endforeach
                            </select>
                            @error('acs_role_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="edit_content" class="form-label">Content <span class="text-danger">*</span></label>
                            <textarea wire:model="content" class="form-control @error('content') is-invalid @enderror" id="edit_content" rows="6" placeholder="Enter announcement content"></textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Maximum 1000 characters. You can use line breaks for formatting.</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select wire:model="status" class="form-select @error('status') is-invalid @enderror" id="edit_status">
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeEditModal">Cancel</button>
                        <button type="submit" class="btn btn-warning">Update Announcement</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Modal -->
    <div class="modal fade @if($showViewModal) show @endif" @if($showViewModal) style="display: block;" @endif tabindex="-1" wire:click.self="closeViewModal">
        <div class="modal-dialog modal-lg" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="ph-duotone ph-eye-circle text-primary me-2"></i>
                        View Announcement
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeViewModal"></button>
                </div>
                <div class="modal-body">
                    @if($viewingAnnouncement)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Title</label>
                            <p class="mb-0">{{ $viewingAnnouncement->title }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Role</label>
                            <p class="mb-0">
                                <span class="badge bg-light-secondary text-dark">
                                    {{ ucfirst(str_replace('-', ' ', $viewingAnnouncement->role->name)) }}
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Status</label>
                            <p class="mb-0">
                                @if($viewingAnnouncement->status == 1)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-secondary">Inactive</span>
                                @endif
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Content</label>
                            <div class="p-3 rounded announcement-content bg-light">
                                {!! nl2br(e($viewingAnnouncement->content)) !!}
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Created</label>
                            <p class="mb-0">{{ $viewingAnnouncement->created_at->format('F d, Y \a\t H:i') }}</p>
                        </div>
                        @if($viewingAnnouncement->updated_at != $viewingAnnouncement->created_at)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Last Updated</label>
                                <p class="mb-0">{{ $viewingAnnouncement->updated_at->format('F d, Y \a\t H:i') }}</p>
                            </div>
                        @endif

                        @if($viewingAnnouncement->email_sent)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email Status</label>
                                <p class="mb-0">
                                    <span class="badge bg-info me-2">
                                        <i class="ph-duotone ph-envelope me-1"></i>
                                        Email sent on {{ $viewingAnnouncement->email_sent_at->format('F d, Y \a\t H:i') }}
                                    </span>
                                    <button wire:click="openEmailDeliveryModal({{ $viewingAnnouncement->id }})"
                                            class="btn btn-sm btn-outline-success">
                                        <i class="ph-duotone ph-chart-line me-1"></i>
                                        View Delivery Status
                                    </button>
                                </p>
                            </div>
                        @endif
                    @else
                        <div class="py-4 text-center text-muted">
                            <i class="ph-duotone ph-megaphone display-4"></i>
                            <p class="mt-2 mb-0">Announcement not found</p>
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeViewModal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade @if($showDeleteModal) show @endif" @if($showDeleteModal) style="display: block;" @endif tabindex="-1" wire:click.self="closeDeleteModal">
        <div class="modal-dialog" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="ph-duotone ph-warning-circle me-2"></i>
                        Delete Announcement
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeDeleteModal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this announcement? This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeDeleteModal">Cancel</button>
                    <button type="button" class="btn btn-danger" wire:click="delete">Delete Announcement</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Send Email Modal -->
    <div class="modal fade @if($showEmailModal) show @endif" @if($showEmailModal) style="display: block;" @endif tabindex="-1" wire:click.self="closeEmailModal">
        <div class="modal-dialog modal-lg" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-info">
                        <i class="ph-duotone ph-envelope-circle me-2"></i>
                        Send Announcement Email
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeEmailModal"></button>
                </div>
                <div class="modal-body">
                    @if($emailingAnnouncement)
                        <div class="mb-4">
                            <h6 class="fw-bold">Announcement Details:</h6>
                            <div class="p-3 rounded bg-light">
                                <h6 class="mb-2">{{ $emailingAnnouncement->title }}</h6>
                                <p class="mb-2 text-muted">{{ Str::limit($emailingAnnouncement->content, 150) }}</p>
                                <small class="text-muted">
                                    <i class="ph-duotone ph-tag me-1"></i>
                                    Role: {{ ucfirst(str_replace('-', ' ', $emailingAnnouncement->role->name)) }}
                                </small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">
                                <i class="ph-duotone ph-users me-1"></i>
                                Select Roles to Send Email
                            </label>
                            <div class="row">
                                @foreach($availableRoles as $role)
                                    <div class="mb-2 col-md-6">
                                        <div class="form-check">
                                            <input
                                                wire:model="selectedRoleIds"
                                                class="form-check-input"
                                                type="checkbox"
                                                value="{{ $role->id }}"
                                                id="role_{{ $role->id }}"
                                            >
                                            <label class="form-check-label" for="role_{{ $role->id }}">
                                                {{ ucfirst(str_replace('-', ' ', $role->name)) }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            @error('selectedRoleIds')
                                <div class="mt-1 text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="alert alert-info">
                            <i class="ph-duotone ph-info me-2"></i>
                            <strong>Note:</strong> Emails will be sent to all active users with the selected roles.
                            Make sure to verify your selection before sending.
                        </div>
                    @else
                        <div class="py-4 text-center text-muted">
                            <i class="ph-duotone ph-megaphone display-4"></i>
                            <p class="mt-2 mb-0">Announcement not found</p>
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeEmailModal">Cancel</button>
                    <button type="button" class="btn btn-info" wire:click="sendEmail" @if(!$emailingAnnouncement) disabled @endif>
                        <i class="ph-duotone ph-paper-plane me-1"></i>
                        Send Emails
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Email Confirmation Modal -->
    <div class="modal fade @if($showBulkEmailModal) show @endif" @if($showBulkEmailModal) style="display: block;" @endif tabindex="-1" wire:click.self="closeBulkEmailModal">
        <div class="modal-dialog" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-info">
                        <i class="ph-duotone ph-envelope-circle me-2"></i>
                        Send All Active Announcements
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeBulkEmailModal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="ph-duotone ph-info me-2"></i>
                        <strong>Bulk Email Sending</strong>
                    </div>
                    <p>This action will send emails for all active announcements that haven't been sent yet.</p>
                    <p>Each announcement will be sent to users with the corresponding role. Are you sure you want to proceed?</p>
                    <div class="alert alert-warning">
                        <i class="ph-duotone ph-warning me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone. Emails will be sent immediately.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeBulkEmailModal">Cancel</button>
                    <button type="button" class="btn btn-info" wire:click="sendEmailsForActiveAnnouncements">
                        <i class="ph-duotone ph-paper-plane me-1"></i>
                        Send All Emails
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Delivery Status Modal -->
    <div class="modal fade @if($showEmailDeliveryModal) show @endif" @if($showEmailDeliveryModal) style="display: block;" @endif tabindex="-1" wire:click.self="closeEmailDeliveryModal">
        <div class="modal-dialog modal-xl" wire:click.stop>
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-success">
                        <i class="ph-duotone ph-chart-line me-2"></i>
                        Email Delivery Status
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeEmailDeliveryModal"></button>
                </div>
                <div class="modal-body">
                    @if($emailDeliveryAnnouncement && $emailLogs)
                        <div class="mb-4">
                            <h6 class="fw-bold">Announcement Details:</h6>
                            <div class="p-3 rounded bg-light">
                                <h6 class="mb-2">{{ $emailDeliveryAnnouncement->title }}</h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <small class="text-muted">
                                            <i class="ph-duotone ph-tag me-1"></i>
                                            Role: {{ ucfirst(str_replace('-', ' ', $emailDeliveryAnnouncement->role->name)) }}
                                        </small>
                                    </div>
                                    <div>
                                        <small class="text-muted">
                                            <i class="ph-duotone ph-clock me-1"></i>
                                            Sent: {{ $emailDeliveryAnnouncement->email_sent_at->format('F d, Y \a\t H:i') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4 row">
                            <div class="col-md-3">
                                <div class="text-white card bg-primary">
                                    <div class="text-center card-body">
                                        <h4 class="card-title">{{ $emailLogs->count() }}</h4>
                                        <p class="card-text">Total Recipients</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-success">
                                    <div class="text-center card-body">
                                        <h4 class="card-title">{{ $emailLogs->where('status', 'sent')->count() }}</h4>
                                        <p class="card-text">Successful</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-danger">
                                    <div class="text-center card-body">
                                        <h4 class="card-title">{{ $emailLogs->where('status', 'failed')->count() }}</h4>
                                        <p class="card-text">Failed</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-info">
                                    <div class="text-center card-body">
                                        <h4 class="card-title">{{ number_format(($emailLogs->where('status', 'sent')->count() / $emailLogs->count()) * 100, 1) }}%</h4>
                                        <p class="card-text">Success Rate</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6 class="fw-bold">Detailed Delivery Report:</h6>
                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table table-hover">
                                    <thead class="table-light sticky-top">
                                        <tr>
                                            <th>Recipient</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Sent At</th>
                                            <th>Error</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($emailLogs->sortBy('user_name') as $log)
                                            <tr>
                                                <td>
                                                    <div>
                                                        <div class="fw-bold">{{ $log->user_name }}</div>
                                                        <small class="text-muted">{{ $log->user_email }}</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-light-secondary text-dark">
                                                        {{ ucfirst(str_replace('-', ' ', $log->user_role)) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($log->status === 'sent')
                                                        <span class="badge bg-success">
                                                            <i class="ph-duotone ph-check me-1"></i>
                                                            Sent
                                                        </span>
                                                    @else
                                                        <span class="badge bg-danger">
                                                            <i class="ph-duotone ph-x me-1"></i>
                                                            Failed
                                                        </span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        {{ \Carbon\Carbon::parse($log->sent_at)->format('M d, Y H:i') }}
                                                    </small>
                                                </td>
                                                <td>
                                                    @if($log->error_message)
                                                        <small class="text-danger" title="{{ $log->error_message }}">
                                                            {{ Str::limit($log->error_message, 30) }}
                                                        </small>
                                                    @else
                                                        <small class="text-muted">-</small>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @else
                        <div class="py-4 text-center text-muted">
                            <i class="ph-duotone ph-chart-line display-4"></i>
                            <p class="mt-2 mb-0">No email delivery data found</p>
                            <small>This announcement may not have been sent via email yet.</small>
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeEmailDeliveryModal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Backdrop -->
    @if($showCreateModal || $showEditModal || $showViewModal || $showDeleteModal || $showEmailModal || $showBulkEmailModal || $showEmailDeliveryModal)
        <div class="modal-backdrop fade show"></div>
    @endif
</div>
