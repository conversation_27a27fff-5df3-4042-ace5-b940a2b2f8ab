<div>
    <div class="page-header">
        <div class="page-block">
           <div class="row align-items-center">
              <div class="col-md-8">
                 <div class="page-header-title">
                    <h2 class="mb-0">Gold Price</h2>
                    @if($lastUpdated)
                        <small class="text-muted">Last updated: {{ $lastUpdated }}</small>
                    @endif
                 </div>
              </div>
              <div class="col-md-4 text-end">
                 <button wire:click="refreshGoldPrices" class="btn btn-primary"
                         wire:loading.attr="disabled" wire:target="refreshGoldPrices">
                    <span wire:loading.remove wire:target="refreshGoldPrices">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </span>
                    <span wire:loading wire:target="refreshGoldPrices">
                        <i class="fas fa-spinner fa-spin"></i> Refreshing...
                    </span>
                 </button>
              </div>
           </div>
        </div>
     </div>

     <!-- Error Message -->
     @if($errorMessage)
         <div class="alert alert-danger alert-dismissible fade show" role="alert">
             <i class="fas fa-exclamation-triangle"></i> {{ $errorMessage }}
             <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
         </div>
     @endif

     <!-- Success Message -->
     @if(session()->has('success'))
         <div class="alert alert-success alert-dismissible fade show" role="alert">
             <i class="fas fa-check-circle"></i> {{ session('success') }}
             <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
         </div>
     @endif

     <div class="row">
        <div class="col-sm-12">
           <div class="card">
              <div class="card-body table-border-style">
                 <!-- Loading State -->
                 <div wire:loading wire:target="refreshGoldPrices,loadGoldPrices" class="py-4 text-center">
                     <div class="spinner-border text-primary" role="status">
                         <span class="visually-hidden">Loading...</span>
                     </div>
                     <p class="mt-2 text-muted">Loading gold prices...</p>
                 </div>

                 <div wire:loading.remove wire:target="refreshGoldPrices,loadGoldPrices" class="table-responsive">
                    <table class="table table-hover">
                       <thead>
                          <tr>
                             <th>#</th>
                             <th>Purity</th>
                             <th>Price</th>
                             {{-- <th class="text-end">Member</th> --}}
                             {{-- <th class="text-end">Dealer</th> --}}
                             {{-- <th class="text-end">RAF</th> --}}
                             {{-- <th class="text-end">ND</th> --}}
                             {{-- <th class="text-end">Trade In</th> --}}
                             {{-- <th class="text-end">Buyback</th> --}}
                          </tr>
                       </thead>
                       <tbody>
                          @forelse($goldPrices as $index => $price)
                          <tr>
                             <td>{{ $index + 1 }}</td>
                             <td><span class="badge bg-primary">{{ $price['Purity'] }}</span></td>
                             <td><strong>RM {{ $price['Harga_Pelanggan'] }}</strong></td>
                             {{-- <td class="text-end">RM {{ $price['Harga_Member'] }}</td>
                             <td class="text-end">RM {{ $price['Harga_Pengedar'] }}</td>
                             <td class="text-end">RM {{ $price['Harga_RAF'] }}</td>
                             <td class="text-end">RM {{ $price['harga_nd'] }}</td>
                             <td class="text-end">
                                 @if($price['harga_trade_in'] > 0)
                                     RM {{ $price['harga_trade_in'] }}
                                 @else
                                     <span class="text-muted">N/A</span>
                                 @endif
                             </td>
                             <td class="text-end">
                                 @if($price['harga_buyback'] > 0)
                                     RM {{ $price['harga_buyback'] }}
                                 @else
                                     <span class="text-muted">N/A</span>
                                 @endif
                             </td> --}}
                          </tr>
                          @empty
                          <tr>
                             <td colspan="9" class="py-4 text-center">
                                 <i class="mb-3 fas fa-coins fa-3x text-muted"></i>
                                 <p class="text-muted">No gold prices available</p>
                                 <button wire:click="refreshGoldPrices" class="btn btn-outline-primary btn-sm">
                                     <i class="fas fa-sync-alt"></i> Try Again
                                 </button>
                             </td>
                          </tr>
                          @endforelse
                       </tbody>
                    </table>
                 </div>
              </div>
           </div>
        </div>
     </div>
</div>
