<div>
    <div class="page-header">
        <div class="page-block">
           <div class="row align-items-center">
              <div class="col-md-12">
                 <div class="page-header-title">
                    <h2 class="mb-0">Downline</h2>
                 </div>
              </div>
           </div>
        </div>
     </div>


     <div class="row">

        <div class="col-lg-4 col-md-6">
           <div class="card statistics-card-1">
              <div class="card-body">
                 <img src="{{asset('assets/images/widget/img-status-3.svg')}}" alt="img" class="img-fluid img-bg">
                 <div class="d-flex align-items-center">
                    <div class="text-white avtar bg-brand-color-1 me-3"><i class="ph-duotone ph-users-four f-26"></i></div>
                    <div>
                       <p class="mb-0 text-muted">Total Referrals</p>
                       <div class="d-flex align-items-end">
                          <h2 class="mb-0 f-w-500">{{ number_format($totalReferrals) }}</h2>
                       </div>
                    </div>
                 </div>
              </div>
           </div>
        </div>
        <div class="col-lg-4 col-md-6">
           <div class="card statistics-card-1">
              <div class="card-body">
                 <img src="{{asset('assets/images/widget/img-status-3.svg')}}" alt="img" class="img-fluid img-bg">
                 <div class="d-flex align-items-center">
                    <div class="text-white avtar bg-brand-color-2 me-3"><i class="ph-duotone ph-calendar f-26"></i></div>
                    <div>
                       <p class="mb-0 text-muted">Previous Month Referrals</p>
                       <div class="d-flex align-items-end">
                          <h2 class="mb-0 f-w-500">{{ number_format($previousMonthReferrals) }}</h2>
                          <small class="text-muted ms-2">{{ \Carbon\Carbon::now()->subMonth()->format('M Y') }}</small>
                       </div>
                    </div>
                 </div>
              </div>
           </div>
        </div>
        <div class="col-lg-4 col-md-6">
           <div class="card statistics-card-1">
              <div class="card-body">
                 <img src="{{asset('assets/images/widget/img-status-3.svg')}}" alt="img" class="img-fluid img-bg">
                 <div class="d-flex align-items-center">
                    <div class="text-white avtar bg-brand-color-3 me-3"><i class="ph-duotone ph-trend-up f-26"></i></div>
                    <div>
                       <p class="mb-0 text-muted">Current Month Referrals</p>
                       <div class="d-flex align-items-end">
                          <h2 class="mb-0 f-w-500">{{ number_format($currentMonthReferrals) }}</h2>
                          <small class="text-muted ms-2">{{ \Carbon\Carbon::now()->format('M Y') }}</small>
                       </div>
                    </div>
                 </div>
              </div>
           </div>
        </div>




        <div class="col-12">
           <div class="card">
              <div class="card-header">
                 <h5 class="mb-0">Referral List</h5>
              </div>
              <div class="card-body">
                 <div class="mb-3 row">
                    <div class="col-lg-8 col-md-7">
                       <label class="form-label">Search Referrals</label>
                       <div class="input-group">
                          <span class="input-group-text">
                             <i class="ph-duotone ph-magnifying-glass" wire:loading.remove wire:target="search"></i>
                             <div class="spinner-border spinner-border-sm" role="status" wire:loading wire:target="search">
                                <span class="visually-hidden">Loading...</span>
                             </div>
                          </span>
                          <input class="form-control" type="text" placeholder="Search by customer name or email..." wire:model.live.debounce.300ms="search" />
                       </div>
                    </div>
                    <div class="col-lg-4 col-md-5 d-flex align-items-end">
                       @if($search)
                          <button class="btn btn-outline-secondary" wire:click="clearSearch">
                             <i class="ph-duotone ph-x me-1"></i>Clear Search
                          </button>
                       @else
                          <div class="text-muted small">
                             Total: {{ number_format(count($downlineData)) }} referral(s)
                          </div>
                       @endif
                    </div>
                 </div>

                 <div class="table-responsive position-relative">
                    <!-- Loading overlay -->

                    <table class="table table-hover">
                       <thead class="table-light">
                          <tr>
                             <th width="60">#</th>
                             <th>Customer Name</th>
                             <th>Email Address</th>
                             <th width="150" class="text-center">Registration Date</th>
                          </tr>
                       </thead>
                       <tbody>
                          @forelse($downlineData as $index => $customer)
                          <tr>
                             <td>{{ $index + 1 }}</td>
                             <td>
                                <div class="d-flex align-items-center">
                                   <div class="flex-shrink-0">
                                      <div class="avtar avtar-s bg-light-primary">
                                         <i class="ph-duotone ph-user"></i>
                                      </div>
                                   </div>
                                   <div class="flex-grow-1 ms-3">
                                      <h6 class="mb-0">{{ $customer->nama }}</h6>
                                   </div>
                                </div>
                             </td>
                             <td>
                                <span class="text-muted">{{ $customer->email }}</span>
                             </td>
                             <td class="text-center">
                                <span class="badge bg-success">
                                   {{ \Carbon\Carbon::parse($customer->tarikh)->format('M d, Y') }}
                                </span>
                                <br>
                                <small class="text-muted">
                                   {{ \Carbon\Carbon::parse($customer->tarikh)->diffForHumans() }}
                                </small>
                             </td>
                          </tr>
                          @empty
                          <tr>
                             <td colspan="4" class="py-5 text-center">
                                <div class="d-flex flex-column align-items-center">
                                   <i class="ph-duotone ph-users-four" style="font-size: 4rem; color: #6c757d; opacity: 0.5;"></i>
                                   <h5 class="mt-3 mb-1 text-muted">No referrals found</h5>
                                   @if($search)
                                      <p class="mb-2 text-muted">No customers match your search criteria</p>
                                      <button class="btn btn-sm btn-outline-primary" wire:click="clearSearch">
                                         <i class="ph-duotone ph-x me-1"></i>Clear Search
                                      </button>
                                   @else
                                      <p class="mb-2 text-muted">Start sharing your referral code to get customers</p>
                                      <a href="{{ url('agent/referral-code') }}" class="btn btn-sm btn-primary">
                                         <i class="ph-duotone ph-share me-1"></i>View Referral Code
                                      </a>
                                   @endif
                                </div>
                             </td>
                          </tr>
                          @endforelse
                       </tbody>
                    </table>
                 </div>
              </div>
           </div>
        </div>

     </div>
</div>
