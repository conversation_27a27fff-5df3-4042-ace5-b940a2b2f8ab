<div>
    <div class="">
        <div class="">
            <!-- <PERSON> Header -->
            <div class="page-header">
                <div class="page-block">
                    <div class="row align-items-center">
                        <div class="col-md-12">
                            <div class="page-header-title">
                                <h2 class="mb-0">Referral Code</h2>
                            </div>
                            <ul class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ url('agent/dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item" aria-current="page">Referral Code</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page Content -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>Your Referral Code</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Left Column - Referral Information -->
                                <div class="col-md-8">
                                    <div class="mb-4">
                                        <label class="form-label">Referral Code</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="referralCode" value="{{ $referralCode }}" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('referralCode')">
                                                <i class="ph-duotone ph-copy"></i> Copy
                                            </button>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Referral Link</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="referralUrl" value="{{ $referralUrl }}" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('referralUrl')">
                                                <i class="ph-duotone ph-copy"></i> Copy Link
                                            </button>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <h6 class="alert-heading"><i class="ph-duotone ph-info"></i> How to use your referral code</h6>
                                        <p class="mb-2">Share your referral code or link with potential customers:</p>
                                        <ul class="mb-0">
                                            <li>Send the referral link directly to customers</li>
                                            <li>Share the QR code for easy scanning</li>
                                            <li>When customers register using your code, you'll earn commissions</li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Right Column - QR Code -->
                                <div class="col-md-4">
                                    <div class="text-center d-flex flex-column align-items-center justify-content-center h-100">
                                        <h6 class="mb-3">QR Code</h6>
                                        <div class="mb-3 d-flex justify-content-center" id="qrcode-container">
                                            <!-- QR Code will be generated here -->
                                        </div>
                                        <button class="btn btn-sm btn-primary" onclick="downloadQR()">
                                            <i class="ph-duotone ph-download"></i> Download QR
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast for copy feedback -->
    <div class="top-0 p-3 toast-container position-fixed end-0">
        <div id="copyToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="ph-duotone ph-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                Copied to clipboard!
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate QR Code
    const qrCodeContainer = document.getElementById('qrcode-container');

    // Clear any existing content
    qrCodeContainer.innerHTML = '';

    const qr = new QRCode(qrCodeContainer, {
        text: '{{ $referralUrl }}',
        width: 200,
        height: 200,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.M
    });
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const textToCopy = element.value;

    // Try modern clipboard API first (requires HTTPS)
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(textToCopy).then(function() {
            showCopyToast();
        }).catch(function(err) {
            console.log('Clipboard API failed, trying fallback:', err);
            fallbackCopyTextToClipboard(textToCopy);
        });
    } else {
        // Use fallback method for HTTP or older browsers
        fallbackCopyTextToClipboard(textToCopy);
    }
}

function fallbackCopyTextToClipboard(text) {
    // Create a temporary textarea element
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopyToast();
        } else {
            showCopyError();
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showCopyError();
    }

    document.body.removeChild(textArea);
}

function showCopyToast() {
    const toast = new bootstrap.Toast(document.getElementById('copyToast'));
    toast.show();
}

function showCopyError() {
    // Update toast to show error message
    const toastBody = document.querySelector('#copyToast .toast-body');
    const toastIcon = document.querySelector('#copyToast .toast-header i');
    const originalBody = toastBody.textContent;
    const originalIconClass = toastIcon.className;

    toastBody.textContent = 'Failed to copy. Please select and copy manually.';
    toastIcon.className = 'ph-duotone ph-x-circle text-danger me-2';

    const toast = new bootstrap.Toast(document.getElementById('copyToast'));
    toast.show();

    // Reset to original after toast is hidden
    setTimeout(() => {
        toastBody.textContent = originalBody;
        toastIcon.className = originalIconClass;
    }, 5000);
}

function downloadQR() {
    const canvas = document.querySelector('#qrcode-container canvas');
    if (canvas) {
        const link = document.createElement('a');
        link.download = 'referral-qr-code.png';
        link.href = canvas.toDataURL();
        link.click();
    }
}
</script>
@endpush
