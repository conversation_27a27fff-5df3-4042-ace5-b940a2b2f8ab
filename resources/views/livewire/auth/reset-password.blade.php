<div class="auth-main v2">
   <div class="bg-overlay bg-dark"></div>
   <div class="auth-wrapper">
      <div class="auth-sidecontent">
         <div class="auth-sidefooter">
           <img src="{{asset('assets/images/logo-dark.svg')}}" class="img-brand img-fluid" alt="images">
            <hr class="mt-4 mb-3">
            <div class="row">
               <div class="my-1 col">
                  <p class="m-0">Made with ♥ by Team <a href="https://themeforest.net/user/phoenixcoded" target="_blank">Phoenixcoded</a></p>
               </div>
               <div class="col-auto my-1">
                  <ul class="mb-0 list-inline footer-link">
                     <li class="list-inline-item"><a href="../index.html">Home</a></li>
                     <li class="list-inline-item"><a href="https://pcoded.gitbook.io/light-able/" target="_blank">Documentation</a></li>
                     <li class="list-inline-item"><a href="https://phoenixcoded.support-hub.io/" target="_blank">Support</a></li>
                  </ul>
               </div>
            </div>
         </div>
      </div>
      <div class="auth-form">
         <div class="mx-3 my-5 card">
            <div class="card-body">
               <h4 class="mb-1 f-w-500">Reset Password</h4>
               <p class="mb-4 text-muted">Enter your new password below.</p>

               @if ($isSuccess)
                  <div class="alert alert-success alert-dismissible fade show" role="alert">
                     {{ $message }}
                     <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>
                  <div class="mt-4 d-grid">
                     <a href="{{ route('login') }}" class="btn btn-primary">
                        <i class="ti ti-login me-2"></i>
                        Go to Login
                     </a>
                  </div>
               @else
                  <form wire:submit="resetPassword">
                     <div class="mb-3">
                        <input type="password"
                               class="form-control @error('password') is-invalid @enderror"
                               wire:model="password"
                               placeholder="New Password"
                               required
                               autofocus
                               @disabled($isLoading)>
                        @error('password')
                           <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Password must be at least 8 characters long.</small>
                     </div>

                     <div class="mb-3">
                        <input type="password"
                               class="form-control @error('password_confirmation') is-invalid @enderror"
                               wire:model="password_confirmation"
                               placeholder="Confirm New Password"
                               required
                               @disabled($isLoading)>
                        @error('password_confirmation')
                           <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                     </div>

                     <div class="mt-4 d-grid">
                        <button type="submit" class="btn btn-primary" @disabled($isLoading)>
                           @if($isLoading)
                              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                              Resetting...
                           @else
                              Reset Password
                           @endif
                        </button>
                     </div>
                  </form>

                  <div class="mt-4 text-center">
                     <a href="{{ route('login') }}" class="text-secondary">
                        <i class="ti ti-arrow-left me-1"></i>
                        Back to Login
                     </a>
                  </div>
               @endif
            </div>
         </div>
      </div>
   </div>
</div>
