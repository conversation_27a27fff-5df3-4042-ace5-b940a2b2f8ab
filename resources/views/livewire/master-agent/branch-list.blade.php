@php
use Illuminate\Support\Facades\Storage;
@endphp

<div> {{-- 🔁 Single root wrapper required by Livewire --}}

    <!-- Page Header -->
    <div class="page-header">
       <div class="page-block">
          <div class="row align-items-center">
             <div class="col-md-12">
                <div class="page-header-title">
                   <h2 class="mb-0">Profile</h2>
                </div>
             </div>
          </div>
       </div>
    </div>

    <!-- Profile Info Card -->
    {{-- <div class="row">
       <div class="card">
          <div class="card-body">
             <div class="row">
                <ul class="list-group list-group-flush">
                   <li class="px-0 pt-0 list-group-item">
                      <div class="mb-0 row">
                         <label class="col-form-label col-md-4 col-sm-12 text-md-start">Organization Name</label>
                         <div class="col-md-8 col-sm-12">
                            <input type="text" class="form-control">
                         </div>
                      </div>
                   </li>
                   <li class="px-0 list-group-item">
                      <div class="mb-0 row">
                         <label class="col-form-label col-md-4 col-sm-12 text-md-start">Business Registration Number</label>
                         <div class="col-md-8 col-sm-12"><input type="text" class="form-control"></div>
                      </div>
                   </li>
                   <li class="px-0 list-group-item">
                      <div class="mb-0 row">
                         <label class="col-form-label col-md-4 col-sm-12 text-md-start">
                            Business Registration Document <a href="#"><i class="ti ti-eye"></i></a>
                         </label>
                         <div class="col-md-8 col-sm-12"><input type="file" class="form-control"></div>
                      </div>
                   </li>
                   <li class="px-0 list-group-item">
                      <div class="mb-0 row">
                         <label class="col-form-label col-md-4 col-sm-12 text-md-start">Account Email</label>
                         <div class="col-md-8 col-sm-12"><input type="text" class="form-control" disabled></div>
                      </div>
                   </li>
                   <li class="px-0 list-group-item">
                      <div class="mb-0 row">
                         <label class="col-form-label col-md-4 col-sm-6 text-md-start">Account Password</label>
                         <div class="col-md-8 col-sm-6 text-end">
                            <a href="#" data-bs-toggle="modal" data-bs-target="#viewBranch" class="avtar avtar-xs btn-link-secondary">
                               <i class="ti ti-check f-20"></i>
                            </a>
                         </div>
                      </div>
                   </li>
                   <li class="px-0 list-group-item">
                      <div class="mb-0 row">
                         <label class="col-form-label col-md-4 col-sm-6 text-md-start">Status</label>
                         <div class="col-md-8 col-sm-6 text-end">
                            <span class="badge bg-success">Active</span>
                         </div>
                      </div>
                   </li>
                </ul>

                <div class="mt-3 text-end">
                   <button class="btn btn-primary">Save</button>
                </div>
             </div>
          </div>
       </div>
    </div> --}}

    <!-- Payment Method Status -->
    <div class="mb-3 row">
        <div class="col-12">
            <div class="card border-{{ $paymentMethod === 'main_cooperative' ? 'primary' : 'success' }}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-2">
                                <i class="ph-duotone ph-credit-card me-2"></i>
                                Current Payment Method:
                                <span class="badge bg-{{ $paymentMethod === 'main_cooperative' ? 'primary' : 'success' }}">
                                    {{ $paymentMethod === 'main_cooperative' ? 'Main Cooperative' : 'Individual Branch' }}
                                </span>
                            </h6>
                            <p class="mb-0 text-muted">
                                @if($paymentMethod === 'main_cooperative')
                                    All payments are processed through the main cooperative account. Use bulk controls to manage all branches together.
                                @else
                                    Each branch can have individual payment settings and registration controls.
                                @endif
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ route('master_agent.payment-settings') }}" class="btn btn-outline-secondary">
                                <i class="ph-duotone ph-gear me-2"></i>Payment Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="mb-3 row">
        <div class="mb-2 col-md-3 col-sm-6">
            <div class="card border-primary">
                <div class="text-center card-body">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avtar avtar-s bg-primary me-2">
                            <i class="text-white ph-duotone ph-buildings"></i>
                        </div>
                        <div>
                            <h4 class="mb-0 text-primary">{{ $totalBranches }}</h4>
                            <p class="mb-0 text-muted small">Total Branches</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mb-2 col-md-3 col-sm-6">
            <div class="card border-success">
                <div class="text-center card-body">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avtar avtar-s bg-success me-2">
                            <i class="text-white ph-duotone ph-check-circle"></i>
                        </div>
                        <div>
                            <h4 class="mb-0 text-success">{{ $activeBranches }}</h4>
                            <p class="mb-0 text-muted small">Active Branches</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mb-2 col-md-3 col-sm-6">
            <div class="card border-warning">
                <div class="text-center card-body">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avtar avtar-s bg-warning me-2">
                            <i class="text-white ph-duotone ph-clock"></i>
                        </div>
                        <div>
                            <h4 class="mb-0 text-warning">{{ $inactiveBranches }}</h4>
                            <p class="mb-0 text-muted small">Inactive Branches</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mb-2 col-md-3 col-sm-6">
            <div class="card border-info">
                <div class="text-center card-body">
                    <div class="d-flex align-items-center justify-content-center">
                        <div class="avtar avtar-s bg-info me-2">
                            <i class="text-white ph-duotone ph-user-plus"></i>
                        </div>
                        <div>
                            <h4 class="mb-0 text-info">{{ $enabledRegistrations }}</h4>
                            <p class="mb-0 text-muted small">Registration Enabled</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Table -->
    <div class="row">
       <div class="card">
          <div class="card-header">
             <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h5 class="mb-0">Branch/Outlet</h5>
                    @if($paymentMethod === 'main_cooperative' && count($branches) > 0)
                        <small class="text-muted">Bulk controls available for Main Cooperative payment method</small>
                    @else
                        <small class="text-muted">
                            Showing {{ count($branches) }}
                            @if($showInactiveBranches)
                                of {{ $totalBranches }} branches (all branches)
                            @else
                                active branches
                                @if($inactiveBranches > 0)
                                    • {{ $inactiveBranches }} inactive hidden
                                @endif
                            @endif
                        </small>
                    @endif
                </div>
                <div class="gap-2 d-flex">
                    @if($paymentMethod === 'main_cooperative' && count($branches) > 0)
                        <div class="btn-group me-2" role="group">
                            <button class="btn btn-outline-success btn-sm"
                                    wire:click="toggleAllBranches(true)"
                                    wire:loading.attr="disabled">
                                <i class="ti ti-check me-1"></i>Enable All
                            </button>
                            <button class="btn btn-outline-danger btn-sm"
                                    wire:click="toggleAllBranches(false)"
                                    wire:loading.attr="disabled">
                                <i class="ti ti-x me-1"></i>Disable All
                            </button>
                        </div>
                    @endif

                    <!-- Toggle Inactive Branches Button -->
                    <button class="btn {{ $showInactiveBranches ? 'btn-warning' : 'btn-outline-warning' }} btn-sm me-2"
                            wire:click="toggleInactiveBranches"
                            wire:loading.attr="disabled"
                            title="{{ $showInactiveBranches ? 'Hide inactive branches' : 'Show inactive branches' }}">
                        <i class="ti {{ $showInactiveBranches ? 'ti-eye-off' : 'ti-eye' }} me-1"></i>
                        {{ $showInactiveBranches ? 'Hide' : 'Show' }} Inactive
                        @if($inactiveBranches > 0)
                            <span class="badge bg-light text-dark ms-1">{{ $inactiveBranches }}</span>
                        @endif
                    </button>

                    <button class="btn btn-primary" wire:click="openCreateBranchModal">Create New Branch</button>
                </div>
             </div>
          </div>
          <div class="card-body table-border-style">
             <div class="table-responsive">
                <table class="table table-hover">
                   <thead>
                      <tr>
                         <th>#</th>
                         <th>Branch Name</th>
                         <th>Organization Name</th>
                         <th class="text-center">Business Registration No</th>
                         <th class="text-center">Bank</th>
                         <th class="text-center">Status</th>
                         <th class="text-center">Registration</th>
                         <th class="text-center">Created At</th>
                         <th class="text-center">Actions</th>
                      </tr>
                   </thead>
                   <tbody>
                      @if(count($branches) > 0)
                         @foreach($branches as $index => $branch)
                         <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $branch->name }}</td>
                            <td>{{ $branch->organization_name ?? 'N/A' }}</td>
                            <td class="text-center">{{ $branch->business_registration_no ?? 'N/A' }}</td>
                            <td class="text-center">{{ $branch->bank_name ?? 'N/A' }}</td>
                            <td class="text-center">
                               @if($branch->status === 'active')
                                  <span class="badge bg-success">Active</span>
                               @elseif($branch->status === 'pending')
                                  <span class="badge bg-dark">Pending</span>
                               @else
                                  <span class="badge bg-secondary">Inactive</span>
                               @endif
                            </td>
                            <td class="text-center">
                               @if($branch->status === 'active')
                                  @if($paymentMethod === 'main_cooperative')
                                     @if($branch->registration_enabled ?? true)
                                        <span class="badge bg-success">Enabled</span>
                                        <br><small class="text-muted">Main Cooperative</small>
                                     @else
                                        <span class="badge bg-danger">Disabled</span>
                                        <br><small class="text-muted">Main Cooperative</small>
                                     @endif
                                  @else
                                     @if($branch->registration_enabled ?? true)
                                        <span class="badge bg-success">Enabled</span>
                                        <br><small class="text-muted">Individual</small>
                                     @else
                                        <span class="badge bg-danger">Disabled</span>
                                        <br><small class="text-muted">Individual</small>
                                     @endif
                                  @endif
                               @else
                                  <span class="text-muted">N/A</span>
                               @endif
                            </td>
                            <td class="text-center">{{ $branch->created_at ? \Carbon\Carbon::parse($branch->created_at)->format('d M Y') : 'N/A' }}</td>
                            <td class="text-center">
                               <button class="btn btn-link-info btn-sm" wire:click="viewBranch('{{ $branch->id }}')">
                                  <i class="ti ti-eye f-16"></i>
                               </button>
                               @if($branch->status === 'pending')
                                  <button class="btn btn-link-warning btn-sm" wire:click="editBranch('{{ $branch->id }}')" data-bs-toggle="modal" data-bs-target="#editBranch">
                                     <i class="ti ti-edit f-16"></i>
                                  </button>
                                  <button class="btn btn-link-danger btn-sm" wire:click="confirmDeleteBranch('{{ $branch->id }}')">
                                     <i class="ti ti-trash f-16"></i>
                                  </button>
                               @endif
                            </td>
                         </tr>
                         @endforeach
                      @else
                         <tr>
                            <td colspan="9" class="text-center">
                                @if($totalBranches > 0 && !$showInactiveBranches)
                                    No active branches found. <button wire:click="toggleInactiveBranches" class="p-0 btn btn-link">Show all branches</button>
                                @else
                                    No branches found
                                @endif
                            </td>
                         </tr>
                      @endif
                   </tbody>
                </table>
             </div>
          </div>
       </div>
    </div>

    <!-- Agreement Table -->
    {{-- <div class="row">
       <div class="card">
          <div class="py-3 card-header d-flex align-items-center justify-content-between">
             <h5 class="mb-0">Agreement & Workflow</h5>
             <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#draftAgreement">Draft New Agreement</button>
          </div>
          <div class="card-body table-border-style">
             <div class="table-responsive">
                <table class="table table-hover">
                   <thead>
                      <tr>
                         <th>#</th>
                         <th>Release Date</th>
                         <th class="text-center">Status</th>
                         <th class="text-center">Acknowledge At</th>
                         <th class="text-center">Acknowledge By</th>
                         <th></th>
                      </tr>
                   </thead>
                   <tbody>
                      <tr>
                         <td>1</td>
                         <td>01 May 2025</td>
                         <td class="text-center"><span class="badge bg-success">Publish</span></td>
                         <td class="text-center">01 May 2025</td>
                         <td class="text-center">MA A</td>
                         <td class="text-end">
                            <a href="{{ url('admin/master-agent/uuid/details') }}" class="avtar avtar-xs btn-link-secondary">
                               <i class="ti ti-mail-forward f-20"></i>
                            </a>
                            <a href="{{ url('admin/master-agent/uuid/details/agreement/agreement-uuid') }}" class="avtar avtar-xs btn-link-secondary">
                               <i class="ti ti-eye f-20"></i>
                            </a>
                         </td>
                      </tr>
                      <tr>
                         <td>2</td>
                         <td>01 May 2025</td>
                         <td class="text-center"><span class="badge bg-dark">Draft</span></td>
                         <td class="text-center">01 May 2025</td>
                         <td class="text-center">MA A</td>
                         <td class="text-end">
                            <a href="{{ url('admin/master-agent/uuid/details/agreement/agreement-uuid') }}" class="avtar avtar-xs btn-link-secondary">
                               <i class="ti ti-eye f-20"></i>
                            </a>
                            <a href="{{ url('admin/master-agent/uuid/details') }}" class="avtar avtar-xs btn-link-secondary">
                               <i class="ti ti-trash f-20"></i>
                            </a>
                         </td>
                      </tr>
                   </tbody>
                </table>
             </div>
          </div>
       </div>
    </div> --}}

    <!-- Create Branch Modal -->
    <div class="modal fade" id="createBranch" tabindex="-1" aria-labelledby="createBranchLabel" aria-hidden="true" wire:ignore.self>
       <div class="modal-dialog modal-lg">
          <div class="modal-content">
             <div class="modal-header">
                <h5 class="modal-title" id="createBranchLabel">Create New Branch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="closeCreateBranchModal"></button>
             </div>
             <div class="modal-body">
                <form wire:submit.prevent="createBranch">
                   <div class="row">
                      <div class="mb-3 col-12">
                         <label for="branch_name" class="form-label">Branch Name <span class="text-danger">*</span></label>
                         <input type="text" class="form-control @error('branch_name') is-invalid @enderror" id="branch_name" wire:model="branch_name" placeholder="Enter branch name">
                         @error('branch_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                         @enderror
                      </div>

                      <div class="mb-3 col-12">
                         <label for="organization_name" class="form-label">Organization Name <span class="text-danger">*</span></label>
                         <input type="text" class="form-control @error('organization_name') is-invalid @enderror" id="organization_name" wire:model="organization_name" placeholder="Enter organization name">
                         @error('organization_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                         @enderror
                      </div>

                      <div class="mb-3 col-12">
                         <label for="business_registration_no" class="form-label">Business Registration Number <span class="text-danger">*</span></label>
                         <input type="text" class="form-control @error('business_registration_no') is-invalid @enderror" id="business_registration_no" wire:model="business_registration_no" placeholder="Enter business registration number">
                         @error('business_registration_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                         @enderror
                      </div>

                      <div class="mb-3 col-12">
                         <label for="business_registration_document" class="form-label">Business Registration Document</label>
                         <input type="file" class="form-control @error('business_registration_document') is-invalid @enderror" id="business_registration_document" wire:model="business_registration_document" accept=".pdf,.jpg,.jpeg,.png">
                         @error('business_registration_document')
                            <div class="invalid-feedback">{{ $message }}</div>
                         @enderror
                         <small class="text-muted">Accepted formats: PDF, JPG, JPEG, PNG (Max: 2MB)</small>
                      </div>

                      @if($paymentMethod === 'main_cooperative')
                         <!-- Main Cooperative Bank Alert -->
                         <div class="mb-3 col-12">
                            <div class="alert alert-info">
                               <h6 class="mb-2 alert-heading">
                                  <i class="ph-duotone ph-info me-2"></i>Bank Account Information
                               </h6>
                               <p class="mb-0">This branch will use the <strong>Main Cooperative</strong> bank account for all transactions. Individual bank account setup is not required.</p>
                            </div>
                         </div>
                      @else
                         <!-- Individual Branch Bank Fields -->
                         <div class="mb-3 col-12">
                            <label for="acs_bank_names_id" class="form-label">Bank Name <span class="text-danger">*</span></label>
                            <select class="form-select @error('acs_bank_names_id') is-invalid @enderror" id="acs_bank_names_id" wire:model="acs_bank_names_id">
                               <option value="">Select Bank</option>
                               @foreach($bankNames as $bank)
                                  <option value="{{ $bank->id }}">{{ $bank->bank_name }}</option>
                               @endforeach
                            </select>
                            @error('acs_bank_names_id')
                               <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                         </div>

                         <div class="mb-3 col-12">
                            <label for="account_name" class="form-label">Account Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('account_name') is-invalid @enderror" id="account_name" wire:model="account_name" placeholder="Enter account holder name">
                            @error('account_name')
                               <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                         </div>

                         <div class="mb-3 col-12">
                            <label for="account_number" class="form-label">Account Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('account_number') is-invalid @enderror" id="account_number" wire:model="account_number" placeholder="Enter account number">
                            @error('account_number')
                               <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                         </div>
                      @endif
                   </div>
                   <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" wire:click="closeCreateBranchModal">Cancel</button>
                      <button type="submit" class="btn btn-primary" wire:loading.attr="disabled">
                         <span wire:loading.remove>Create Branch</span>
                         <span wire:loading>
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Creating...
                         </span>
                      </button>
                   </div>
                </form>
             </div>
          </div>
       </div>
    </div>

    <!-- Edit Branch Modal -->
    <div class="modal fade" id="editBranch" tabindex="-1" aria-labelledby="editBranchLabel" aria-hidden="true" wire:ignore.self>
       <div class="modal-dialog modal-lg">
          <div class="modal-content">
             <div class="modal-header">
                <h5 class="modal-title" id="editBranchLabel">Edit Branch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="closeEditBranchModal"></button>
             </div>
             <div class="modal-body">
                <form wire:submit.prevent="updateBranch">
                   <div class="row">
                      <div class="mb-3 col-12">
                         <label for="edit_branch_name" class="form-label">Branch Name <span class="text-danger">*</span></label>
                         <input type="text" class="form-control @error('edit_branch_name') is-invalid @enderror" id="edit_branch_name" wire:model="edit_branch_name" placeholder="Enter branch name">
                         @error('edit_branch_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                         @enderror
                      </div>

                      <div class="mb-3 col-12">
                         <label for="edit_organization_name" class="form-label">Organization Name <span class="text-danger">*</span></label>
                         <input type="text" class="form-control @error('edit_organization_name') is-invalid @enderror" id="edit_organization_name" wire:model="edit_organization_name" placeholder="Enter organization name">
                         @error('edit_organization_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                         @enderror
                      </div>

                      <div class="mb-3 col-12">
                         <label for="edit_business_registration_no" class="form-label">Business Registration Number <span class="text-danger">*</span></label>
                         <input type="text" class="form-control @error('edit_business_registration_no') is-invalid @enderror" id="edit_business_registration_no" wire:model="edit_business_registration_no" placeholder="Enter business registration number">
                         @error('edit_business_registration_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                         @enderror
                      </div>

                      <div class="mb-3 col-12">
                         <label for="edit_business_registration_document" class="form-label">Business Registration Document</label>
                         <input type="file" class="form-control @error('edit_business_registration_document') is-invalid @enderror" id="edit_business_registration_document" wire:model="edit_business_registration_document" accept=".pdf,.jpg,.jpeg,.png">
                         @error('edit_business_registration_document')
                            <div class="invalid-feedback">{{ $message }}</div>
                         @enderror
                         <small class="text-muted">Accepted formats: PDF, JPG, JPEG, PNG (Max: 2MB). Leave empty to keep current document.</small>
                      </div>

                      @if($paymentMethod === 'main_cooperative')
                         <!-- Main Cooperative Bank Alert -->
                         <div class="mb-3 col-12">
                            <div class="alert alert-info">
                               <h6 class="mb-2 alert-heading">
                                  <i class="ph-duotone ph-info me-2"></i>Bank Account Information
                               </h6>
                               <p class="mb-0">This branch will use the <strong>Main Cooperative</strong> bank account for all transactions. Individual bank account setup is not required.</p>
                            </div>
                         </div>
                      @else
                         <!-- Individual Branch Bank Fields -->
                         <div class="mb-3 col-12">
                            <label for="edit_acs_bank_names_id" class="form-label">Bank Name <span class="text-danger">*</span></label>
                            <select class="form-select @error('edit_acs_bank_names_id') is-invalid @enderror" id="edit_acs_bank_names_id" wire:model="edit_acs_bank_names_id">
                               <option value="">Select Bank</option>
                               @foreach($bankNames as $bank)
                                  <option value="{{ $bank->id }}">{{ $bank->bank_name }}</option>
                               @endforeach
                            </select>
                            @error('edit_acs_bank_names_id')
                               <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                         </div>

                         <div class="mb-3 col-12">
                            <label for="edit_account_name" class="form-label">Account Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('edit_account_name') is-invalid @enderror" id="edit_account_name" wire:model="edit_account_name" placeholder="Enter account holder name">
                            @error('edit_account_name')
                               <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                         </div>

                         <div class="mb-3 col-12">
                            <label for="edit_account_number" class="form-label">Account Number <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('edit_account_number') is-invalid @enderror" id="edit_account_number" wire:model="edit_account_number" placeholder="Enter account number">
                            @error('edit_account_number')
                               <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                         </div>
                      @endif
                   </div>
                </form>
             </div>
             <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" wire:click="closeEditBranchModal">Cancel</button>
                <button type="button" class="btn btn-primary" wire:click="updateBranch" wire:loading.attr="disabled">
                   <span wire:loading.remove>Update Branch</span>
                   <span wire:loading>
                      <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Updating...
                   </span>
                </button>
             </div>
          </div>
       </div>
    </div>

    <!-- Delete Branch Confirmation Modal -->
    <div class="modal fade" id="deleteBranchConfirmation" tabindex="-1" aria-labelledby="deleteBranchConfirmationLabel" aria-hidden="true" wire:ignore.self>
       <div class="modal-dialog">
          <div class="modal-content">
             <div class="modal-header">
                <h5 class="modal-title" id="deleteBranchConfirmationLabel">Confirm Delete Branch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
             </div>
             <div class="modal-body">
                Are you sure you want to delete this branch? This action cannot be undone.
             </div>
             <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" wire:click="deleteBranch" wire:loading.attr="disabled">
                   <span wire:loading.remove>Delete Branch</span>
                   <span wire:loading>
                      <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Deleting...
                   </span>
                </button>
             </div>
          </div>
       </div>
    </div>

    <!-- Branch View Modal -->
    <div class="modal fade" id="viewBranchModal" tabindex="-1" aria-labelledby="viewBranchModalLabel" aria-hidden="true" wire:ignore.self>
       <div class="modal-dialog modal-lg">
          <div class="modal-content">
             <div class="modal-header">
                <h5 class="modal-title" id="viewBranchModalLabel">Branch Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" wire:click="closeViewBranchModal"></button>
             </div>
             <div class="modal-body">
                @if($viewingBranch)
                <div class="row">
                   <!-- Left Column - Basic Information -->
                   <div class="col-md-6">
                      <div class="pb-3 mb-4 border-bottom">
                         <h6 class="mb-3 text-primary">
                            <i class="ti ti-building me-2"></i>Basic Information
                         </h6>

                         <div class="mb-3">
                            <label class="form-label fw-bold text-muted small">BRANCH NAME</label>
                            <div class="fs-6">{{ $viewingBranch->name }}</div>
                         </div>

                         <div class="mb-3">
                            <label class="form-label fw-bold text-muted small">ORGANIZATION NAME</label>
                            <div class="fs-6">{{ $viewingBranch->organization_name ?? 'N/A' }}</div>
                         </div>

                         <div class="mb-3">
                            <label class="form-label fw-bold text-muted small">BUSINESS REGISTRATION NO</label>
                            <div class="fs-6">{{ $viewingBranch->business_registration_no ?? 'N/A' }}</div>
                         </div>

                         <div class="mb-3">
                            <label class="form-label fw-bold text-muted small">BUSINESS REGISTRATION DOCUMENT</label>
                            <div>
                               @if($viewingBranch->business_registration_document)
                                  <a href="{{ Storage::url($viewingBranch->business_registration_document) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                     <i class="ti ti-file-text me-1"></i>View Document
                                  </a>
                               @else
                                  <span class="text-muted">No document uploaded</span>
                               @endif
                            </div>
                         </div>

                         <div class="mb-0">
                            <label class="form-label fw-bold text-muted small">STATUS</label>
                            <div>
                               @if($viewingBranch->status === 'active')
                                  <span class="px-3 py-2 badge bg-success fs-6">
                                     <i class="ti ti-check me-1"></i>Active
                                  </span>
                               @elseif($viewingBranch->status === 'pending')
                                  <span class="px-3 py-2 badge bg-warning fs-6">
                                     <i class="ti ti-clock me-1"></i>Pending
                                  </span>
                               @else
                                  <span class="px-3 py-2 badge bg-secondary fs-6">
                                     <i class="ti ti-x me-1"></i>Inactive
                                  </span>
                               @endif
                            </div>
                         </div>
                      </div>

                      <!-- Bank Information Section -->
                      <div class="mb-4">
                         <h6 class="mb-3 text-primary">
                            <i class="ti ti-credit-card me-2"></i>Bank Account Information
                         </h6>

                         @if($paymentMethod === 'main_cooperative')
                            <div class="border-0 alert alert-info">
                               <div class="d-flex align-items-center">
                                  <i class="ti ti-info-circle me-2 fs-5"></i>
                                  <div>
                                     <strong>Main Cooperative Account</strong>
                                     <div class="mt-1 small text-muted">This branch uses the main cooperative bank account for all transactions. Individual bank setup is not required.</div>
                                  </div>
                               </div>
                            </div>
                         @else
                            <div class="mb-3">
                               <label class="form-label fw-bold text-muted small">BANK NAME</label>
                               <div class="fs-6">{{ $viewingBranch->bank_name ?? 'N/A' }}</div>
                            </div>
                            <div class="mb-3">
                               <label class="form-label fw-bold text-muted small">ACCOUNT NAME</label>
                               <div class="fs-6">{{ $viewingBranch->account_name ?? 'N/A' }}</div>
                            </div>
                            <div class="mb-0">
                               <label class="form-label fw-bold text-muted small">ACCOUNT NUMBER</label>
                               <div class="fs-6">{{ $viewingBranch->account_number ?? 'N/A' }}</div>
                            </div>
                         @endif
                      </div>
                   </div>

                   <!-- Right Column - Registration & Tracking -->
                   <div class="col-md-6">
                      @if($viewingBranch->status === 'active')
                         <!-- Registration Settings -->
                         <div class="pb-3 mb-4 border-bottom">
                            <h6 class="mb-3 text-primary">
                               <i class="ti ti-user-plus me-2"></i>Registration Settings
                            </h6>

                            <div class="mb-3">
                               <label class="form-label fw-bold text-muted small">REGISTRATION STATUS</label>
                               <div class="gap-3 d-flex align-items-center">
                                  @if($viewingBranch->registration_enabled ?? true)
                                     <span class="px-3 py-2 badge bg-success fs-6">
                                        <i class="ti ti-check me-1"></i>Registration Enabled
                                     </span>
                                  @else
                                     <span class="px-3 py-2 badge bg-danger fs-6">
                                        <i class="ti ti-x me-1"></i>Registration Disabled
                                     </span>
                                  @endif

                                  <!-- Toggle Button - Available for both payment methods -->
                                  <button type="button"
                                          class="btn btn-sm {{ ($viewingBranch->registration_enabled ?? true) ? 'btn-outline-danger' : 'btn-outline-success' }}"
                                          wire:click="toggleRegistrationStatus('{{ $viewingBranch->id }}')"
                                          wire:loading.attr="disabled">
                                     <span wire:loading.remove wire:target="toggleRegistrationStatus">
                                        <i class="ti {{ ($viewingBranch->registration_enabled ?? true) ? 'ti-x' : 'ti-check' }} me-1"></i>
                                        {{ ($viewingBranch->registration_enabled ?? true) ? 'Disable' : 'Enable' }}
                                     </span>
                                     <span wire:loading wire:target="toggleRegistrationStatus">
                                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                        Updating...
                                     </span>
                                  </button>
                               </div>

                               <small class="mt-2 text-muted d-block">
                                  @if($paymentMethod === 'main_cooperative')
                                     <i class="ti ti-info-circle me-1"></i>
                                     This branch is controlled by Main Cooperative payment method. All payments will be processed centrally.
                                  @else
                                     <i class="ti ti-info-circle me-1"></i>
                                     Control whether new agents can register using the registration URL below.
                                  @endif
                               </small>
                            </div>

                            <div class="mb-0">
                               <label class="form-label fw-bold text-muted small">REGISTRATION URL</label>
                               <div class="input-group">
                                  <input type="text"
                                         class="form-control {{ !($viewingBranch->registration_enabled ?? true) ? 'bg-light text-muted' : '' }}"
                                         value="{{ ($viewingBranch->registration_enabled ?? true) && $viewingBranch->registration_token ? url('/register/' . $viewingBranch->registration_token) : 'Registration is disabled' }}"
                                         readonly
                                         id="view-registration-url">
                                  @if(($viewingBranch->registration_enabled ?? true) && $viewingBranch->registration_token)
                                     <button class="btn btn-outline-secondary"
                                             type="button"
                                             onclick="copyToClipboard('view-registration-url')"
                                             title="Copy to clipboard">
                                        <i class="ti ti-copy"></i>
                                     </button>
                                  @else
                                     <button class="btn btn-outline-secondary" type="button" disabled>
                                        <i class="ti ti-copy"></i>
                                     </button>
                                  @endif
                               </div>
                               <small class="mt-1 text-muted d-block">
                                  @if($viewingBranch->registration_enabled ?? true)
                                     <i class="ti ti-share me-1"></i>Share this URL with agents who want to register under this branch.
                                  @else
                                     <i class="ti ti-ban me-1"></i>Registration is currently disabled. Enable it to allow new agent registrations.
                                  @endif
                               </small>
                            </div>
                         </div>
                      @else
                         <div class="mb-4">
                            <div class="border-0 alert alert-warning">
                               <div class="d-flex align-items-center">
                                  <i class="ti ti-alert-triangle me-2 fs-5"></i>
                                  <div>
                                     <strong>Registration Unavailable</strong>
                                     <div class="mt-1 small text-muted">Registration settings will be available once the branch is approved and activated.</div>
                                  </div>
                               </div>
                            </div>
                         </div>
                      @endif

                      <!-- Tracking Information -->
                      <div class="mb-0">
                         <h6 class="mb-3 text-primary">
                            <i class="ti ti-clock-history me-2"></i>Tracking Information
                         </h6>

                         <div class="mb-3">
                            <label class="form-label fw-bold text-muted small">CREATED BY</label>
                            <div class="fs-6">{{ $viewingBranch->created_by_name ?? 'N/A' }}</div>
                         </div>

                         <div class="mb-3">
                            <label class="form-label fw-bold text-muted small">CREATED AT</label>
                            <div class="fs-6">{{ $viewingBranch->created_at ? \Carbon\Carbon::parse($viewingBranch->created_at)->format('d M Y, h:i A') : 'N/A' }}</div>
                         </div>

                         <div class="mb-3">
                            <label class="form-label fw-bold text-muted small">VERIFIED BY</label>
                            <div class="fs-6">{{ $viewingBranch->verified_by_name ?? 'N/A' }}</div>
                         </div>

                         <div class="mb-0">
                            <label class="form-label fw-bold text-muted small">VERIFIED AT</label>
                            <div class="fs-6">{{ $viewingBranch->verified_at ? \Carbon\Carbon::parse($viewingBranch->verified_at)->format('d M Y, h:i A') : 'N/A' }}</div>
                         </div>
                      </div>
                   </div>
                </div>
                @else
                <div class="py-5 text-center text-muted">
                   <i class="mb-3 ti ti-folder-x fs-1 text-muted"></i>
                   <div>No branch selected.</div>
                </div>
                @endif
             </div>
             <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" wire:click="closeViewBranchModal">Close</button>
             </div>
          </div>
       </div>
    </div>

 </div> {{-- ✅ Closing main Livewire root wrapper --}}

<script>
document.addEventListener('livewire:initialized', () => {
    // Handle modal closing from Livewire
    Livewire.on('close-modal', (event) => {
        const modalId = event.modalId;
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }
    });

    // Handle showing modals from Livewire
    Livewire.on('show-modal', (event) => {
        const modalId = event.modalId;
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        }
    });

    // Handle success alerts
    Livewire.on('show-success-alert', (event) => {
        const message = event.message;
        // Implement your success alert UI here
        // For example using SweetAlert2 or your preferred alert library
        console.log('Success:', message);
    });

    // Handle error alerts
    Livewire.on('show-error-alert', (event) => {
        const message = event.message;
        // Implement your error alert UI here
        // For example using SweetAlert2 or your preferred alert library
        console.log('Error:', message);
    });
});

// Copy to clipboard function
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;
    element.select();
    element.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        // Show success message
        const button = element.nextElementSibling;
        if (button) {
            const originalIcon = button.innerHTML;
            button.innerHTML = '<i class="ti ti-check"></i>';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');
            setTimeout(() => {
                button.innerHTML = originalIcon;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        }
    } catch (err) {
        console.error('Failed to copy: ', err);
    }
}
</script>
