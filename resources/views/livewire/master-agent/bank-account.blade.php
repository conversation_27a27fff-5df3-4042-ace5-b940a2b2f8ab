<div>
    @php
    use Illuminate\Support\Facades\Storage;
    @endphp
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Bank Account Management</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="feather icon-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="feather icon-alert-circle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Main Content -->
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Bank Accounts</h5>
                    <button wire:click="openCreateModal" class="btn btn-primary">
                        <i class="feather icon-plus me-2"></i>Add Bank Account
                    </button>
                </div>
                <div class="card-body">
                    @if(count($bankAccounts) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Bank Name</th>
                                        <th>Account Number</th>
                                        <th>Account Holder</th>
                                        <th>Status</th>
                                        <th>Bank Statement</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($bankAccounts as $bank)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($bank->is_default)
                                                        <span class="badge bg-primary me-2">Default</span>
                                                    @endif
                                                    {{ $bank->bankName->bank_name }}
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-muted">{{ substr($bank->account_number, 0, 4) }}</span>****<span class="text-muted">{{ substr($bank->account_number, -4) }}</span>
                                            </td>
                                            <td>{{ $bank->account_holder_name }}</td>
                                            <td>
                                                @if($bank->is_default)
                                                    <span class="badge bg-success">Default Account</span>
                                                @else
                                                    <span class="badge bg-secondary">Secondary Account</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($bank->bank_statement)
                                                    <a href="{{ Storage::url($bank->bank_statement) }}" target="_blank" class="btn btn-sm btn-outline-info">
                                                        <i class="feather icon-eye me-1"></i>View
                                                    </a>
                                                @else
                                                    <span class="text-muted">No file</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    @if(!$bank->is_default)
                                                        <button wire:click="setAsDefault({{ $bank->id }})"
                                                                class="btn btn-sm btn-outline-success"
                                                                title="Set as Default"
                                                                wire:loading.attr="disabled"
                                                                wire:target="setAsDefault({{ $bank->id }})">
                                                            <span wire:loading.remove wire:target="setAsDefault({{ $bank->id }})">
                                                                <i class="feather icon-star"></i>
                                                            </span>
                                                            <span wire:loading wire:target="setAsDefault({{ $bank->id }})">
                                                                <span class="spinner-border spinner-border-sm" role="status"></span>
                                                            </span>
                                                        </button>
                                                    @endif
                                                    <button wire:click="openEditModal({{ $bank->id }})"
                                                            class="btn btn-sm btn-outline-primary"
                                                            title="Edit">
                                                        <i class="feather icon-edit"></i>
                                                    </button>
                                                    @if(!$bank->is_default)
                                                        <button wire:click="confirmDelete({{ $bank->id }})"
                                                                class="btn btn-sm btn-outline-danger"
                                                                title="Delete">
                                                            <i class="feather icon-trash-2"></i>
                                                        </button>
                                                    @else
                                                        <button class="btn btn-sm btn-outline-secondary"
                                                                title="Cannot delete default account"
                                                                disabled>
                                                            <i class="feather icon-shield"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="py-4 text-center">
                            <i class="feather icon-credit-card text-muted" style="font-size: 48px;"></i>
                            <h6 class="mt-3 text-muted">No Bank Accounts Found</h6>
                            <p class="text-muted">Add your first bank account to get started</p>
                            <button wire:click="openCreateModal" class="mt-2 btn btn-primary">
                                <i class="feather icon-plus me-2"></i>Add Bank Account
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Bank Account Modal -->
    @if($showModal)
        <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ $editMode ? 'Edit Bank Account' : 'Add New Bank Account' }}</h5>
                        <button type="button" class="btn-close" wire:click="closeModal" aria-label="Close"></button>
                    </div>
                    <form wire:submit.prevent="save">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bank_name" class="form-label">Bank Name <span class="text-danger">*</span></label>
                                        <select wire:model="acs_bank_names_id" class="form-control @error('acs_bank_names_id') is-invalid @enderror">
                                            <option value="">Select Bank</option>
                                            @foreach($bankNames as $bankName)
                                                <option value="{{ $bankName->id }}">{{ $bankName->bank_name }}</option>
                                            @endforeach
                                        </select>
                                        @error('acs_bank_names_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="account_number" class="form-label">Account Number <span class="text-danger">*</span></label>
                                        <input type="text"
                                               wire:model="account_number"
                                               class="form-control @error('account_number') is-invalid @enderror"
                                               placeholder="Enter account number">
                                        @error('account_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="account_holder_name" class="form-label">Account Holder Name <span class="text-danger">*</span></label>
                                        <input type="text"
                                               wire:model="account_holder_name"
                                               class="form-control @error('account_holder_name') is-invalid @enderror"
                                               placeholder="Enter account holder name">
                                        @error('account_holder_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="bank_statement" class="form-label">Bank Statement</label>
                                        <input type="file"
                                               wire:model="bank_statement"
                                               class="form-control @error('bank_statement') is-invalid @enderror"
                                               accept="application/pdf,image/jpeg,image/jpg,image/png">
                                        <div class="form-text">Upload bank statement (PDF, JPG, JPEG, PNG - Max 2MB)</div>
                                        @error('bank_statement')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-check">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               wire:model="is_default"
                                               id="is_default">
                                        <label class="form-check-label" for="is_default">
                                            Set as default bank account
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" wire:click="closeModal">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="feather icon-save me-2"></i>{{ $editMode ? 'Update' : 'Save' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="modal-backdrop fade show"></div>
    @endif

    <!-- Delete Confirmation Modal -->
    @if($showDeleteModal)
        <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Delete</h5>
                        <button type="button" class="btn-close" wire:click="closeDeleteModal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <i class="feather icon-alert-triangle text-danger" style="font-size: 48px;"></i>
                            <h6 class="mt-3">Are you sure?</h6>
                            <p class="text-muted">This action cannot be undone. The bank account will be permanently deleted.</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeDeleteModal">Cancel</button>
                        <button type="button" class="btn btn-danger" wire:click="delete">
                            <i class="feather icon-trash-2 me-2"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-backdrop fade show"></div>
    @endif

    <style>
        .modal {
            z-index: 1050;
        }
        .modal-backdrop {
            z-index: 1040;
        }
        .btn-loading {
            pointer-events: none;
            opacity: 0.6;
        }
    </style>

    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('bankAccountUpdated', () => {
                // Force page refresh or show feedback
                console.log('Bank account updated successfully');
            });
        });

        // Add loading state to set default buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('button[wire\\:click*="setAsDefault"]')) {
                const button = e.target.closest('button');
                button.classList.add('btn-loading');
                button.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status"></span>Setting...';

                // Remove loading state after 3 seconds as fallback
                setTimeout(() => {
                    if (button.classList.contains('btn-loading')) {
                        button.classList.remove('btn-loading');
                        location.reload();
                    }
                }, 3000);
            }
        });
    </script>
</div>
