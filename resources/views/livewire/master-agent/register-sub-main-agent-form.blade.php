<div>
    <style>
        .permission-table {
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            overflow: hidden;
        }
        .permission-table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            font-weight: 600;
            padding: 0.75rem;
            text-align: center;
        }
        .permission-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }
        .permission-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .permission-group-header {
            background-color: #e3f2fd;
            font-weight: 600;
            color: #1976d2;
        }
        .permission-checkbox {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .permission-checkbox input[type="checkbox"] {
            transform: scale(1.2);
        }
        .resource-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .resource-icon {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.25rem;
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .permission-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        .badge-view { background-color: #d1ecf1; color: #0c5460; }
        .badge-create { background-color: #d4edda; color: #155724; }
        .badge-edit { background-color: #fff3cd; color: #856404; }
        .badge-delete { background-color: #f8d7da; color: #721c24; }

        .menu-item-card {
            transition: all 0.2s ease;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.75rem;
        }
        .menu-item-card:hover {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }
        .menu-item-card.selected {
            background-color: #fff3cd;
            border-color: #ffc107;
        }
    </style>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                @this.call('copyLink');
            });
        }
    </script>

    {{-- Page Header --}}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Register New Sub-Main-Agent</h2>
                        <p class="mt-2 mb-0 text-muted">Create a new sub-main-agent account with custom permissions</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Main Content --}}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Registration Form</h5>
                        <button type="button" class="btn btn-secondary" wire:click="backToList">
                            <i class="ph-duotone ph-arrow-left me-2"></i>Back to List
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    @if(!$showLinkCopy)
                        <form wire:submit="register">
                            {{-- Basic Information --}}
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" wire:model="name" placeholder="Enter full name">
                                        @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control @error('email') is-invalid @enderror" wire:model="email" placeholder="Enter email address">
                                        @error('email') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>
                            </div>

                            {{-- UI Settings --}}
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label">Menu Visibility Settings</label>
                                        <div class="mb-3 alert alert-info">
                                            <i class="ph-duotone ph-info me-2"></i>
                                            Select which menu items should be hidden from this user's interface. Unchecked items will be visible.
                                        </div>

                                        @php
                                            $availableMenus = [
                                                'dashboard' => [
                                                    'title' => 'Dashboard',
                                                    'icon' => 'ph-house',
                                                    'description' => 'Main dashboard and overview'
                                                ],
                                                'profile' => [
                                                    'title' => 'Profile Management',
                                                    'icon' => 'ph-user-circle',
                                                    'description' => 'Personal profile settings'
                                                ],
                                                'agents' => [
                                                    'title' => 'Agent Management',
                                                    'icon' => 'ph-handshake',
                                                    'description' => 'Manage agents and registrations'
                                                ],
                                                'branch' => [
                                                    'title' => 'Branch Management',
                                                    'icon' => 'ph-buildings',
                                                    'description' => 'Manage branch locations'
                                                ],
                                                'reports' => [
                                                    'title' => 'Reports & Analytics',
                                                    'icon' => 'ph-chart-bar',
                                                    'description' => 'Generate and view reports'
                                                ],
                                                'agreements' => [
                                                    'title' => 'Agreements',
                                                    'icon' => 'ph-file-text',
                                                    'description' => 'Manage agreement documents'
                                                ],
                                                'bank_account' => [
                                                    'title' => 'Banking Information',
                                                    'icon' => 'ph-bank',
                                                    'description' => 'Banking details and information'
                                                ],
                                                'password_management' => [
                                                    'title' => 'Password Management',
                                                    'icon' => 'ph-lock-key',
                                                    'description' => 'Password resets and security'
                                                ],
                                                'settings' => [
                                                    'title' => 'System Settings',
                                                    'icon' => 'ph-gear',
                                                    'description' => 'System configuration'
                                                ],
                                                'notifications' => [
                                                    'title' => 'Notifications',
                                                    'icon' => 'ph-bell',
                                                    'description' => 'System notifications and alerts'
                                                ]
                                            ];
                                        @endphp

                                        <div class="row">
                                            @foreach($availableMenus as $menuKey => $menuData)
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="mb-3">
                                                        <div class="menu-item-card {{ in_array($menuKey, $hiddenMenus ?? []) ? 'selected' : '' }}">
                                                            <div class="form-check d-flex align-items-start">
                                                                <input class="mt-1 form-check-input" type="checkbox"
                                                                       wire:model="hiddenMenus"
                                                                       value="{{ $menuKey }}"
                                                                       id="hideMenu_{{ $menuKey }}">
                                                                <div class="ms-2 flex-grow-1">
                                                                    <label class="cursor-pointer form-check-label fw-medium d-block" for="hideMenu_{{ $menuKey }}">
                                                                        <i class="ph-duotone {{ $menuData['icon'] }} me-2 text-warning"></i>
                                                                        Hide {{ $menuData['title'] }}
                                                                    </label>
                                                                    <div class="text-muted small">{{ $menuData['description'] }}</div>
                                                                    @if(in_array($menuKey, $hiddenMenus ?? []))
                                                                        <div class="mt-1">
                                                                            <span class="badge bg-warning text-dark small">
                                                                                <i class="ph-duotone ph-eye-slash me-1"></i>Will be hidden
                                                                            </span>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>

                                        @if(!empty($hiddenMenus))
                                            <div class="mt-3 alert alert-warning">
                                                <h6 class="alert-heading">
                                                    <i class="ph-duotone ph-eye-slash me-2"></i>Hidden Menu Items
                                                </h6>
                                                <p class="mb-2">The following menu items will be hidden from this user:</p>
                                                <div class="flex-wrap gap-2 d-flex">
                                                    @foreach($hiddenMenus as $hiddenMenu)
                                                        @php $menuInfo = $availableMenus[$hiddenMenu] ?? ['title' => ucfirst($hiddenMenu)] @endphp
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="ph-duotone {{ $menuInfo['icon'] ?? 'ph-circle' }} me-1"></i>
                                                            {{ $menuInfo['title'] }}
                                                        </span>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <hr class="my-4">

                            {{-- Permissions Section --}}
                            <div class="mb-3">
                                <label class="form-label">Set Access Permissions</label>
                                <div class="mb-3 alert alert-info">
                                    <i class="ph-duotone ph-info me-2"></i>
                                    Select multiple permissions for each section as needed. You can check multiple boxes per resource and change these permissions anytime after registration.
                                </div>

                                @php
                                    $permissionDetails = [
                                        'dashboard' => [
                                            'title' => 'Dashboard',
                                            'icon' => 'ph-house',
                                            'group' => 'Basic Access',
                                            'description' => 'Main dashboard and statistics'
                                        ],
                                        'profile' => [
                                            'title' => 'Profile Management',
                                            'icon' => 'ph-user-circle',
                                            'group' => 'Basic Access',
                                            'description' => 'Personal profile and settings'
                                        ],
                                        'agents' => [
                                            'title' => 'Agent Management',
                                            'icon' => 'ph-handshake',
                                            'group' => 'Management',
                                            'description' => 'Manage agents and registrations'
                                        ],
                                        'branch' => [
                                            'title' => 'Branch Management',
                                            'icon' => 'ph-buildings',
                                            'group' => 'Management',
                                            'description' => 'Manage branch locations'
                                        ],
                                        'reports' => [
                                            'title' => 'Reports & Analytics',
                                            'icon' => 'ph-chart-bar',
                                            'group' => 'Data Management',
                                            'description' => 'Generate and view reports'
                                        ],
                                        'agreements' => [
                                            'title' => 'Agreements',
                                            'icon' => 'ph-file-text',
                                            'group' => 'Data Management',
                                            'description' => 'Manage agreement documents'
                                        ],
                                        'bank_account' => [
                                            'title' => 'Banking Information',
                                            'icon' => 'ph-bank',
                                            'group' => 'Security',
                                            'description' => 'Banking details and information'
                                        ],
                                        'password_management' => [
                                            'title' => 'Password Management',
                                            'icon' => 'ph-lock-key',
                                            'group' => 'Security',
                                            'description' => 'Password resets and security'
                                        ]
                                    ];

                                    // Group permissions by their group
                                    $groupedPermissions = [];
                                    foreach($availablePermissions as $resource => $actions) {
                                        $detail = $permissionDetails[$resource] ?? [];
                                        $group = $detail['group'] ?? 'Other';
                                        $groupedPermissions[$group][$resource] = $actions;
                                    }
                                @endphp

                                <div class="table-responsive">
                                    <table class="table mb-0 permission-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 30%;">Resource</th>
                                                <th style="width: 35%;">Description</th>
                                                <th style="width: 10%;">View</th>
                                                <th style="width: 10%;">Create</th>
                                                <th style="width: 10%;">Edit</th>
                                                <th style="width: 5%;">Delete</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($groupedPermissions as $groupName => $groupPermissions)
                                                <tr>
                                                    <td colspan="6" class="permission-group-header">
                                                        <i class="ph-duotone ph-folder me-2"></i>{{ $groupName }}
                                                    </td>
                                                </tr>
                                                @foreach($groupPermissions as $resource => $actions)
                                                    @php
                                                        $detail = $permissionDetails[$resource] ?? [];
                                                        $currentPermissions = $permissions[$resource] ?? ['view'];
                                                        // Ensure currentPermissions is always an array
                                                        if (!is_array($currentPermissions)) {
                                                            $currentPermissions = [$currentPermissions];
                                                        }
                                                    @endphp
                                                    <tr>
                                                        <td>
                                                            <div class="resource-info">
                                                                <div class="resource-icon">
                                                                    <i class="ph-duotone {{ $detail['icon'] ?? 'ph-gear' }}"></i>
                                                                </div>
                                                                <div>
                                                                    <div class="fw-medium">{{ $detail['title'] ?? ucfirst(str_replace('_', ' ', $resource)) }}</div>
                                                                    <div class="text-nowrap">
                                                                        @foreach($currentPermissions as $perm)
                                                                            <span class="permission-badge badge-{{ $perm }} me-1">
                                                                                {{ ucfirst($perm) }}
                                                                            </span>
                                                                        @endforeach
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">{{ $detail['description'] ?? 'Access to this section' }}</small>
                                                        </td>
                                                        @foreach(['view', 'create', 'edit', 'delete'] as $action)
                                                            <td>
                                                                @if(in_array($action, $actions))
                                                                    <div class="permission-checkbox">
                                                                        <input class="form-check-input" type="checkbox"
                                                                               id="{{ $action }}_{{ $resource }}"
                                                                               wire:change="togglePermission('{{ $resource }}', '{{ $action }}')"
                                                                               {{ in_array($action, $currentPermissions) ? 'checked' : '' }}>
                                                                    </div>
                                                                @else
                                                                    <div class="text-center text-muted">-</div>
                                                                @endif
                                                            </td>
                                                        @endforeach
                                                    </tr>
                                                @endforeach
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                {{-- Permission Summary --}}
                                <div class="mt-4 row">
                                    <div class="col-md-6">
                                        <div class="alert alert-success">
                                            <h6 class="alert-heading d-flex align-items-center">
                                                <i class="ph-duotone ph-check-circle me-2"></i>
                                                Access Summary
                                            </h6>
                                            @php
                                                $permissionCounts = ['view' => 0, 'create' => 0, 'edit' => 0, 'delete' => 0];
                                                foreach($permissions as $resource => $resourcePermissions) {
                                                    // Ensure resourcePermissions is an array
                                                    if (!is_array($resourcePermissions)) {
                                                        $resourcePermissions = [$resourcePermissions];
                                                    }
                                                    foreach($resourcePermissions as $permission) {
                                                        if(isset($permissionCounts[$permission])) {
                                                            $permissionCounts[$permission]++;
                                                        }
                                                    }
                                                }
                                                $totalSections = count($permissions);
                                            @endphp
                                            <p class="mb-2">This user will have access to <strong>{{ $totalSections }}</strong> sections with:</p>
                                            <ul class="mb-0">
                                                @if($permissionCounts['view'] > 0)<li><strong>{{ $permissionCounts['view'] }}</strong> sections with View access</li>@endif
                                                @if($permissionCounts['create'] > 0)<li><strong>{{ $permissionCounts['create'] }}</strong> sections with Create access</li>@endif
                                                @if($permissionCounts['edit'] > 0)<li><strong>{{ $permissionCounts['edit'] }}</strong> sections with Edit access</li>@endif
                                                @if($permissionCounts['delete'] > 0)<li><strong>{{ $permissionCounts['delete'] }}</strong> sections with Delete access</li>@endif
                                            </ul>
                                            @if(!empty($hiddenMenus))
                                                <div class="mt-2">
                                                    <span class="badge bg-warning"><i class="ph-duotone ph-eye-slash me-1"></i>{{ count($hiddenMenus) }} Menu Items Hidden</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <h6 class="alert-heading d-flex align-items-center">
                                                <i class="ph-duotone ph-info me-2"></i>
                                                Permission Guide
                                            </h6>
                                            <ul class="mb-2">
                                                <li><strong>View:</strong> Can see information only</li>
                                                <li><strong>Create:</strong> Can add new items</li>
                                                <li><strong>Edit:</strong> Can modify existing items</li>
                                                <li><strong>Delete:</strong> Can remove items</li>
                                            </ul>
                                            <small class="text-muted">
                                                <i class="ph-duotone ph-check-square me-1"></i>
                                                You can select multiple permissions per section (e.g., both View and Edit)
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- Form Actions --}}
                            <div class="gap-2 mt-4 d-flex justify-content-end">
                                <button type="button" class="btn btn-secondary" wire:click="backToList">Cancel</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="ph-duotone ph-plus me-2"></i>Register Sub-Main-Agent
                                </button>
                            </div>
                        </form>
                    @else
                        {{-- Success Message with Link --}}
                        <div class="alert alert-success">
                            <h6 class="alert-heading">Registration Successful!</h6>
                            <p class="mb-2">Sub-main-agent has been registered successfully. Share the registration link below:</p>
                            <div class="mt-3 input-group">
                                <input type="text" class="form-control" value="{{ $registrationLink }}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $registrationLink }}')">
                                    <i class="ph-duotone ph-copy"></i> Copy
                                </button>
                            </div>
                            <small class="text-muted">The user will need to use this link to set their password and complete registration.</small>
                        </div>

                        <div class="gap-2 mt-4 d-flex justify-content-end">
                            <button type="button" class="btn btn-primary" wire:click="backToList">
                                <i class="ph-duotone ph-arrow-left me-2"></i>Back to List
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
