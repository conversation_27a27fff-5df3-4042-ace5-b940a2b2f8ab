<div>
    <style>
        .table-responsive {
            overflow-x: auto !important;
            overflow-y: visible !important;
        }
        .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }
        .dropdown {
            position: static !important;
        }
        .card {
            overflow: visible !important;
        }

        .sub-agent-sortable {
            cursor: pointer;
            transition: all 0.2s;
        }
        .sub-agent-sortable:hover {
            background-color: #f8f9fa;
        }
        .sub-agent-sortable.active {
            background-color: #e3f2fd;
        }


    </style>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                @this.call('copyLink');
            });
        }
    </script>

    {{-- Page Header --}}
    <div class="page-header">
        <div class="page-block">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <div class="page-header-title">
                        <h2 class="mb-0">Sub Main Agent Management</h2>
                        <p class="mt-2 mb-0 text-muted">Register and manage sub-main-agents with custom permissions</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Statistics Cards --}}
    <div class="mb-4 row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-primary">
                            <i class="ph-duotone ph-users"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Total Sub-Main-Agents</p>
                            <h4 class="mb-0">{{ $subMainAgents->total() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-success">
                            <i class="ph-duotone ph-check-circle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Active</p>
                            <h4 class="mb-0">{{ $subMainAgents->where('status', 'active')->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-warning">
                            <i class="ph-duotone ph-clock"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Pending</p>
                            <h4 class="mb-0">{{ $subMainAgents->where('status', 'pending')->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avtar avtar-s bg-light-danger">
                            <i class="ph-duotone ph-x-circle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-1 text-muted">Suspended</p>
                            <h4 class="mb-0">{{ $subMainAgents->where('status', 'suspended')->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Main Content Card --}}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Sub-Main-Agent List</h5>
                <a href="{{ route('master_agent.register-sub-main-agent') }}" class="btn btn-primary">
                    <i class="ph-duotone ph-plus me-2"></i>Register New Sub-Main-Agent
                </a>
            </div>
        </div>

        <div class="card-body">
            {{-- Search and Filters --}}
            <div class="mb-3 row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" wire:model.live="search" placeholder="Search by name or email...">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">Status Filter</label>
                        <select class="form-control" wire:model.live="statusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="suspended">Suspended</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">Per Page</label>
                        <select class="form-control" wire:model.live="perPage">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>
            </div>

            {{-- Table --}}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Permissions Summary</th>
                            <th>Created</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($subMainAgents as $subAgent)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avtar avtar-xs bg-light-secondary me-2">
                                            <i class="ph-duotone ph-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $subAgent->name }}</h6>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ $subAgent->email }}</td>
                                <td>
                                    @if($subAgent->status === 'active')
                                        <span class="badge bg-success">Active</span>
                                    @elseif($subAgent->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @else
                                        <span class="badge bg-danger">Suspended</span>
                                    @endif
                                </td>
                                <td>
                                    @if($subAgent->receivedPermissions)
                                        @php
                                            $permissions = $subAgent->receivedPermissions->permissions;
                                            $editCount = collect($permissions)->filter(fn($p) => $p === 'edit')->count();
                                            $viewCount = collect($permissions)->filter(fn($p) => $p === 'view')->count();
                                        @endphp
                                        <small class="text-muted">
                                            {{ $editCount }} Edit, {{ $viewCount }} View
                                        </small>
                                    @else
                                        <small class="text-muted">No permissions set</small>
                                    @endif
                                </td>
                                <td>{{ $subAgent->created_at->format('M d, Y') }}</td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" wire:click="viewDetails({{ $subAgent->id }})">View Details</a></li>
                                            <li><a class="dropdown-item" href="{{ route('master_agent.manage-permissions', ['userId' => $subAgent->id]) }}" >Manage Permissions</a></li>
                                            @if($subAgent->status === 'active')
                                                <li><a class="dropdown-item text-danger" href="#" wire:click="deactivateUser({{ $subAgent->id }})">Deactivate</a></li>
                                            @else
                                                <li><a class="dropdown-item text-success" href="#" wire:click="activateUser({{ $subAgent->id }})">Activate</a></li>
                                            @endif
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="py-4 text-center">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="ph-duotone ph-users-three" style="font-size: 3rem; color: #6c757d;"></i>
                                        <p class="mt-2 mb-0 text-muted">No sub-main-agents found</p>
                                        <small class="text-muted">Click "Register New Sub-Main-Agent" to get started</small>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            @if($subMainAgents->hasPages())
                <div class="mt-3">
                    {{ $subMainAgents->links() }}
                </div>
            @endif
        </div>
    </div>





    {{-- Detail Modal --}}
    @if($showDetailModal && $selectedSubAgent)
        <div class="modal fade show" style="display: block;" tabindex="-1" aria-modal="true" wire:click.self="$set('showDetailModal', false)">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Sub-Main-Agent Details</h5>
                        <button type="button" class="btn-close" wire:click="$set('showDetailModal', false)"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Personal Information</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{{ $selectedSubAgent->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ $selectedSubAgent->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ $selectedSubAgent->phone ?: 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            @if($selectedSubAgent->status === 'active')
                                                <span class="badge bg-success">Active</span>
                                            @elseif($selectedSubAgent->status === 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @else
                                                <span class="badge bg-danger">Suspended</span>
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Current Permissions</h6>
                                @if($selectedSubAgent->receivedPermissions)
                                    <div class="row">
                                        @foreach($selectedSubAgent->receivedPermissions->permissions as $resource => $permission)
                                            <div class="mb-2 col-12">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span>{{ ucfirst(str_replace('_', ' ', $resource)) }}:</span>
                                                    <span class="badge {{ $permission === 'edit' ? 'bg-success' : 'bg-primary' }}">
                                                        {{ ucfirst($permission) }}
                                                    </span>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-muted">No permissions set</p>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="$set('showDetailModal', false)">Close</button>
                        <a href="{{ route('master_agent.manage-permissions', ['userId' => $selectedSubAgent->id]) }}" class="btn btn-primary">Manage Permissions</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-backdrop fade show" style="background-color: rgba(0,0,0,0.4);"></div>
    @endif
</div>
