# Branch Approval System

## Overview

The Branch Approval System allows administrators to approve or reject branch applications with reasons, maintain a complete audit trail, and automatically notify branch creators via email.

## Features

### 1. Approval/Rejection with Reasons
- **Approval**: Optional reason field for approving branches
- **Rejection**: Required reason field for declining branches
- **Validation**: Ensures rejection reasons are at least 10 characters and maximum 500 characters

### 2. Database History Tracking
- **Complete Audit Trail**: All approval/rejection actions are stored in `acs_branch_approval_history` table
- **Status Tracking**: Records previous and new status for each action
- **User Tracking**: Tracks who performed each action
- **Email Status**: Records whether email notifications were sent successfully

### 3. Email Notifications
- **Automatic Notifications**: Branch creators receive email notifications for approval/rejection
- **Custom Templates**: Supports custom email templates via admin panel
- **Comprehensive Information**: Includes branch details, action taken, reason, and action performer
- **Error Handling**: Email failures don't prevent approval/rejection actions

### 4. User Interface
- **Modal Dialogs**: Clean modal interfaces for approval and rejection actions
- **Real-time Validation**: Form validation with helpful error messages
- **Loading States**: Visual feedback during approval/rejection processes
- **History Display**: Approval history shown in branch details modal

## Database Schema

### `acs_branch_approval_history` Table
```sql
- id (Primary Key)
- acs_coorperative_branch_id (Foreign Key)
- action (enum: 'approve', 'decline')
- reason (text, nullable)
- action_by (Foreign Key to acs_users)
- previous_status (enum: 'pending', 'active', 'inactive')
- new_status (enum: 'pending', 'active', 'inactive')
- additional_data (json, nullable)
- created_at, updated_at (timestamps)
```

## Implementation Details

### Models
1. **`AcsBranchApprovalHistory`**: Manages approval history records
2. **`AcsCooperativeBranch`**: Updated with approval history relationship
3. **`BranchApprovalNotification`**: Handles email notifications

### Livewire Components
- **`ListBranches`**: Updated with approval/rejection functionality
- **Modal Management**: Separate modals for approval and rejection
- **Validation**: Real-time form validation
- **Database Transactions**: Ensures data consistency

### Email Templates
The system supports custom email templates with the following placeholders:
- `{creator_name}` - Name of the branch creator
- `{branch_name}` - Name of the branch
- `{cooperative_name}` - Name of the cooperative
- `{action}` - Action taken (approve/decline)
- `{action_display}` - Display name of action
- `{reason}` - Reason provided
- `{action_by}` - Name of person who performed action
- `{action_date}` - Date and time of action
- `{business_registration_no}` - Business registration number
- `{organization_name}` - Organization name

## Usage

### For Administrators
1. Navigate to the Branch List page
2. Click the "Approve" button (✓) for pending branches
3. Enter an optional approval reason
4. Click "Approve Branch" to confirm
5. For rejection, click the "Decline" button (✗)
6. Enter a required rejection reason (minimum 10 characters)
7. Click "Decline Branch" to confirm

### For Branch Creators
1. Receive email notifications when branches are approved/rejected
2. Email includes all relevant details and reasons
3. Can view approval history in branch details

## API Endpoints

The system uses Livewire actions rather than traditional API endpoints:
- `showApprovalModal($branchId)` - Shows approval modal
- `showRejectionModal($branchId)` - Shows rejection modal
- `approveBranch($branchId)` - Approves branch with reason
- `declineBranch($branchId)` - Declines branch with reason

## Testing

Run the test suite to verify functionality:
```bash
php artisan test tests/Feature/BranchApprovalTest.php
```

## Configuration

### Email Configuration
Ensure your email settings are configured in `.env`:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ACS RMS"
```

### Custom Email Templates
To use custom email templates:
1. Create a template in the admin panel
2. Set template type to `branch_approval`
3. Use available placeholders in the template

## Security Features

- **Authorization**: Only admin users can approve/reject branches
- **Validation**: Required validation for rejection reasons
- **Audit Trail**: Complete history of all actions
- **Transaction Safety**: Database transactions ensure data consistency
- **Error Handling**: Graceful handling of email failures

## Future Enhancements

- **Bulk Actions**: Approve/reject multiple branches at once
- **Template Management**: Admin interface for email template management
- **Notification Preferences**: Allow users to set notification preferences
- **SMS Notifications**: Add SMS notifications for urgent updates
- **Approval Workflows**: Multi-level approval workflows for complex scenarios 
