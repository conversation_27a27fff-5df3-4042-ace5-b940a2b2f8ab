# KYC Approval Email System for Main Agents

## Overview

This document describes the KYC (Know Your Customer) approval email notification system implemented for main agents in the ACS RMS application.

## Features

### Email Notifications
- **KYC Approval**: Sends congratulatory email when KYC is approved
- **<PERSON><PERSON><PERSON> Rejection**: Sends rejection email with reason when <PERSON>Y<PERSON> is rejected
- **Customizable Templates**: Supports custom email templates via the admin panel
- **Dashboard Links**: Includes direct links to the main agent dashboard

### Implementation Details

#### Files Modified/Created

1. **`app/Notifications/MainAgentKycApprovalNotification.php`**
   - New notification class for main agent KYC status updates
   - Supports both approval and rejection scenarios
   - Uses custom email templates when available
   - Falls back to default templates if custom templates are not configured

2. **`app/Livewire/Admin/RegisterMainAgent.php`**
   - Updated `processKycAction()` method to send email notifications
   - Integrated with existing KYC approval/rejection workflow
   - Error handling for email sending failures

3. **`app/Livewire/Admin/EmailTemplateManagement.php`**
   - Added support for `main_agent_kyc_approval` email template type
   - Configured default template with placeholders
   - Added available placeholders for customization

4. **`tests/Feature/MainAgentKycApprovalTest.php`**
   - Unit tests for email notification functionality
   - Tests both approval and rejection scenarios

#### Email Template Placeholders

The following placeholders are available for customizing KYC approval emails:

- `{main_agent_name}` - Name of the main agent
- `{status}` - KYC status (approved/rejected)
- `{reason}` - Reason for rejection (if applicable)
- `{cooperative_name}` - Name of the cooperative
- `{dashboard_url}` - URL to the main agent dashboard

#### Default Email Content

**For Approval:**
```
Subject: KYC Status Update - approved

Hello {main_agent_name}!

Your KYC details have been reviewed and approved.

Congratulations! Your KYC details have been approved.

You now have full access to the ACS RMS system and can start managing your agents and operations.

As a Main Agent, you can now:
• Register and manage sub-agents
• Access commission reports
• View transaction history
• Manage your profile and settings

[Access Dashboard Button]

Thank you for your cooperation.

Best regards,
{cooperative_name} Team
```

**For Rejection:**
```
Subject: KYC Status Update - rejected

Hello {main_agent_name}!

Your KYC details have been reviewed and rejected.

Your KYC details have been reviewed but could not be approved at this time.

Please review and update your information, then submit again for approval.

Reason for rejection:
{reason}

[Update Profile Button]

Thank you for your cooperation.

Best regards,
{cooperative_name} Team
```

## Usage

### For Administrators

1. **KYC Approval Process:**
   - Navigate to Admin → Register Main Agent
   - Select a main agent with pending KYC
   - Click "KYC Management" tab
   - Choose "Approve" or "Reject"
   - Provide reason if rejecting
   - Submit the action

2. **Email Template Customization:**
   - Navigate to Admin → Email Template Management
   - Select "Main Agent KYC Approval Email"
   - Customize subject, body, and button text
   - Save changes

### For Main Agents

- **Approval**: Receive congratulatory email with dashboard access link
- **Rejection**: Receive rejection email with reason and profile update link

## Error Handling

- Email sending failures are logged but don't prevent KYC status updates
- Graceful fallback to default templates if custom templates fail to load
- Comprehensive error logging for debugging

## Testing

Run the test suite to verify email functionality:

```bash
php artisan test tests/Feature/MainAgentKycApprovalTest.php
```

## Configuration

### Email Settings
Ensure your Laravel email configuration is properly set up in `.env`:

```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="ACS RMS"
```

### Queue Configuration (Optional)
For better performance, consider using queues for email sending:

```env
QUEUE_CONNECTION=database
```

Then run:
```bash
php artisan queue:work
```

## Troubleshooting

### Common Issues

1. **Emails not sending:**
   - Check email configuration in `.env`
   - Verify SMTP credentials
   - Check application logs for errors

2. **Template not loading:**
   - Ensure email template exists in database
   - Check template type matches `main_agent_kyc_approval`
   - Verify template is active

3. **Route errors:**
   - Ensure `master_agent.dashboard` route exists
   - Check route caching: `php artisan route:clear`

### Logs

Email sending activities are logged in:
- `storage/logs/laravel.log`
- Look for entries with "MainAgentKycApprovalNotification"

## Future Enhancements

- Add email tracking and delivery confirmation
- Implement email preferences for main agents
- Add support for SMS notifications
- Create email templates for other languages 
