# Branch Created Email Notification System

## Overview

This document describes the email notification system implemented for branch creation in the ACS RMS application. When a Master Agent creates a new branch, the system automatically sends email notifications to all admin users for approval.

## Features

### Email Notifications
- **Branch Creation Alert**: Sends notification to all admins when a new branch is created
- **Customizable Templates**: Supports custom email templates via the admin panel
- **Comprehensive Branch Details**: Includes all relevant branch information in the email
- **Admin Panel Integration**: Direct links to admin panel for branch management

### Implementation Details

#### Files Modified/Created

1. **`app/Notifications/BranchCreatedNotification.php`**
   - New notification class for branch creation alerts
   - Supports custom email templates when available
   - Falls back to default templates if custom templates are not configured
   - Includes comprehensive branch and master agent information

2. **`app/Services/AcsNotificationService.php`**
   - Updated `sendBranchCreatedNotification()` method to send email notifications
   - Integrated with existing in-system notification workflow
   - Error handling for email sending failures
   - Sends notifications to all admin and super-admin users

3. **`app/Livewire/Admin/EmailTemplateManagement.php`**
   - Added support for `branch_created` email template type
   - Configured default template with placeholders
   - Added available placeholders for customization

4. **`tests/Feature/BranchCreatedNotificationTest.php`**
   - Unit tests for email notification functionality
   - Tests notification sending to multiple admins
   - Tests data integrity and edge cases

#### Email Template Placeholders

The following placeholders are available for customizing branch creation emails:

- `{admin_name}` - Name of the admin receiving the email
- `{cooperative_name}` - Name of the cooperative
- `{master_agent_name}` - Name of the master agent who created the branch
- `{master_agent_email}` - Email of the master agent
- `{branch_name}` - Name of the created branch
- `{organization_name}` - Organization name of the branch
- `{business_registration_no}` - Business registration number
- `{created_at}` - Date and time when the branch was created

#### Default Email Content

```
Subject: New Branch Created - Requires Approval - {cooperative_name}

Hello {admin_name},

A Master Agent has created a new branch and it requires your approval.

Branch Details:
• Branch Name: {branch_name}
• Organization: {organization_name}
• Business Registration No: {business_registration_no}
• Created by: {master_agent_name} ({master_agent_email})
• Cooperative: {cooperative_name}
• Created: {created_at}

Please log in to the admin panel to review and approve this branch.

Best regards,
{cooperative_name} Team
```

## Usage

### For Master Agents

1. **Branch Creation Process:**
   - Navigate to Master Agent → Branch Management
   - Fill in branch details (name, organization, registration number, etc.)
   - Upload business registration document (optional)
   - Submit the branch creation form
   - System automatically sends notifications to all admins

### For Administrators

1. **Email Notifications:**
   - Receive immediate email notification when a new branch is created
   - Email contains all relevant branch and master agent information
   - Direct access to admin panel for branch management

2. **Branch Approval Process:**
   - Navigate to Admin → Branch Management
   - View pending branches in the list
   - Click "View Details" to see full branch information
   - Click "Approve" or "Decline" to process the branch

3. **Email Template Customization:**
   - Navigate to Admin → Email Template Management
   - Select "Branch Created Notification Email"
   - Customize subject, body, and placeholders
   - Save changes

## Integration with Existing Systems

### Admin Dashboard
- Branch approval count is displayed on the admin dashboard
- Direct links to branch management from dashboard
- Real-time updates when new branches are created

### Notification System
- Integrates with existing in-system notification system
- Both email and in-system notifications are sent simultaneously
- Consistent with other notification patterns in the application

### Email Template System
- Uses the existing email template management system
- Supports custom templates with placeholders
- Fallback to default templates if custom templates are not configured

## Error Handling

- Email sending failures are logged but don't prevent branch creation
- Graceful fallback to default templates if custom templates fail to load
- Comprehensive error logging for debugging
- In-system notifications continue to work even if email fails

## Testing

Run the test suite to verify email functionality:

```bash
php artisan test tests/Feature/BranchCreatedNotificationTest.php
```

The test suite includes:
- Testing notification sending to multiple admins
- Verifying correct data in notifications
- Testing edge cases (no admins exist)

## Configuration

### Email Settings
Ensure your Laravel email configuration is properly set up in `.env`:

```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

### Database Requirements
The system requires the following database tables:
- `acs_users` - For user and role management
- `acs_coorperative_branch` - For branch data
- `acs_coorperative` - For cooperative information
- `acs_notifications` - For in-system notifications
- `acs_email_templates` - For custom email templates

## Security Considerations

- Only admin and super-admin users receive branch creation notifications
- Email notifications include only necessary information
- No sensitive data (passwords, tokens) are included in emails
- All email content is properly escaped to prevent XSS attacks

## Performance Considerations

- Email notifications are sent asynchronously to prevent blocking
- In-system notifications are created in a single database transaction
- Email template caching is utilized for better performance
- Bulk email sending is optimized for multiple admin users

## Future Enhancements

Potential improvements for the branch creation notification system:

1. **Branch Approval Notifications**: Send notifications to master agents when their branches are approved/rejected
2. **Branch Status Updates**: Notify admins when branch status changes
3. **Branch Statistics**: Include branch statistics in admin dashboard
4. **Custom Approval Workflows**: Support for multi-level approval processes
5. **Branch Expiry Notifications**: Notify admins when branches are approaching expiry dates 
