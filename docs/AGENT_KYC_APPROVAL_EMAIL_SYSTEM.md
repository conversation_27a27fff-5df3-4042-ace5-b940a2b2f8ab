# Agent KYC Approval Email System

## Overview

This document describes the KYC (Know Your Customer) approval email notification system implemented for regular agents in the ACS RMS application.

## Features

### Email Notifications
- **KYC Approval**: Sends congratulatory email when KYC is approved
- **<PERSON><PERSON><PERSON> Rejection**: Sends rejection email with reason when KY<PERSON> is rejected
- **Customizable Templates**: Supports custom email templates via the admin panel
- **Dashboard Links**: Includes direct links to the agent dashboard

### Implementation Details

#### Files Modified/Created

1. **`app/Notifications/AgentKycApprovalNotification.php`**
   - New notification class for agent KYC status updates
   - Supports both approval and rejection scenarios
   - Uses custom email templates when available (template type: `agent_kyc_approval`)
   - Falls back to default templates if custom templates are not configured

2. **`app/Livewire/Admin/RegisterAgent.php`**
   - Updated `processKycAction()` method to send email notifications
   - Integrated with existing KYC approval/rejection workflow
   - Error handling for email sending failures
   - Added import for `AgentKycApprovalNotification`

#### Email Template Placeholders

The following placeholders are available for customizing agent <PERSON><PERSON><PERSON> approval emails:

- `{agent_name}` - Name of the agent
- `{status}` - KYC status (approved/rejected)
- `{reason}` - Reason for rejection (if applicable)
- `{cooperative_name}` - Name of the cooperative
- `{dashboard_url}` - URL to the agent dashboard

#### Default Email Content

**For Approval:**
```
Subject: KYC Status Update - approved

Hello {agent_name}!

Congratulations! Your KYC details have been approved.

You now have full access to the ACS RMS system and can start using all available features.

As an Agent, you can now:
• Access commission reports
• View transaction history
• Manage your profile and settings
• Participate in campaigns and promotions

[Access Dashboard Button]

Thank you for your cooperation.
```

**For Rejection:**
```
Subject: KYC Status Update - rejected

Hello {agent_name}!

Your KYC details have been reviewed but could not be approved at this time.

Please review and update your information, then submit again for approval.

Reason for rejection:
{reason}

[Access Dashboard Button]

Thank you for your cooperation.
```

## How It Works

1. **Admin Action**: Admin reviews agent KYC details and approves/rejects through the RegisterAgent interface
2. **Database Update**: KYC status is updated in the database with reason (if rejected)
3. **Email Notification**: System automatically sends appropriate email notification
4. **Error Handling**: If email fails, KYC status update still succeeds, but failure is logged

## Error Handling

- Email failures are logged but don't prevent KYC status updates
- Admin receives feedback about email delivery status
- Detailed error logs include user ID, status, and stack trace

## Integration with Email Templates

The system integrates with the existing EmailTemplate system:
- Template type: `agent_kyc_approval`
- Supports all standard placeholders and custom content
- Falls back to hardcoded default if no custom template exists

## Testing

To test the email notifications:

1. Create a test agent account
2. Submit KYC details
3. Use the admin panel to approve/reject the KYC
4. Check that appropriate email is sent
5. Verify email content and dashboard link functionality

## Routes Used

- `agent.dashboard` - Link destination in approval/rejection emails

## Logging

All email activities are logged with:
- Agent information
- KYC status change
- Email delivery success/failure
- Error details (if applicable)

Look for log entries with "AgentKycApprovalNotification" in the application logs. 
