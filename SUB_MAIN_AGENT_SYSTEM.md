# Sub-Main-Agent System

## Overview

The Sub-Main-Agent system allows Main Agents to register and manage users with limited, customizable permissions. These sub-main-agents have access to the same dashboard and interface as main agents, but their permissions are controlled and set by the main agent who registered them.

## Features

### 1. **Role-Based Access Control**
- **Main Agent**: Can register sub-main-agents and set their specific permissions
- **Sub-Main-Agent**: Has limited access based on permissions set by their main agent
- **<PERSON><PERSON> Permission**: Integrated for advanced permission management

### 2. **Permission Management**
Available permission categories:
- **Dashboard**: View/Edit dashboard access
- **Profile**: View/Edit personal profile information
- **Agents**: View/Edit agent management
- **Reports**: View/Edit various reports
- **Agreements**: View/Edit agreement management
- **Bank Account**: View/Edit bank account information

Each category supports two permission levels:
- **View**: Read-only access
- **Edit**: Full read-write access

### 3. **Registration System**
- Main agents can register sub-main-agents through a dedicated interface
- Registration generates a secure link for password creation
- Default permissions can be set during registration
- Permissions can be modified after registration

## Implementation Details

### Database Structure

#### New Tables
1. **Permission Tables** (via spatie/laravel-permission)
   - `permissions`: Stores available permissions
   - `roles`: Stores user roles
   - `model_has_permissions`: Links users to permissions
   - `model_has_roles`: Links users to roles

2. **ACS User Permissions** (`acs_user_permissions`)
   - `main_agent_id`: The main agent who set the permissions
   - `sub_main_agent_id`: The sub-main-agent receiving permissions
   - `permissions`: JSON column storing permission settings
   - `is_active`: Boolean flag to enable/disable permissions

#### Updated Tables
- **ACS Roles** (`acs_roles`): Added 'sub-main-agent' role

### Key Files Created/Modified

#### Models
- `app/Models/AcsUserPermission.php`: Manages custom permission relationships
- `app/Models/User.php`: Added spatie permission traits and custom permission methods

#### Livewire Components
- `app/Livewire/MasterAgent/RegisterSubMainAgent.php`: Main interface for sub-main-agent management
- `resources/views/livewire/master-agent/register-sub-main-agent.blade.php`: Registration and management UI

#### Middleware
- `app/Http/Middleware/CheckSubMainAgentPermission.php`: Checks resource-level permissions for sub-main-agents

#### Routes
- `routes/main-agent.php`: Added route for sub-main-agent management
- Updated middleware groups to include sub-main-agent role

#### Seeders
- `database/seeders/AcsRoleSeeder.php`: Added sub-main-agent role
- `database/seeders/PermissionSeeder.php`: Sets up permission structure

## Usage Guide

### For Main Agents

#### 1. Register a Sub-Main-Agent
1. Navigate to `main-agent/sub-main-agent`
2. Click "Register New Sub-Main-Agent"
3. Fill in name and email
4. Set default permissions for each resource category
5. Submit registration
6. Share the generated registration link with the sub-main-agent

#### 2. Manage Existing Sub-Main-Agents
1. View list of registered sub-main-agents
2. Use the "Actions" dropdown to:
   - View detailed information
   - Manage permissions
   - Activate/Deactivate the user

#### 3. Update Permissions
1. Click "Manage Permissions" for any sub-main-agent
2. Set View or Edit permissions for each resource category
3. Save changes

### For Sub-Main-Agents

#### 1. Complete Registration
1. Use the registration link provided by the main agent
2. Set a secure password
3. Complete profile information

#### 2. Dashboard Access
1. Sub-main-agents use the same dashboard as main agents
2. Access is automatically restricted based on permissions
3. Unavailable features will show permission error messages

## Permission System Details

### Permission Checking Logic
```php
// Check if user has permission for a resource and action
$user->hasCustomPermission('reports', 'edit');

// For sub-main-agents: checks AcsUserPermission
// For others: always returns true (full access based on role)
```

### Middleware Usage
```php
// Protect routes with permission requirements
Route::middleware('sub.main.agent.permission:reports,edit')->group(function () {
    // Routes that require edit access to reports
});
```

### Available Permission Resources
- `dashboard`
- `profile`
- `agents`
- `reports`
- `agreements`
- `bank_account`

## API Reference

### User Model Methods
```php
// Check custom permissions (for sub-main-agents)
$user->hasCustomPermission(string $resource, string $action = 'view'): bool

// Get permissions set by this main agent
$user->setPermissionsFor(): HasMany

// Get permissions received by this sub-main-agent
$user->receivedPermissions(): HasOne
```

### AcsUserPermission Model Methods
```php
// Check specific permission
$permission->hasPermission(string $resource, string $action = 'view'): bool

// Get available permission options
AcsUserPermission::getAvailablePermissions(): array
```

## Security Features

### Access Control
- Sub-main-agents cannot access resources without explicit permission
- Permission changes take effect immediately
- Deactivated users lose all access instantly

### Data Isolation
- Sub-main-agents only see data within their cooperative/branch
- Permission settings are tied to the main agent who created them
- Role-based fallback for other user types

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Check if user has the required permission level
   - Verify permission is active (`is_active = true`)
   - Ensure main agent has set the permission

2. **Registration Link Issues**
   - Links expire after a certain period
   - Check if email is already registered
   - Verify token in `password_reset_tokens` table

3. **Dashboard Access Issues**
   - Confirm user has 'sub-main-agent' role
   - Check if user has basic dashboard view permission
   - Verify middleware is properly configured

### Database Queries for Debugging

```sql
-- Check user roles
SELECT u.name, u.email, r.name as role 
FROM acs_users u 
JOIN acs_roles r ON u.acs_role_id = r.id;

-- Check sub-main-agent permissions
SELECT u.name, up.permissions, up.is_active
FROM acs_users u
JOIN acs_user_permissions up ON u.id = up.sub_main_agent_id
WHERE u.acs_role_id = (SELECT id FROM acs_roles WHERE name = 'sub-main-agent');
```

## Future Enhancements

### Potential Improvements
1. **Time-based Permissions**: Set expiration dates for permissions
2. **Permission Templates**: Create reusable permission sets
3. **Audit Logging**: Track permission changes and access attempts
4. **Bulk Operations**: Manage multiple sub-main-agents at once
5. **Permission Inheritance**: Hierarchical permission structures

### Integration Points
- **Notification System**: Alert main agents of permission requests
- **Reporting**: Usage analytics for sub-main-agents
- **API Access**: RESTful endpoints for external systems 
