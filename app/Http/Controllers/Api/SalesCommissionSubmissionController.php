<?php

namespace App\Http\Controllers\Api;

use Exception;
use App\Models\User;
use App\Models\AcsRole;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\AcsCommissionDistribution;
use App\Models\AcsCommissionSetting;
use App\Models\AcsSalesCommission;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class SalesCommissionSubmissionController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        Log::info('commission_requests', $request->all());
        //payload
        /**
         * {
         * "total_amount_paid": 1000,
         * "status": "paid",
         * "user": [
         *      "user_id": 1000,
         *      "agent_id": 2000
         * ],
         * "items":[
         *      "design_id": 588,
         *      "variety_id": 6,
         *      "quantity": 1,
         *      "price": 100,
         * ]}
         *
         * "588_6" => qty >= x && qty <= x =  tiers | $percentage / 100 $total_amount = actual_comissions
         */
        $callbackReq = Http::get('http://api.hos0sgpzxv-wg96gkodm4oy.p.temp-site.link/2d890f2a292f4160aafa800f1b7f9/fake/callback');
        $responseData = json_decode($callbackReq, true);
        $customerId = $responseData['data']['order']['customer_id'] ?? null;
        $agentReferralCode = $responseData['data']['order']['agent_referral_code'] ?? null;
        $productData = $responseData['data']['product'] ?? [];
        $requestSignature = hash('sha256', implode('|', $request->all()));
        $totalCommissions = 0;
        $itemDetails = [];

        $getAgentByReferralCode = User::where('referral_code', $agentReferralCode)->first();

        if ($getAgentByReferralCode->role?->name != AcsRole::AGENT_ROLE) {
            return response()->json([
                'error' => false,
                'status_code' => 400,
                'data' => 'Current user is not agent, or agent is not found.'
            ]);
        }

        if (is_null($customerId) || is_null($agentReferralCode)) {
            return response()->json([
                'error' => true,
                'status' => 400,
                'data' => 'Missing customer ID or agent referral code'
            ]);
        }

        //load all active comissions
        $comissionsSettings = AcsCommissionSetting::with('tiers')->active()
        ->get()
        ->mapWithKeys(function ($value, $key) {
            return ["{$value->product_category_code}_{$value->variety_type}" => $value];
        })
        ->all();

        foreach ($productData as $purchasedProduct) {
            $totalComissionEarn = 0;
            $percentageComission = 0;
            $configKey = $purchasedProduct['category_id'].'_'.$purchasedProduct['variety_id'];

            if (array_key_exists($configKey, $comissionsSettings)) {
                $getComissionSettings = collect($comissionsSettings[$configKey]['tiers']) ?? null;
                $tierComission = $getComissionSettings->where('min_qty', '<=', $purchasedProduct['quantity'])
                    ->where('max_qty', '>=', $purchasedProduct['quantity'])
                    ->first() ?? null;
                $itemAmount = $purchasedProduct['price'] / 100;
                $percentageComission = ($tierComission['commission_percentage'] / 100);
                $totalComissionEarn = $itemAmount * $percentageComission;
                //hardcode 60/40
                $totalCommissions += $totalComissionEarn;
                $purchasedProduct['comission_earned'] = $percentageComission;
                $purchasedProduct['customer_id'] = $customerId;
                $purchasedProduct['agent_referral_code'] = $agentReferralCode;
                $purchasedProduct['main_agent_percentage'] = 40;
                $purchasedProduct['agent_percentage'] = 60;
                $purchasedProduct['total_comissions'] = number_format($totalComissionEarn, 2, '.', '');
                $purchasedProduct['agent_comission'] = number_format(60 / 100 * $totalComissionEarn, 2, '.', '');
                $purchasedProduct['master_agent_comission'] = number_format(40 / 100 * $totalComissionEarn, 2, '.', '');
                $itemDetails[] = $purchasedProduct;
            }
        }


        foreach ($itemDetails as $itemDetail) {
            $salesComission = AcsSalesCommission::create([
                'table_22_jualan_id' => 1,
                'table_23_senarai_jualan_id' => 1,
                'invoice_no' => '',
                'affiliate_membership_no' => '',
                'senarai_pelanggan_id' => 1,
                'comission_percentage' => 0,
                'comission_amount' => 0,
            ]);
            //@TODO db transaction
            AcsCommissionDistribution::create([
                'acs_sales_commission_id' => $salesComission->id,
                'acs_coorperative_id' => $getAgentByReferralCode->acs_coorperative_id,
                'acs_coorperative_branch_id' => $getAgentByReferralCode->acs_coorperative_branch_id,
                'acs_main_agent_user_id' => $getAgentByReferralCode->invited_by,
                'main_agent_percentage' => $itemDetail['main_agent_percentage'],
                'main_agent_commission_amount' => $itemDetail['master_agent_comission'],
                'agent_percentage' => $itemDetail['agent_percentage'],
                'agent_commission_amount' => $itemDetail['agent_comission'],
                'acs_agent_user_id' => $getAgentByReferralCode->id,
            ]);
        }

        return response()->json([
            'status' => 200,
            'error' => false,
            'data' => $itemDetails
        ]);

        //method to get comission tiers
        //in memory calculation
        // return Collection::make($comissionsSettings['588_6']['tiers'])
        // ->filter(fn ($item, $key) => $item->min_qty >= 11 && $item->max_qty <= 20)
        // ->all();

    }
}
