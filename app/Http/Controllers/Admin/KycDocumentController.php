<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class KycDocumentController extends Controller
{
        public function downloadBankStatement($agentId)
    {
        Log::info('Download Bank Statement requested for agent: ' . $agentId);

        $user = User::find($agentId);

        if (!$user) {
            Log::warning('Agent not found: ' . $agentId);
            abort(404, 'Agent not found.');
        }

        Log::info('Agent found: ' . $user->name);

        if (!$user->userDetails) {
            Log::warning('User details not found for agent: ' . $agentId);
            abort(404, 'User details not found.');
        }

        if (!$user->userDetails || !$user->userDetails->bankDetail || !$user->userDetails->bankDetail->bank_statement) {
            Log::warning('Bank statement not found for agent: ' . $agentId);
            abort(404, 'Bank statement not found.');
        }

        $filePath = storage_path('app/public/' . $user->userDetails->bankDetail->bank_statement);

        Log::info('File path: ' . $filePath);

        if (!file_exists($filePath)) {
            Log::warning('Bank statement file not found on server: ' . $filePath);
            abort(404, 'Bank statement file not found on server.');
        }

        Log::info('File exists, downloading: ' . $filePath);

        return response()->download($filePath, 'bank_statement_' . $user->name . '.' . pathinfo($filePath, PATHINFO_EXTENSION));
    }

        public function downloadIcFront($agentId)
    {
        Log::info('Download IC Front requested for agent: ' . $agentId);

        $user = User::find($agentId);

        if (!$user) {
            Log::warning('Agent not found: ' . $agentId);
            abort(404, 'Agent not found.');
        }

        Log::info('Agent found: ' . $user->name);

        if (!$user->userDetails) {
            Log::warning('User details not found for agent: ' . $agentId);
            abort(404, 'User details not found.');
        }

        if (!$user->userDetails || !$user->userDetails->ic_front) {
            Log::warning('IC front document not found for agent: ' . $agentId);
            abort(404, 'IC front document not found.');
        }

        $filePath = storage_path('app/public/' . $user->userDetails->ic_front);

        Log::info('File path: ' . $filePath);

        if (!file_exists($filePath)) {
            Log::warning('IC front file not found on server: ' . $filePath);
            abort(404, 'IC front file not found on server.');
        }

        Log::info('File exists, downloading: ' . $filePath);

        return response()->download($filePath, 'ic_front_' . $user->name . '.' . pathinfo($filePath, PATHINFO_EXTENSION));
    }

            public function downloadIcBack($agentId)
    {
        Log::info('Download IC Back requested for agent: ' . $agentId);

        $user = User::find($agentId);

        if (!$user) {
            Log::warning('Agent not found: ' . $agentId);
            abort(404, 'Agent not found.');
        }

        Log::info('Agent found: ' . $user->name);

        if (!$user->userDetails) {
            Log::warning('User details not found for agent: ' . $agentId);
            abort(404, 'User details not found.');
        }

        if (!$user->userDetails || !$user->userDetails->ic_back) {
            Log::warning('IC back document not found for agent: ' . $agentId);
            abort(404, 'IC back document not found.');
        }

        $filePath = storage_path('app/public/' . $user->userDetails->ic_back);

        Log::info('File path: ' . $filePath);

        if (!file_exists($filePath)) {
            Log::warning('IC back file not found on server: ' . $filePath);
            abort(404, 'IC back file not found on server.');
        }

        Log::info('File exists, downloading: ' . $filePath);

        return response()->download($filePath, 'ic_back_' . $user->name . '.' . pathinfo($filePath, PATHINFO_EXTENSION));
    }
}
