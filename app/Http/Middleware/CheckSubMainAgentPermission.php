<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Models\User;

class CheckSubMainAgentPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $resource, string $action = 'view'): Response
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // If user is not a sub-main-agent, allow access based on their role
        if (!$user->hasRole('sub-main-agent')) {
            return $next($request);
        }

        // Check if sub-main-agent has permission for this resource and action
        if (!$user->hasCustomPermission($resource, $action)) {
            abort(403, 'You do not have permission to access this resource.');
        }

        // Check if the menu is hidden for this user
        $currentPath = $request->path();
        if (!$user->canAccessRoute($currentPath, $action)) {
            abort(403, 'This section is not available for your account.');
        }

        return $next($request);
    }
}
