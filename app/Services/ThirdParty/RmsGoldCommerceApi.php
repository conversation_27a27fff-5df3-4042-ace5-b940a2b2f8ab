<?php

namespace App\Services\ThirdParty;

use GuzzleHttp\Client;
use App\Models\RmsGoldCommerceSetting;

class RmsGoldCommerceApi
{
    protected $client;
    protected $apiKey;
    protected $apiSecret;
    protected $apiUrl;

    protected $apiResponseStatus = 0;
    protected $apiResponseData = [];
    protected $apiResponseMessage = '';

    final public const HTTP_METHOD_GET = 'GET';
    final public const HTTP_METHOD_POST = 'POST';
    final public const HTTP_METHOD_PUT = 'PUT';
    final public const HTTP_METHOD_DELETE = 'DELETE';

    //endpoints
    final public const ENDPOINT_GET_CATEGORIES = 'v1/category/all';
    final public const ENDPOINT_GET_DESIGN = 'v1/goldbar/category-variety';
    final public const ENDPOINT_POST_DOWNLINE = 'v1/downline-list';
    final public const ENDPOINT_GET_GOLD_PRICE = 'v1/gold-price/normal';

    public function __construct()
    {
        $settings = RmsGoldCommerceSetting::getActiveSettings();
        $this->apiKey = $settings->api_key;
        $this->apiSecret = $settings->api_secret;
        $this->apiUrl = $settings->api_url;
        //run validation against api key and secret
        $this->validateApiKeyAndSecret();

        $this->client = new Client([
            'base_uri' => $this->apiUrl,
            'headers' => [
                'Key' => $this->apiKey,
                'Secret' => $this->apiSecret,
            ],
        ]);

    }

    public static function make()
    {
        return new self();
    }

    //get data by endpoints
    public function request($endpoint, $method = 'GET', $data = [])
    {
        try {
            $response = $this->client->request($method, $endpoint, [
                'json' => $data,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
            $parsedResponse = json_decode($response->getBody(), true);
            $this->apiResponseStatus = $parsedResponse['status_code'];
            $this->apiResponseData = $parsedResponse['data'] ?? [];
            $this->apiResponseMessage = $parsedResponse['message'] ?? '';

            return $this->apiResponseData;
        } catch (\Exception $e) {
            throw new \Exception('Error: ' . $e->getMessage());
        }
    }

    public function requestPost($endpoint, $method = 'POST', $data = [])
    {
        try {
            $response = $this->client->request($method, $endpoint, [
                'json' => $data,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
            $parsedResponse = json_decode($response->getBody(), true);

            // Handle different response structures
            if (is_array($parsedResponse) && isset($parsedResponse[0])) {
                $this->apiResponseStatus = 200;
                $this->apiResponseData = $parsedResponse;
                $this->apiResponseMessage = 'Success';
            } else {
                $this->apiResponseStatus = $parsedResponse['status_code'] ?? 200;
                $this->apiResponseData = $parsedResponse['data'] ?? $parsedResponse;
                $this->apiResponseMessage = $parsedResponse['message'] ?? 'Success';
            }

            return $this->apiResponseData;
        } catch (\Exception $e) {
            throw new \Exception('Error: ' . $e->getMessage());
        }
    }

    public function getApiResponseStatus()
    {
        return $this->apiResponseStatus;
    }

    public function getApiResponseData()
    {
        return $this->apiResponseData;
    }

    public function getApiResponseMessage()
    {
        return $this->apiResponseMessage;
    }


    public function getClient()
    {
        return $this->client;
    }

    private function validateApiKeyAndSecret()
    {
        if (!$this->apiKey || !$this->apiSecret) {
            throw new \Exception('API key and secret are required');
        }
    }
}
