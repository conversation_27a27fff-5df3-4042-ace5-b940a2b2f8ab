<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\User;
use App\Models\EmailTemplate;
use Illuminate\Support\HtmlString;

class PasswordChangeNotification extends Notification
{
    use Queueable;

    private $user;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        // Try to get custom template, fallback to default
        $template = EmailTemplate::where('type', 'password_change')->first();

        if ($template && $template->is_active) {
            // Replace placeholders in template
            $content = str_replace([
                '{name}',
                '{email}',
                '{date_time}',
                '{app_name}'
            ], [
                $this->user->name,
                $this->user->email,
                now()->format('Y-m-d H:i:s'),
                config('app.name')
            ], $template->content);

            return (new MailMessage)
                ->subject($template->subject)
                ->greeting($template->greeting ?: 'Hello ' . $this->user->name . '!')
                ->line(new HtmlString($content))
                ->line('If you did not change your password, please contact us immediately.')
                ->salutation($template->signature ?: 'Best regards, ' . config('app.name') . ' Team');
        }

        // Default template if no custom template found
        return (new MailMessage)
            ->subject('Password Changed - ' . config('app.name'))
            ->greeting('Hello ' . $this->user->name . '!')
            ->line('Your password has been successfully changed.')
            ->line('This change was made on: ' . now()->format('Y-m-d H:i:s'))
            ->line('If you did not make this change, please contact us immediately to secure your account.')
            ->line('For your security, we recommend:')
            ->line('• Using a strong, unique password')
            ->line('• Not sharing your password with anyone')
            ->line('• Logging out of shared devices')
            ->action('Login to Your Account', url('/login'))
            ->line('Thank you for using ' . config('app.name') . '!')
            ->salutation('Best regards, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'user_email' => $this->user->email,
            'changed_at' => now()->toISOString(),
        ];
    }
}
