<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class KycStatusUpdateNotification extends Notification
{
    protected $user;
    protected $status;
    protected $reason;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, string $status, ?string $reason = null)
    {
        $this->user = $user;
        $this->status = $status;
        $this->reason = $reason;
        Log::info("KycStatusUpdateNotification created for user {$user->name}, status: {$status}");
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        Log::info("KycStatusUpdateNotification via() called for {$notifiable->name}");
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        Log::info("KycStatusUpdateNotification toMail() called for {$notifiable->name}, status: {$this->status}");

        $mailMessage = (new MailMessage)
            ->subject('KYC Status Update - ' . ucfirst($this->status))
            ->greeting('Hello ' . $notifiable->name . ',');

        if ($this->status === 'approved') {
            Log::info("Building approved email for {$notifiable->name}");
            $mailMessage->line('Congratulations! Your KYC details have been approved.')
                ->line('You now have full access to the system and can start using all features.');
        } else {
            Log::info("Building rejected email for {$notifiable->name} with reason: " . ($this->reason ?? 'none provided'));
            $mailMessage->line('Your KYC details have been reviewed but could not be approved at this time.')
                ->line('Please review and update your information, then submit again for approval.');

            if ($this->reason) {
                $mailMessage->line('Reason for rejection:')
                    ->line($this->reason);
            }
        }

        try {
            // Use a route that definitely exists
            $url = route('master_agent.complete-details');
            Log::info("Action URL for {$notifiable->name}: {$url}");

            return $mailMessage
                ->action('View Profile', $url)
                ->line('Thank you for your cooperation.');
        } catch (\Exception $e) {
            Log::error("Failed to generate URL: " . $e->getMessage());

            // Fall back to not including an action button
            return $mailMessage
                ->line('Please log in to your account to view your profile.')
                ->line('Thank you for your cooperation.');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'status' => $this->status,
            'reason' => $this->reason,
        ];
    }
}
