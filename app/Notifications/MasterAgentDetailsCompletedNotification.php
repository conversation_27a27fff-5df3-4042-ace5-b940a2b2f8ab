<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MasterAgentDetailsCompletedNotification extends Notification
{
    protected $masterAgent;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $masterAgent)
    {
        $this->masterAgent = $masterAgent;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        try {
            // Use the correct admin route for main agent management
            $url = route('admin.index');

            return (new MailMessage)
                ->subject('Master Agent Profile Completion - Requires Review')
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line('A Master Agent has completed their profile details and is waiting for your approval.')
                ->line('Master Agent Details:')
                ->line('Name: ' . $this->masterAgent->name)
                ->line('Email: ' . $this->masterAgent->email)
                ->line('Cooperative: ' . ($this->masterAgent->cooperative ? $this->masterAgent->cooperative->name : 'Not Assigned'))
                ->action('Review Details', $url)
                ->line('Thank you for your prompt attention to this matter.');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Failed to generate admin URL: " . $e->getMessage());

            // Fall back to not including an action button
            return (new MailMessage)
                ->subject('Master Agent Profile Completion - Requires Review')
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line('A Master Agent has completed their profile details and is waiting for your approval.')
                ->line('Master Agent Details:')
                ->line('Name: ' . $this->masterAgent->name)
                ->line('Email: ' . $this->masterAgent->email)
                ->line('Cooperative: ' . ($this->masterAgent->cooperative ? $this->masterAgent->cooperative->name : 'Not Assigned'))
                ->line('Please log in to the admin panel to review their details.')
                ->line('Thank you for your prompt attention to this matter.');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'master_agent_id' => $this->masterAgent->id,
            'master_agent_name' => $this->masterAgent->name,
            'master_agent_email' => $this->masterAgent->email,
        ];
    }
}
