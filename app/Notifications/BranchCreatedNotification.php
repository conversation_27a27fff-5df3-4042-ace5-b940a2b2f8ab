<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\User;
use App\Models\EmailTemplate;
use Illuminate\Support\HtmlString;

class BranchCreatedNotification extends Notification
{
    use Queueable;

    protected $masterAgent;
    protected $branchData;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $masterAgent, array $branchData)
    {
        $this->masterAgent = $masterAgent;
        $this->branchData = $branchData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $cooperativeName = $this->masterAgent->cooperative ? $this->masterAgent->cooperative->name : 'ACS RMS';

        // Get custom email template
        $emailTemplate = EmailTemplate::getByType('branch_created');

        if ($emailTemplate) {
            // Use custom template
            $placeholderData = [
                'admin_name' => $notifiable->name,
                'cooperative_name' => $cooperativeName,
                'master_agent_name' => $this->masterAgent->name,
                'master_agent_email' => $this->masterAgent->email,
                'branch_name' => $this->branchData['name'],
                'organization_name' => $this->branchData['organization_name'] ?? 'N/A',
                'business_registration_no' => $this->branchData['business_registration_no'],
                'created_at' => now()->format('d M Y H:i:s'),
            ];

            $customContent = $emailTemplate->replacePlaceholders($placeholderData);

            return (new MailMessage)
                ->subject($customContent['subject'])
                ->greeting('') // Remove default greeting since it's in the custom body
                ->line(new HtmlString(nl2br(e($customContent['body']))));
        }

        // Fallback to default template
        return (new MailMessage)
            ->subject('New Branch Created - Requires Approval - ' . $cooperativeName)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('A Master Agent has created a new branch and it requires your approval.')
            ->line('Branch Details:')
            ->line('• Branch Name: ' . $this->branchData['name'])
            ->line('• Organization: ' . ($this->branchData['organization_name'] ?? 'N/A'))
            ->line('• Business Registration No: ' . $this->branchData['business_registration_no'])
            ->line('• Created by: ' . $this->masterAgent->name . ' (' . $this->masterAgent->email . ')')
            ->line('• Cooperative: ' . $cooperativeName)
            ->line('• Created: ' . now()->format('d M Y H:i:s'))
            ->line('Please log in to the admin panel to review and approve this branch.')
            ->salutation('Best regards, ' . $cooperativeName . ' Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'master_agent_id' => $this->masterAgent->id,
            'master_agent_name' => $this->masterAgent->name,
            'master_agent_email' => $this->masterAgent->email,
            'branch_id' => $this->branchData['id'] ?? null,
            'branch_name' => $this->branchData['name'],
            'organization_name' => $this->branchData['organization_name'] ?? null,
            'business_registration_no' => $this->branchData['business_registration_no'],
            'cooperative_name' => $this->masterAgent->cooperative ? $this->masterAgent->cooperative->name : null,
            'created_at' => now()->toISOString(),
        ];
    }
}
