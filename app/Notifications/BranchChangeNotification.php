<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\AcsCooperativeBranch;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use App\Models\EmailTemplate;
use Illuminate\Support\HtmlString;

class BranchChangeNotification extends Notification
{
    protected $agent;
    protected $fromBranch;
    protected $toBranch;
    protected $reason;
    protected $actionBy;
    protected $recipientType; // 'admin', 'main_agent', 'agent'

    /**
     * Create a new notification instance.
     */
    public function __construct(User $agent, $fromBranch, AcsCooperativeBranch $toBranch, string $reason, User $actionBy, string $recipientType)
    {
        $this->agent = $agent;
        $this->fromBranch = $fromBranch;
        $this->toBranch = $toBranch;
        $this->reason = $reason;
        $this->actionBy = $actionBy;
        $this->recipientType = $recipientType;

        Log::info("BranchChangeNotification created for agent {$agent->name}, recipient type: {$recipientType}");
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        Log::info("BranchChangeNotification via() called for {$notifiable->name}");
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        Log::info("Preparing BranchChangeNotification email for {$notifiable->name}");

        // Try to get email template
        $emailTemplate = EmailTemplate::where('type', 'branch_change_notification')->first();

        if ($emailTemplate && !empty($emailTemplate->template)) {
            return $this->buildTemplateEmail($notifiable, $emailTemplate);
        }

        return $this->buildDefaultEmail($notifiable);
    }

    private function buildTemplateEmail(object $notifiable, EmailTemplate $emailTemplate): MailMessage
    {
        try {
            $subject = $emailTemplate->subject ?: 'Branch Change Notification';
            $template = $emailTemplate->template;

            // Replace placeholders based on recipient type
            $replacements = $this->getReplacements($notifiable);

            foreach ($replacements as $placeholder => $value) {
                $template = str_replace($placeholder, $value, $template);
                $subject = str_replace($placeholder, $value, $subject);
            }

            return (new MailMessage)
                ->subject($subject)
                ->line(new HtmlString($template));

        } catch (\Exception $e) {
            Log::error('Error building template email for BranchChangeNotification: ' . $e->getMessage());
            return $this->buildDefaultEmail($notifiable);
        }
    }

    private function buildDefaultEmail(object $notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->subject('Branch Change Notification - ' . $this->agent->name);

        if ($this->recipientType === 'admin') {
            $message->greeting('Dear Admin,')
                ->line("An agent's branch has been changed in the system.")
                ->line('**Agent Details:**')
                ->line('Name: ' . $this->agent->name)
                ->line('Email: ' . $this->agent->email)
                ->line('**Branch Change Details:**')
                ->line('From Branch: ' . ($this->fromBranch ? $this->fromBranch->name : 'No Branch'))
                ->line('To Branch: ' . $this->toBranch->name)
                ->line('From Cooperative: ' . ($this->fromBranch && $this->fromBranch->cooperative ? $this->fromBranch->cooperative->name : 'No Cooperative'))
                ->line('To Cooperative: ' . ($this->toBranch->cooperative ? $this->toBranch->cooperative->name : 'Unknown'))
                ->line('Reason: ' . $this->reason)
                ->line('Action performed by: ' . $this->actionBy->name . ' (' . $this->actionBy->email . ')')
                ->line('Date: ' . now()->format('Y-m-d H:i:s'))
                ->line('Please review this change if necessary.');

        } elseif ($this->recipientType === 'main_agent') {
            $message->greeting('Dear ' . $notifiable->name . ',')
                ->line("An agent under your management has had their branch changed.")
                ->line('**Agent Details:**')
                ->line('Name: ' . $this->agent->name)
                ->line('Email: ' . $this->agent->email)
                ->line('**Branch Change Details:**')
                ->line('From Branch: ' . ($this->fromBranch ? $this->fromBranch->name : 'No Branch'))
                ->line('To Branch: ' . $this->toBranch->name)
                ->line('From Cooperative: ' . ($this->fromBranch && $this->fromBranch->cooperative ? $this->fromBranch->cooperative->name : 'No Cooperative'))
                ->line('To Cooperative: ' . ($this->toBranch->cooperative ? $this->toBranch->cooperative->name : 'Unknown'))
                ->line('Reason: ' . $this->reason)
                ->line('Date: ' . now()->format('Y-m-d H:i:s'))
                ->line('If you have any questions about this change, please contact the administration.');

        } else { // agent
            $message->greeting('Dear ' . $notifiable->name . ',')
                ->line("Your branch assignment has been changed.")
                ->line('**Branch Change Details:**')
                ->line('From Branch: ' . ($this->fromBranch ? $this->fromBranch->name : 'No Branch'))
                ->line('To Branch: ' . $this->toBranch->name)
                ->line('From Cooperative: ' . ($this->fromBranch && $this->fromBranch->cooperative ? $this->fromBranch->cooperative->name : 'No Cooperative'))
                ->line('To Cooperative: ' . ($this->toBranch->cooperative ? $this->toBranch->cooperative->name : 'Unknown'))
                ->line('Reason: ' . $this->reason)
                ->line('Date: ' . now()->format('Y-m-d H:i:s'))
                ->line('If you have any questions about this change, please contact your main agent or administration.');
        }

        return $message->line('Thank you for your attention to this matter.');
    }

    private function getReplacements(object $notifiable): array
    {
        return [
            '{{recipient_name}}' => $notifiable->name,
            '{{agent_name}}' => $this->agent->name,
            '{{agent_email}}' => $this->agent->email,
            '{{from_branch}}' => $this->fromBranch ? $this->fromBranch->name : 'No Branch',
            '{{to_branch}}' => $this->toBranch->name,
            '{{from_cooperative}}' => $this->fromBranch && $this->fromBranch->cooperative ? $this->fromBranch->cooperative->name : 'No Cooperative',
            '{{to_cooperative}}' => $this->toBranch->cooperative ? $this->toBranch->cooperative->name : 'Unknown',
            '{{reason}}' => $this->reason,
            '{{action_by}}' => $this->actionBy->name,
            '{{action_by_email}}' => $this->actionBy->email,
            '{{date}}' => now()->format('Y-m-d H:i:s'),
            '{{recipient_type}}' => $this->recipientType,
        ];
    }
}
