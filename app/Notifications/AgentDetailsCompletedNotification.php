<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AgentDetailsCompletedNotification extends Notification
{
    protected $agent;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $agent)
    {
        $this->agent = $agent;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        try {
            // Use the correct admin route for agent management
            $url = route('admin.index');

            return (new MailMessage)
                ->subject('Agent Profile Completion - Requires Review')
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line('An Agent has completed their profile details and is waiting for your approval.')
                ->line('Agent Details:')
                ->line('Name: ' . $this->agent->name)
                ->line('Email: ' . $this->agent->email)
                ->line('Cooperative: ' . ($this->agent->cooperative ? $this->agent->cooperative->name : 'Not Assigned'))
                ->line('Branch: ' . ($this->agent->cooperativeBranch ? $this->agent->cooperativeBranch->name : 'Not Assigned'))
                ->action('Review Details', $url)
                ->line('Thank you for your prompt attention to this matter.');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Failed to generate admin URL: " . $e->getMessage());

            // Fall back to not including an action button
            return (new MailMessage)
                ->subject('Agent Profile Completion - Requires Review')
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line('An Agent has completed their profile details and is waiting for your approval.')
                ->line('Agent Details:')
                ->line('Name: ' . $this->agent->name)
                ->line('Email: ' . $this->agent->email)
                ->line('Cooperative: ' . ($this->agent->cooperative ? $this->agent->cooperative->name : 'Not Assigned'))
                ->line('Branch: ' . ($this->agent->cooperativeBranch ? $this->agent->cooperativeBranch->name : 'Not Assigned'))
                ->line('Please log in to the admin panel to review their details.')
                ->line('Thank you for your prompt attention to this matter.');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'agent_id' => $this->agent->id,
            'agent_name' => $this->agent->name,
            'agent_email' => $this->agent->email,
        ];
    }
}
