<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\User;
use App\Models\EmailTemplate;
use Illuminate\Support\HtmlString;

class AgentPasswordCreatedNotification extends Notification
{
    use Queueable;

    protected $agent;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $agent)
    {
        $this->agent = $agent;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $cooperativeName = $this->agent->cooperative ? $this->agent->cooperative->name : 'ACS RMS';
        $branchName = $this->agent->cooperativeBranch ? $this->agent->cooperativeBranch->name : 'Not assigned';

        // Check recipient's role to customize the email
        $isMainAgent = $notifiable->hasRole('main-agent');
        $isAgent = $notifiable->hasRole('agent');

        // Get custom email template
        $emailTemplate = EmailTemplate::getByType('agent_password_created');

        if ($emailTemplate) {
            // Use custom template with role-based customization
            $placeholderData = [
                'cooperative_name' => $cooperativeName,
                'branch_name' => $branchName,
                'agent_name' => $this->agent->name,
                'agent_email' => $this->agent->email,
                'agent_phone' => $this->agent->phone,
                'activated_at' => $this->agent->activated_at ? $this->agent->activated_at->format('d M Y H:i:s') : now()->format('d M Y H:i:s'),
                'admin_name' => $notifiable->name
            ];

            $customContent = $emailTemplate->replacePlaceholders($placeholderData);

            return (new MailMessage)
                ->subject($customContent['subject'])
                ->greeting('') // Remove default greeting since it's in the custom body
                ->line(new HtmlString(nl2br(e($customContent['body']))));
        }

        // Fallback to default template with role-based customization
        $subject = $isMainAgent
            ? 'Main Agent Account Activated - ' . $cooperativeName
            : 'Agent Account Activated - ' . $cooperativeName;

        $mailMessage = (new MailMessage)
            ->subject($subject)
            ->greeting('Hello ' . $notifiable->name . ',');

        if ($isMainAgent) {
            // For main agents - hide branch information
            $mailMessage->line('An Main agent has successfully created their password and activated their account.')
                ->line('Main Agent Details:')
                ->line('• Name: ' . $this->agent->name)
                ->line('• Email: ' . $this->agent->email)
                ->line('• Phone: ' . $this->agent->phone)
                ->line('• Cooperative: ' . $cooperativeName)
                ->line('• Activated: ' . ($this->agent->activated_at ? $this->agent->activated_at->format('d M Y H:i:s') : now()->format('d M Y H:i:s')))
                ->line('The main agent can now access the system and perform their assigned tasks.');
        } else {
            // For agents and others - show branch information
            $mailMessage->line('An agent has successfully created their password and activated their account.')
                ->line('Agent Details:')
                ->line('• Name: ' . $this->agent->name)
                ->line('• Email: ' . $this->agent->email)
                ->line('• Phone: ' . $this->agent->phone)
                ->line('• Cooperative: ' . $cooperativeName)
                ->line('• Branch: ' . $branchName)
                ->line('• Activated: ' . ($this->agent->activated_at ? $this->agent->activated_at->format('d M Y H:i:s') : now()->format('d M Y H:i:s')))
                ->line('The agent can now access the system and perform their assigned tasks.');
        }

        return $mailMessage->salutation('Best regards, ' . $cooperativeName . ' Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'agent_id' => $this->agent->id,
            'agent_name' => $this->agent->name,
            'agent_email' => $this->agent->email,
            'cooperative_name' => $this->agent->cooperative ? $this->agent->cooperative->name : null,
            'branch_name' => $this->agent->cooperativeBranch ? $this->agent->cooperativeBranch->name : null,
            'activated_at' => $this->agent->activated_at,
        ];
    }
}
