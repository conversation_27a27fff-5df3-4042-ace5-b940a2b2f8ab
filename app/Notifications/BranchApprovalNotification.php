<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\AcsCooperativeBranch;
use App\Models\User;
use App\Models\EmailTemplate;
use Illuminate\Support\HtmlString;

class BranchApprovalNotification extends Notification
{
    use Queueable;

    protected $branch;
    protected $action;
    protected $reason;
    protected $actionBy;

    /**
     * Create a new notification instance.
     */
    public function __construct(AcsCooperativeBranch $branch, string $action, ?string $reason = null, ?User $actionBy = null)
    {
        $this->branch = $branch;
        $this->action = $action;
        $this->reason = $reason;
        $this->actionBy = $actionBy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $cooperativeName = $this->branch->cooperative ? $this->branch->cooperative->name : 'ACS RMS';
        $actionBy = $this->actionBy ? $this->actionBy->name : 'Administrator';

        // Get custom email template
        $emailTemplate = EmailTemplate::getByType('branch_approval');

        if ($emailTemplate) {
            // Use custom template
            $placeholderData = [
                'creator_name' => $notifiable->name,
                'branch_name' => $this->branch->name,
                'cooperative_name' => $cooperativeName,
                'action' => $this->action,
                'action_display' => ucfirst($this->action),
                'reason' => $this->reason ?: 'No reason provided',
                'action_by' => $actionBy,
                'action_date' => now()->format('d M Y H:i:s'),
                'business_registration_no' => $this->branch->business_registration_no ?: 'N/A',
                'organization_name' => $this->branch->organization_name ?: 'N/A',
            ];

            $customContent = $emailTemplate->replacePlaceholders($placeholderData);

            return (new MailMessage)
                ->subject($customContent['subject'])
                ->greeting('') // Remove default greeting since it's in the custom body
                ->line(new HtmlString(nl2br(e($customContent['body']))));
        }

        // Fallback to default template
        $subject = $this->action === 'approve'
            ? "Branch Approved - {$this->branch->name}"
            : "Branch Declined - {$this->branch->name}";

        $mailMessage = (new MailMessage)
            ->subject($subject)
            ->greeting('Hello ' . $notifiable->name . ',');

        if ($this->action === 'approve') {
            $mailMessage->line("Your branch **{$this->branch->name}** has been approved!")
                ->line("**Cooperative:** {$cooperativeName}")
                ->line("**Approved by:** {$actionBy}")
                ->line("**Approved on:** " . now()->format('d M Y H:i:s'))
                ->line("Your branch is now active and ready for use.");
        } else {
            $mailMessage->line("Your branch **{$this->branch->name}** has been declined.")
                ->line("**Cooperative:** {$cooperativeName}")
                ->line("**Declined by:** {$actionBy}")
                ->line("**Declined on:** " . now()->format('d M Y H:i:s'));

            if ($this->reason) {
                $mailMessage->line("**Reason for decline:**")
                    ->line($this->reason);
            }

            $mailMessage->line("Please review the feedback and make necessary changes before resubmitting.");
        }

        return $mailMessage
            ->line("If you have any questions, please contact the administrator.")
            ->salutation("Best regards, {$cooperativeName} Team");
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'branch_id' => $this->branch->id,
            'branch_name' => $this->branch->name,
            'action' => $this->action,
            'reason' => $this->reason,
            'action_by' => $this->actionBy ? $this->actionBy->id : null,
            'cooperative_name' => $this->branch->cooperative ? $this->branch->cooperative->name : null,
        ];
    }
}
