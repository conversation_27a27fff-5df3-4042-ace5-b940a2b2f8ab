<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use App\Models\EmailTemplate;
use Illuminate\Support\HtmlString;

class MainAgentKycApprovalNotification extends Notification
{
    protected $mainAgent;
    protected $status;
    protected $reason;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $mainAgent, string $status, ?string $reason = null)
    {
        $this->mainAgent = $mainAgent;
        $this->status = $status;
        $this->reason = $reason;
        Log::info("MainAgentKycApprovalNotification created for main agent {$mainAgent->name}, status: {$status}");
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        Log::info("MainAgentKycApprovalNotification via() called for {$notifiable->name}");
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        Log::info("MainAgentKycApprovalNotification toMail() called for {$notifiable->name}, status: {$this->status}");

        // Get custom email template
        $emailTemplate = EmailTemplate::getByType('main_agent_kyc_approval');

        if ($emailTemplate) {
            // Use custom template
            $placeholderData = [
                'main_agent_name' => $this->mainAgent->name,
                'status' => $this->status,
                'reason' => $this->reason ?? '',
                'cooperative_name' => $this->mainAgent->cooperative ? $this->mainAgent->cooperative->name : 'ACS RMS',
                'dashboard_url' => route('master_agent.dashboard')
            ];

            $customContent = $emailTemplate->replacePlaceholders($placeholderData);
            $mailMessage = (new MailMessage)->subject($customContent['subject']);

            // Split the body based on button position
            $buttonPosition = $customContent['button_position'] ?? 'none';

            if ($buttonPosition === 'none') {
                // Button is already in the body content, don't add another one
                $mailMessage->greeting('') // Remove default greeting
                    ->line(new HtmlString(nl2br($customContent['body'])));
            } else {
                // For other button positions, we'll use the default approach
                $mailMessage->greeting('') // Remove default greeting
                    ->line(new HtmlString(nl2br(e($customContent['body']))));
            }

            return $mailMessage;
        }

        // Fallback to default template
        $mailMessage = (new MailMessage)
            ->subject('KYC Status Update - ' . ucfirst($this->status))
            ->greeting('Hello ' . $notifiable->name . ',');

        if ($this->status === 'approved') {
            Log::info("Building approved email for {$notifiable->name}");
            $mailMessage->line('Congratulations! Your KYC details have been approved.')
                ->line('You now have full access to the ACS RMS system and can start managing your agents and operations.')
                ->line('As a Main Agent, you can now:')
                ->line('• Register and manage sub-agents')
                ->line('• Access commission reports')
                ->line('• View transaction history')
                ->line('• Manage your profile and settings');
        } else {
            Log::info("Building rejected email for {$notifiable->name} with reason: " . ($this->reason ?? 'none provided'));
            $mailMessage->line('Your KYC details have been reviewed but could not be approved at this time.')
                ->line('Please review and update your information, then submit again for approval.');

            if ($this->reason) {
                $mailMessage->line('Reason for rejection:')
                    ->line($this->reason);
            }
        }

        try {
            // Use a route that definitely exists for main agents
            $url = route('master_agent.dashboard');
            Log::info("Action URL for {$notifiable->name}: {$url}");

            return $mailMessage
                ->action('Access Dashboard', $url)
                ->line('Thank you for your cooperation.');
        } catch (\Exception $e) {
            Log::error("Failed to generate URL: " . $e->getMessage());

            // Fall back to not including an action button
            return $mailMessage
                ->line('Please log in to your account to access your dashboard.')
                ->line('Thank you for your cooperation.');
        }
    }

    /**
     * Get the status of the KYC update
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * Get the reason for rejection (if applicable)
     */
    public function getReason(): ?string
    {
        return $this->reason;
    }

    /**
     * Get the main agent
     */
    public function getMainAgent(): User
    {
        return $this->mainAgent;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'main_agent_id' => $this->mainAgent->id,
            'main_agent_name' => $this->mainAgent->name,
            'main_agent_email' => $this->mainAgent->email,
            'status' => $this->status,
            'reason' => $this->reason,
        ];
    }
}
