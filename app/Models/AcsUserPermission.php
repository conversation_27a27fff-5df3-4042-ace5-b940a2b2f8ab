<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AcsUserPermission extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'acs_user_permissions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'main_agent_id',
        'sub_main_agent_id',
        'permissions',
        'hidden_menus',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'permissions' => 'array',
        'hidden_menus' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the main agent who set the permissions.
     */
    public function mainAgent()
    {
        return $this->belongsTo(User::class, 'main_agent_id');
    }

    /**
     * Get the sub-main-agent who receives the permissions.
     */
    public function subMainAgent()
    {
        return $this->belongsTo(User::class, 'sub_main_agent_id');
    }

    /**
     * Check if the user has permission for a specific resource and action.
     */
    public function hasPermission(string $resource, string $action = 'view'): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $permissions = $this->permissions ?? [];

        if (!isset($permissions[$resource])) {
            return false;
        }

        $resourcePermission = $permissions[$resource];

        // If it's an array of permissions
        if (is_array($resourcePermission)) {
            return in_array($action, $resourcePermission);
        }

        // If it's a single permission string
        if ($action === 'view') {
            return in_array($resourcePermission, ['view', 'edit']);
        }

        return $resourcePermission === $action;
    }

    /**
     * Get available permission options.
     */
    public static function getAvailablePermissions(): array
    {
        return [
            'dashboard' => ['view', 'edit'],
            'profile' => ['view', 'edit'],
            'agents' => ['view', 'create', 'edit', 'delete'],
            'reports' => ['view', 'create', 'edit', 'delete'],
            'agreements' => ['view', 'edit'],
            'bank_account' => ['view', 'edit'],
            'branch' => ['view', 'create', 'edit', 'delete'],
            'password_management' => ['view', 'edit'],
        ];
    }

    /**
     * Check if a specific menu is hidden for this user.
     */
    public function isMenuHidden(string $menuKey): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $hiddenMenus = $this->hidden_menus ?? [];
        return in_array($menuKey, $hiddenMenus);
    }

    /**
     * Get list of visible menus for this user.
     */
    public function getVisibleMenus(): array
    {
        $allMenus = [
            'dashboard', 'profile', 'agents', 'branch', 'reports',
            'agreements', 'bank_account', 'password_management', 'settings', 'notifications'
        ];

        $hiddenMenus = $this->hidden_menus ?? [];

        return array_diff($allMenus, $hiddenMenus);
    }

    /**
     * Get list of hidden menu keys.
     */
    public function getHiddenMenus(): array
    {
        return $this->hidden_menus ?? [];
    }

    /**
     * Get permission groups for better organization.
     */
    public static function getPermissionGroups(): array
    {
        return [
            'basic' => [
                'dashboard' => ['view', 'edit'],
                'profile' => ['view', 'edit'],
            ],
            'management' => [
                'agents' => ['view', 'create', 'edit', 'delete'],
                'branch' => ['view', 'create', 'edit', 'delete'],
            ],
            'data' => [
                'reports' => ['view', 'create', 'edit', 'delete'],
                'agreements' => ['view', 'edit'],
            ],
            'security' => [
                'bank_account' => ['view', 'edit'],
                'password_management' => ['view', 'edit'],
            ]
        ];
    }
}
