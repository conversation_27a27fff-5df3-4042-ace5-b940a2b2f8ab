<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class AcsBranchApprovalHistory extends Model
{
    use HasFactory, HasUlids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'acs_branch_approval_history';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'acs_coorperative_branch_id',
        'action',
        'reason',
        'action_by',
        'previous_status',
        'new_status',
        'additional_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'additional_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the branch that this approval history belongs to.
     */
    public function branch()
    {
        return $this->belongsTo(AcsCooperativeBranch::class, 'acs_coorperative_branch_id');
    }

    /**
     * Get the user who performed the action.
     */
    public function actionBy()
    {
        return $this->belongsTo(User::class, 'action_by');
    }

    /**
     * Scope to get approvals only
     */
    public function scopeApprovals($query)
    {
        return $query->where('action', 'approve');
    }

    /**
     * Scope to get declines only
     */
    public function scopeDeclines($query)
    {
        return $query->where('action', 'decline');
    }

    /**
     * Scope to get history for a specific branch
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('acs_coorperative_branch_id', $branchId);
    }

    /**
     * Scope to get history by action user
     */
    public function scopeByActionUser($query, $userId)
    {
        return $query->where('action_by', $userId);
    }

    /**
     * Scope to get latest actions
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Get the action display name
     */
    public function getActionDisplayNameAttribute()
    {
        return ucfirst($this->action);
    }

    /**
     * Check if this is an approval action
     */
    public function isApproval()
    {
        return $this->action === 'approve';
    }

    /**
     * Check if this is a decline action
     */
    public function isDecline()
    {
        return $this->action === 'decline';
    }
}
