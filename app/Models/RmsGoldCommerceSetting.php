<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RmsGoldCommerceSetting extends Model
{
    protected $fillable = [
        'api_key',
        'api_secret',
        'api_url',
        'is_active',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public static function getActiveSettings()
    {
        return self::where('is_active', true)->first();
    }

    public static function updateOrCreateSettings(array $data)
    {
        // Deactivate all existing settings
        self::where('is_active', true)->update(['is_active' => false]);

        // Create new active settings
        return self::create(array_merge($data, ['is_active' => true]));
    }
}
