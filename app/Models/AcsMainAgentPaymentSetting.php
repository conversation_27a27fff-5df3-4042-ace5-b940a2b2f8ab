<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class AcsMainAgentPaymentSetting extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'acs_main_agent_payment_settings';

    protected $fillable = [
        'main_agent_id',
        'payment_method',
        'notes',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    // Payment method constants
    const PAYMENT_METHOD_INDIVIDUAL_BRANCH = 'individual_branch';
    const PAYMENT_METHOD_MAIN_COOPERATIVE = 'main_cooperative';

    /**
     * Get the main agent that owns this payment setting
     */
    public function mainAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'main_agent_id');
    }

    /**
     * Get the user who created this setting
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this setting
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get available payment methods
     */
    public static function getPaymentMethods(): array
    {
        return [
            self::PAYMENT_METHOD_MAIN_COOPERATIVE => 'Main Cooperative',
            self::PAYMENT_METHOD_INDIVIDUAL_BRANCH => 'Individual Branch',
        ];
    }

    /**
     * Check if payment method is by individual branch
     */
    public function isIndividualBranchPayment(): bool
    {
        return $this->payment_method === self::PAYMENT_METHOD_INDIVIDUAL_BRANCH;
    }

    /**
     * Check if payment method is by main cooperative
     */
    public function isMainCooperativePayment(): bool
    {
        return $this->payment_method === self::PAYMENT_METHOD_MAIN_COOPERATIVE;
    }

    /**
     * Get the active payment setting for a main agent
     */
    public static function getActiveSettingForAgent($mainAgentId)
    {
        return self::where('main_agent_id', $mainAgentId)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Create or update payment setting for a main agent
     */
    public static function updateOrCreateForAgent($mainAgentId, array $data)
    {
        return DB::transaction(function () use ($mainAgentId, $data) {
            // Deactivate existing active settings
            self::where('main_agent_id', $mainAgentId)
                ->where('is_active', true)
                ->update(['is_active' => false]);

            // Create new active setting
            return self::create(array_merge($data, [
                'main_agent_id' => $mainAgentId,
                'is_active' => true,
            ]));
        });
    }

    /**
     * Boot method to add model events
     */
    protected static function boot()
    {
        parent::boot();

        // Ensure only one active setting per main agent
        static::creating(function ($model) {
            if ($model->is_active) {
                self::where('main_agent_id', $model->main_agent_id)
                    ->where('is_active', true)
                    ->update(['is_active' => false]);
            }
        });

        static::updating(function ($model) {
            if ($model->is_active && $model->isDirty('is_active')) {
                self::where('main_agent_id', $model->main_agent_id)
                    ->where('id', '!=', $model->id)
                    ->where('is_active', true)
                    ->update(['is_active' => false]);
            }
        });
    }
}
