<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class AcsUsersBranchChangeLog extends Model
{
    use HasFactory, HasUlids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'acs_users_branch_change_logs';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'acs_users_id',
        'from_branch_id',
        'to_branch_id',
        'from_branch_name',
        'to_branch_name',
        'from_cooperative_id',
        'to_cooperative_id',
        'from_cooperative_name',
        'to_cooperative_name',
        'reason',
        'action_done_by',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that this branch change log belongs to.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'acs_users_id', 'id');
    }

    /**
     * Get the user who performed the action.
     */
    public function actionBy()
    {
        return $this->belongsTo(User::class, 'action_done_by', 'id');
    }

    /**
     * Get the previous branch.
     */
    public function fromBranch()
    {
        return $this->belongsTo(AcsCooperativeBranch::class, 'from_branch_id');
    }

    /**
     * Get the new branch.
     */
    public function toBranch()
    {
        return $this->belongsTo(AcsCooperativeBranch::class, 'to_branch_id');
    }

    /**
     * Get the previous cooperative.
     */
    public function fromCooperative()
    {
        return $this->belongsTo(AcsCooperative::class, 'from_cooperative_id');
    }

    /**
     * Get the new cooperative.
     */
    public function toCooperative()
    {
        return $this->belongsTo(AcsCooperative::class, 'to_cooperative_id');
    }

    /**
     * Scope to get logs for a specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('acs_users_id', $userId);
    }

    /**
     * Scope to get latest changes
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Scope to get changes by action user
     */
    public function scopeByActionUser($query, $userId)
    {
        return $query->where('action_done_by', $userId);
    }
}
