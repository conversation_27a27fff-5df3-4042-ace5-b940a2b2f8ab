<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AcsBankDetail extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'acs_bank_details';

    protected $fillable = [
        'acs_users_id',
        'acs_bank_names_id',
        'account_number',
        'account_holder_name',
        'bank_statement',
        'is_default',
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns this bank detail
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'acs_users_id', 'id');
    }

    /**
     * Get the bank name for this bank detail
     */
    public function bankName()
    {
        return $this->belongsTo(AcsBankName::class, 'acs_bank_names_id');
    }

    /**
     * Get the user details that uses this bank detail
     */
    public function userDetail()
    {
        return $this->hasOne(AcsUsersDetail::class, 'acs_bank_detail_id');
    }

    /**
     * Scope to get default bank details
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to get non-default bank details
     */
    public function scopeNonDefault($query)
    {
        return $query->where('is_default', false);
    }

    /**
     * Set this bank detail as default and unset others for the same user
     */
    public function setAsDefault()
    {
        // Remove default from other bank details of the same user
        static::where('acs_users_id', $this->acs_users_id)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        // Set this as default
        $this->update(['is_default' => true]);
    }

    /**
     * Check if this bank detail can be deleted
     */
    public function canBeDeleted()
    {
        return !$this->is_default;
    }
}
