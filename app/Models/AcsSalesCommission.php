<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class AcsSalesCommission extends Model
{
    use HasFactory;
    use SoftDeletes;
    use HasUlids;

    protected $table = 'acs_sales_commission';

    protected $fillable = [
        'table_22_jualan_id',
        'table_23_senarai_jualan_id',
        'invoice_no',
        'acs_campaign_id',
        'affiliate_membership_no',
        'senarai_pelanggan_id',
        'comission_percentage',
        'comission_amount',
    ];

    protected $casts = [
        'table_22_jualan_id' => 'integer',
        'table_23_senarai_jualan_id' => 'integer',
        'senarai_pelanggan_id' => 'integer',
        'comission_percentage' => 'decimal:2',
        'comission_amount' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the campaign this sales commission belongs to
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(AcsCampaign::class, 'acs_campaign_id');
    }

    /**
     * Get the commission distributions for this sales commission
     */
    public function distributions(): HasMany
    {
        return $this->hasMany(AcsCommissionDistribution::class, 'acs_sales_commission_id');
    }

    /**
     * Check if this sales commission is part of a campaign
     */
    public function isPartOfCampaign(): bool
    {
        return !is_null($this->acs_campaign_id);
    }

    /**
     * Get the campaign name if applicable
     */
    public function getCampaignNameAttribute(): ?string
    {
        return $this->campaign?->title;
    }

    /**
     * Scope for sales commissions with campaigns
     */
    public function scopeWithCampaign($query)
    {
        return $query->whereNotNull('acs_campaign_id');
    }

    /**
     * Scope for sales commissions without campaigns
     */
    public function scopeWithoutCampaign($query)
    {
        return $query->whereNull('acs_campaign_id');
    }

    /**
     * Scope for specific campaign
     */
    public function scopeForCampaign($query, $campaignId)
    {
        return $query->where('acs_campaign_id', $campaignId);
    }
}
