<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AcsBankName extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'acs_bank_names';

    protected $fillable = [
        'bank_name',
        'bank_code',
    ];

    /**
     * Get the bank details for this bank
     */
    public function bankDetails()
    {
        return $this->hasMany(AcsBankDetail::class, 'acs_bank_names_id');
    }
}
