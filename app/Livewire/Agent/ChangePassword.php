<?php

namespace App\Livewire\Agent;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

#[Layout('layouts.account.app')]
#[Title('Change Password')]
class ChangePassword extends Component
{
    #[Rule('required|string')]
    public $current_password = '';

    #[Rule('required|string|min:8')]
    public $new_password = '';

    #[Rule('required|string|min:8|same:new_password')]
    public $new_password_confirmation = '';

    public $user = null;

    protected $messages = [
        'current_password.required' => 'Current password is required.',
        'new_password.required' => 'New password is required.',
        'new_password.min' => 'New password must be at least 8 characters.',
        'new_password_confirmation.required' => 'Password confirmation is required.',
        'new_password_confirmation.same' => 'Password confirmation does not match.',
    ];

    public function mount()
    {
        $this->user = Auth::user();
    }

    public function changePassword()
    {
        $this->validate();

        // Check if current password is correct
        if (!Hash::check($this->current_password, $this->user->password)) {
            $this->addError('current_password', 'Current password is incorrect.');
            return;
        }

        try {
            $this->user->update([
                'password' => Hash::make($this->new_password),
            ]);

            // Send password change notification email
            $this->user->notify(new \App\Notifications\PasswordChangeNotification($this->user));

            // Reset form
            $this->reset(['current_password', 'new_password', 'new_password_confirmation']);
            $this->resetErrorBag();

            session()->flash('success', 'Password changed successfully! A confirmation email has been sent to you.');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to change password: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.agent.change-password');
    }
}
