<?php

namespace App\Livewire\Agent;

use App\Services\ThirdParty\RmsGoldCommerceApi;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

#[Layout('layouts.account.app')]
#[Title('Downline')]
class Downline extends Component
{
    public $search = '';
    public $downlineData = [];
    public $totalReferrals = 0;
    public $previousMonthReferrals = 0;
    public $currentMonthReferrals = 0;

    public function mount()
    {
        $this->loadStatistics();
    }

    public function updatedSearch()
    {
        $this->loadDownlineFromRms();
    }

    public function clearSearch()
    {
        $this->search = '';
        $this->loadDownlineFromRms();
    }

    public function refreshDownlineData()
    {
        $this->loadDownlineFromRms();
        session()->flash('success', 'Downline data refreshed successfully!');
    }

    public function loadStatistics()
    {
        // Try to get statistics from RMS first
        $referralCode = $this->getUserReferralCode();

        if ($referralCode) {
            $rmsDownlineData = $this->getDownlineFromRms($referralCode);

            // Calculate statistics from RMS data
            $this->totalReferrals = $rmsDownlineData->count();

            $previousMonth = Carbon::now()->subMonth();
            $this->previousMonthReferrals = $rmsDownlineData->filter(function ($item) use ($previousMonth) {
                $itemDate = Carbon::parse($item->tarikh);
                return $itemDate->year === $previousMonth->year &&
                       $itemDate->month === $previousMonth->month;
            })->count();

            $currentMonth = Carbon::now();
            $this->currentMonthReferrals = $rmsDownlineData->filter(function ($item) use ($currentMonth) {
                $itemDate = Carbon::parse($item->tarikh);
                return $itemDate->year === $currentMonth->year &&
                       $itemDate->month === $currentMonth->month;
            })->count();
        } else {
            // Fallback to local database statistics
            $userId = Auth::user()->id;

            $this->totalReferrals = DB::table('senarai_pelanggan')
                ->where('acs_users_id', $userId)
                ->count();

            $previousMonth = Carbon::now()->subMonth();
            $this->previousMonthReferrals = DB::table('senarai_pelanggan')
                ->where('acs_users_id', $userId)
                ->whereYear('tarikh', $previousMonth->year)
                ->whereMonth('tarikh', $previousMonth->month)
                ->count();

            $currentMonth = Carbon::now();
            $this->currentMonthReferrals = DB::table('senarai_pelanggan')
                ->where('acs_users_id', $userId)
                ->whereYear('tarikh', $currentMonth->year)
                ->whereMonth('tarikh', $currentMonth->month)
                ->count();
        }
    }

    public function loadDownlineData()
    {
        $query = DB::table('senarai_pelanggan')
            ->select('senarai_pelanggan.nama', 'senarai_pelanggan.email', 'senarai_pelanggan.tarikh')
            ->where('senarai_pelanggan.acs_users_id', Auth::user()->id);

        // Apply search filter if search term exists
        if (!empty($this->search)) {
            $query->where(function ($q) {
                $q->where('senarai_pelanggan.nama', 'like', '%' . $this->search . '%')
                  ->orWhere('senarai_pelanggan.email', 'like', '%' . $this->search . '%');
            });
        }

        $this->downlineData = $query->orderBy('senarai_pelanggan.tarikh', 'desc')->get();
    }

    private function getDownlineFromRms($referral_code)
    {
        try {
            $api = RmsGoldCommerceApi::make();
            $response = $api->requestPost(RmsGoldCommerceApi::ENDPOINT_POST_DOWNLINE, RmsGoldCommerceApi::HTTP_METHOD_POST, [
                'referral_code' => $referral_code
            ]);

            // Transform the API response to match our expected format
            $result = collect($response)->map(function ($item) {
                return (object) [
                    'nama' => $item['nama'] ?? '',
                    'email' => $item['email'] ?? '',
                    'tarikh' => $item['tarikh'] ?? '',
                    'no_pelanggan' => $item['no_pelanggan'] ?? '',
                    'no_ic' => $item['no_ic'] ?? '',
                    'no_tel' => $item['no_tel'] ?? '',
                    'status' => $item['status'] ?? '',
                    'baki_point' => $item['baki_point'] ?? 0,
                    'referral_code' => $item['referral_code'] ?? '',
                ];
            });

            return $result;
        } catch (\Exception $e) {
            // Log the error and return empty collection
            Log::error('Failed to fetch downline from RMS: ' . $e->getMessage());
            return collect([]);
        }
    }

    public function loadDownlineFromRms()
    {
        // Get the current user's referral code
        $referralCode = $this->getUserReferralCode();

        if ($referralCode) {
            $rmsDownlineData = $this->getDownlineFromRms($referralCode);

            // Apply search filter if search term exists
            if (!empty($this->search)) {
                $rmsDownlineData = $rmsDownlineData->filter(function ($item) {
                    return stripos($item->nama, $this->search) !== false ||
                           stripos($item->email, $this->search) !== false ||
                           stripos($item->no_pelanggan, $this->search) !== false;
                });
            }

            // Sort by date (newest first)
            $this->downlineData = $rmsDownlineData->sortByDesc('tarikh')->values();
        } else {
            // Fallback to local data if no referral code
            $this->loadDownlineData();
        }
    }

    private function getUserReferralCode()
    {
        // You might need to adjust this based on where the referral code is stored
        // This could be in the users table or a related table
        $user = Auth::user();

        // Example: if referral code is stored in users table
        // return $user->referral_code ?? null;

        // Example: if referral code is stored in a related table
        // return $user->agent_profile->referral_code ?? null;

        // For now, using a test referral code - replace with actual logic
        return 'REFwc6abpA1B'; // This matches the referral_code in your API response
    }

    public function render()
    {
        $this->loadDownlineFromRms();

        return view('livewire.agent.downline', [
            'downlineData' => $this->downlineData,
        ]);
    }
}
