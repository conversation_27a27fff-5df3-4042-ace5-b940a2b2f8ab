<?php

namespace App\Livewire\Agent;

use Livewire\Component;
use App\Models\HargaEmas;
use App\Services\ThirdParty\RmsGoldCommerceApi;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class GoldPrice extends Component
{
    public $goldPrices = [];
    public $lastUpdated = null;
    public $isLoading = false;
    public $errorMessage = null;

    public function mount()
    {
        $this->loadGoldPrices();
    }

    public function loadGoldPrices()
    {
        try {
            $this->isLoading = true;
            $this->errorMessage = null;

            $api = RmsGoldCommerceApi::make();
            $response = $api->request(RmsGoldCommerceApi::ENDPOINT_GET_GOLD_PRICE, RmsGoldCommerceApi::HTTP_METHOD_GET);

            // Handle the nested response structure
            if (isset($response['gold_price']) && is_array($response['gold_price'])) {
                $this->goldPrices = collect($response['gold_price'])->map(function ($item) {
                    return [
                        'Purity' => $item['Purity'] ?? '',
                        'Harga_Pelanggan' => number_format((float)($item['Harga_Pelanggan'] ?? 0), 2),
                        'Harga_Member' => number_format((float)($item['Harga_Member'] ?? 0), 2),
                        'Harga_Pengedar' => number_format((float)($item['Harga_Pengedar'] ?? 0), 2),
                        'Harga_RAF' => number_format((float)($item['Harga_RAF'] ?? 0), 2),
                        'harga_nd' => number_format((float)($item['harga_nd'] ?? 0), 2),
                        'harga_trade_in' => number_format((float)($item['harga_trade_in'] ?? 0), 2),
                        'harga_buyback' => number_format((float)($item['harga_buyback'] ?? 0), 2),
                    ];
                })->toArray();

                // Set last updated time
                if (isset($response['time']['write_timestamp'])) {
                    $this->lastUpdated = Carbon::parse($response['time']['write_timestamp'])->format('d/m/Y H:i:s');
                }
            } else {
                $this->goldPrices = [];
                $this->errorMessage = 'Invalid response format from API';
            }

        } catch (\Exception $e) {
            Log::error('Failed to fetch gold prices from RMS: ' . $e->getMessage());
            $this->errorMessage = 'Failed to fetch gold prices. Please try again later.';
            $this->goldPrices = [];

            // Fallback to local data if available
            $this->loadLocalGoldPrices();
        } finally {
            $this->isLoading = false;
        }
    }

    private function loadLocalGoldPrices()
    {
        try {
            // Fallback to local database if RMS API fails
            $localPrices = HargaEmas::latest()->first();

            if ($localPrices) {
                $this->goldPrices = [
                    [
                        'Purity' => '22K',
                        'Harga_Pelanggan' => number_format((float)$localPrices->harga_pelanggan, 2),
                        'Harga_Member' => number_format((float)$localPrices->harga_member, 2),
                        'Harga_Pengedar' => number_format((float)$localPrices->harga_pengedar, 2),
                        'Harga_RAF' => number_format((float)$localPrices->harga_raf, 2),
                        'harga_nd' => number_format((float)$localPrices->harga_nd, 2),
                        'harga_trade_in' => '0.00',
                        'harga_buyback' => '0.00',
                    ]
                ];
                $this->lastUpdated = $localPrices->updated_at->format('d/m/Y H:i:s');
            }
        } catch (\Exception $e) {
            Log::error('Failed to load local gold prices: ' . $e->getMessage());
        }
    }

    public function refreshGoldPrices()
    {
        $this->loadGoldPrices();

        if (!$this->errorMessage) {
            session()->flash('success', 'Gold prices updated successfully!');
        }
    }

    public function render()
    {
        return view('livewire.agent.gold-price', [
            'goldPrices' => $this->goldPrices,
            'lastUpdated' => $this->lastUpdated,
            'isLoading' => $this->isLoading,
            'errorMessage' => $this->errorMessage,
        ]);
    }
}
