<?php

namespace App\Livewire\MasterAgent;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Rule;
use Livewire\WithPagination;
use App\Models\User;
use App\Models\AcsUserPermission;
use App\Models\AcsRole;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

// #[Layout('layouts.master-agent.app')]
#[Title('Register Sub Main Agent')]
class RegisterSubMainAgent extends Component
{
    use WithPagination;

    // Registration form fields
    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('required|email|unique:acs_users,email')]
    public $email = '';

    // Modal states
    public $showModal = false;
    public $showDetailModal = false;
    public $registrationLink = '';
    public $showLinkCopy = false;

    // Detail modal
    public $selectedSubAgent = null;

    // Filter and search properties
    public $search = '';
    public $statusFilter = '';
    public $perPage = 10;
    public $sortBy = 'name';
    public $sortOrder = 'asc';

    public function mount()
    {
        // Component initialization
    }

    public function register()
    {
        $this->validate();

        try {
            DB::beginTransaction();

            // Get the sub-main-agent role
            $subMainAgentRole = AcsRole::where('name', 'sub-main-agent')->first();
            if (!$subMainAgentRole) {
                throw new \Exception('Sub-main-agent role not found in the system.');
            }

            // Get current user's cooperative info
            $currentUser = Auth::user();

            // Create the user with temporary password
            $user = User::create([
                'name' => $this->name,
                'email' => $this->email,
                'username' => $this->email,
                'phone' => '',
                'password' => Hash::make(Str::random(32)),
                'acs_role_id' => $subMainAgentRole->id,
                'invited_by' => Auth::id(),
                'acs_coorperative_id' => $currentUser->acs_coorperative_id,
                'acs_coorperative_branch_id' => $currentUser->acs_coorperative_branch_id,
                'status' => User::STATUS_PENDING,
            ]);

            // Create default permissions for the sub-main-agent
            AcsUserPermission::create([
                'main_agent_id' => Auth::id(),
                'sub_main_agent_id' => $user->id,
                'permissions' => $this->permissions,
                'is_active' => true,
            ]);

            // Create password reset token
            $token = Str::random(64);

            DB::table('password_reset_tokens')->updateOrInsert(
                ['email' => $this->email],
                [
                    'token' => Hash::make($token),
                    'created_at' => now()
                ]
            );

            // Generate registration link
            $this->registrationLink = route('agent.password.create') . '?token=' . $token;

            DB::commit();

            session()->flash('success', 'Sub-main-agent registered successfully!');
            $this->showLinkCopy = true;
            $this->reset(['name', 'email']);

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to register sub-main-agent: ' . $e->getMessage());
        }
    }

    public function copyLink()
    {
        session()->flash('success', 'Registration link copied to clipboard!');
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->showLinkCopy = false;
        $this->reset(['name', 'email', 'registrationLink']);
        $this->initializePermissions();
    }

    public function viewDetails($userId)
    {
        $this->selectedSubAgent = User::with(['cooperative', 'cooperativeBranch', 'receivedPermissions'])->find($userId);
        $this->showDetailModal = true;
    }

    public function managePermissions($userId)
    {
        return redirect()->route('master_agent.manage-permissions', ['userId' => $userId]);
    }



    public function deactivateUser($userId)
    {
        try {
            $user = User::find($userId);
            $user->update(['status' => User::STATUS_SUSPENDED]);

            // Deactivate permissions
            if ($user->receivedPermissions) {
                $user->receivedPermissions->update(['is_active' => false]);
            }

            session()->flash('success', 'Sub-main-agent deactivated successfully!');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to deactivate user: ' . $e->getMessage());
        }
    }

    public function activateUser($userId)
    {
        try {
            $user = User::find($userId);
            $user->update(['status' => User::STATUS_ACTIVE]);

            // Activate permissions
            if ($user->receivedPermissions) {
                $user->receivedPermissions->update(['is_active' => true]);
            }

            session()->flash('success', 'Sub-main-agent activated successfully!');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to activate user: ' . $e->getMessage());
        }
    }

    public function getSubMainAgentsProperty()
    {
        $query = User::with(['cooperative', 'cooperativeBranch', 'receivedPermissions'])
            ->whereHas('role', function ($q) {
                $q->where('name', 'sub-main-agent');
            })
            ->whereHas('receivedPermissions', function ($q) {
                $q->where('main_agent_id', Auth::id());
            });

        // Apply search filter
        if (!empty($this->search)) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%');
            });
        }

        // Apply status filter
        if (!empty($this->statusFilter)) {
            $query->where('status', $this->statusFilter);
        }

        return $query->orderBy($this->sortBy, $this->sortOrder)
                    ->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.master-agent.register-sub-main-agent', [
            'subMainAgents' => $this->subMainAgents,
        ]);
    }
}
