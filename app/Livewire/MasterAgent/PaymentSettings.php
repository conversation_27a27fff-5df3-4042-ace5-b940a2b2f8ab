<?php

namespace App\Livewire\MasterAgent;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Rule;
use App\Models\AcsMainAgentPaymentSetting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

// #[Layout('layouts.account')]
#[Title('Payment Settings')]
class PaymentSettings extends Component
{
    #[Rule('required|in:main_cooperative,individual_branch')]
    public $payment_method = 'main_cooperative';

    #[Rule('nullable|string|max:1000')]
    public $notes = '';

    public $currentSetting = null;

    public function mount()
    {
        $this->loadCurrentSettings();
    }

    public function loadCurrentSettings()
    {
        $this->currentSetting = AcsMainAgentPaymentSetting::getActiveSettingForAgent(Auth::id());

        if ($this->currentSetting) {
            $this->payment_method = $this->currentSetting->payment_method;
            $this->notes = $this->currentSetting->notes ?? '';
        }
    }

    public function saveSettings()
    {
        $this->validate();

        try {
            DB::beginTransaction();

            $data = [
                'payment_method' => $this->payment_method,
                'notes' => $this->notes,
                'created_by' => Auth::id(),
                'updated_by' => Auth::id(),
            ];

            AcsMainAgentPaymentSetting::updateOrCreateForAgent(Auth::id(), $data);

            DB::commit();

            session()->flash('success', 'Payment settings saved successfully!');
            $this->loadCurrentSettings(); // Reload to get the updated data

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to save payment settings: ' . $e->getMessage());
        }
    }

    public function resetSettings()
    {
        $this->payment_method = 'main_cooperative';
        $this->notes = '';
    }

    public function render()
    {
        return view('livewire.master-agent.payment-settings', [
            'paymentMethods' => AcsMainAgentPaymentSetting::getPaymentMethods(),
        ]);
    }
}
