<?php

namespace App\Livewire\MasterAgent;

use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\AcsBankDetail;
use App\Models\AcsBankName;

class BankAccount extends Component
{
    use WithFileUploads;

    // List of bank accounts
    public $bankAccounts = [];

    // Bank names for dropdown
    public $bankNames = [];

    // Form fields
    #[Rule('required|exists:acs_bank_names,id')]
    public $acs_bank_names_id = '';

    #[Rule('required|string|max:50')]
    public $account_number = '';

    #[Rule('required|string|max:100')]
    public $account_holder_name = '';

    #[Rule('nullable|file|mimes:pdf,jpg,jpeg,png|max:2048')]
    public $bank_statement = null;

    public $is_default = false;

    // Modal states
    public $showModal = false;
    public $editMode = false;
    public $editingBankId = null;

    // Delete confirmation
    public $showDeleteModal = false;
    public $deletingBankId = null;

    protected $messages = [
        'acs_bank_names_id.required' => 'Please select a bank',
        'acs_bank_names_id.exists' => 'Please select a valid bank',
        'account_number.required' => 'Account number is required',
        'account_number.max' => 'Account number must not exceed 50 characters',
        'account_holder_name.required' => 'Account holder name is required',
        'account_holder_name.max' => 'Account holder name must not exceed 100 characters',
        'bank_statement.mimes' => 'Bank statement must be a PDF, JPG, JPEG, or PNG file',
        'bank_statement.max' => 'Bank statement file size must not exceed 2MB',
    ];

    public function mount()
    {
        $this->loadBankAccounts();
        $this->loadBankNames();
    }

    public function render()
    {
        return view('livewire.master-agent.bank-account');
    }

    public function loadBankAccounts()
    {
        $this->bankAccounts = AcsBankDetail::with('bankName')
            ->where('acs_users_id', Auth::id())
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function loadBankNames()
    {
        $this->bankNames = AcsBankName::orderBy('bank_name', 'asc')->get();
    }

    public function openCreateModal()
    {
        $this->resetForm();
        $this->editMode = false;
        $this->showModal = true;
    }

    public function openEditModal($bankId)
    {
        $bankDetail = AcsBankDetail::where('id', $bankId)
            ->where('acs_users_id', Auth::id())
            ->firstOrFail();

        $this->editingBankId = $bankId;
        $this->acs_bank_names_id = $bankDetail->acs_bank_names_id;
        $this->account_number = $bankDetail->account_number;
        $this->account_holder_name = $bankDetail->account_holder_name;
        $this->is_default = $bankDetail->is_default;
        $this->bank_statement = null; // Reset file input

        $this->editMode = true;
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function save()
    {
        $this->validate();

        // Custom validation for default account
        if ($this->is_default) {
            DB::table('acs_bank_details')->where('acs_users_id', Auth::id())->update(['is_default' => false]);
        }


        try {
            DB::beginTransaction();

            $user = Auth::user();

            // Handle file upload
            $bankStatementPath = null;
            if ($this->bank_statement) {
                $bankStatementPath = $this->bank_statement->store('bank-statements', 'public');
            }

            $data = [
                'acs_users_id' => $user->id,
                'acs_bank_names_id' => $this->acs_bank_names_id,
                'account_number' => $this->account_number,
                'account_holder_name' => $this->account_holder_name,
                'is_default' => $this->is_default,
            ];

            if ($bankStatementPath) {
                $data['bank_statement'] = $bankStatementPath;
            }

            if ($this->editMode) {
                // Update existing bank account
                $bankDetail = AcsBankDetail::where('id', $this->editingBankId)
                    ->where('acs_users_id', $user->id)
                    ->firstOrFail();

                // Don't update bank_statement if no new file uploaded
                if (!$bankStatementPath) {
                    unset($data['bank_statement']);
                }

                $bankDetail->update($data);
            } else {
                // Create new bank account
                $bankDetail = AcsBankDetail::create($data);

                // If this is the user's first bank account, make it default
                $existingCount = AcsBankDetail::where('acs_users_id', $user->id)->count();
                if ($existingCount == 1) {
                    $bankDetail->update(['is_default' => true]);
                }
            }

            // If marked as default, ensure only this one is default
            if ($this->is_default) {
                $bankDetail->setAsDefault();
            }

            // Ensure at least one default account exists
            $hasDefault = AcsBankDetail::where('acs_users_id', $user->id)
                ->where('is_default', true)
                ->exists();

            if (!$hasDefault) {
                // Set the first bank account as default if none exists
                $firstBank = AcsBankDetail::where('acs_users_id', $user->id)->first();
                if ($firstBank) {
                    $firstBank->update(['is_default' => true]);
                }
            }

            DB::commit();

            $this->loadBankAccounts();
            $this->closeModal();

            session()->flash('success', $this->editMode ? 'Bank account updated successfully!' : 'Bank account added successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to save bank account: ' . $e->getMessage());
        }
    }

        public function setAsDefault($bankId)
    {
        try {
            Log::info('Setting bank as default', [
                'bank_id' => $bankId,
                'user_id' => Auth::id()
            ]);

            DB::beginTransaction();

            $bankDetail = AcsBankDetail::where('id', $bankId)
                ->where('acs_users_id', Auth::id())
                ->firstOrFail();

            Log::info('Found bank detail', [
                'bank_detail_id' => $bankDetail->id,
                'current_is_default' => $bankDetail->is_default,
                'account_number' => $bankDetail->account_number
            ]);

            // Check if this bank account is already default
            if ($bankDetail->is_default) {
                session()->flash('info', 'This bank account is already set as default.');
                return;
            }

            // Before setting as default, log current state
            $currentDefaults = AcsBankDetail::where('acs_users_id', Auth::id())
                ->where('is_default', true)
                ->get();

            Log::info('Current default accounts', [
                'count' => $currentDefaults->count(),
                'ids' => $currentDefaults->pluck('id')->toArray()
            ]);

            $bankDetail->setAsDefault();

            // Verify the change was made
            $bankDetail->refresh();
            Log::info('After setAsDefault', [
                'is_default' => $bankDetail->is_default
            ]);

            DB::commit();

            // Force reload the bank accounts data
            $this->loadBankAccounts();

            // Force Livewire to refresh the component
            $this->dispatch('bankAccountUpdated');

            session()->flash('success', 'Default bank account updated successfully!');
            Log::info('Set default bank account completed successfully');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to set default bank account', [
                'bank_id' => $bankId,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            session()->flash('error', 'Failed to set default bank account: ' . $e->getMessage());
        }
    }

    public function confirmDelete($bankId)
    {
        $bankDetail = AcsBankDetail::where('id', $bankId)
            ->where('acs_users_id', Auth::id())
            ->firstOrFail();

        if (!$bankDetail->canBeDeleted()) {
            session()->flash('error', 'Cannot delete default bank account. Please set another account as default first.');
            return;
        }

        // Additional check to ensure user has at least 2 bank accounts before deleting
        $totalBankAccounts = AcsBankDetail::where('acs_users_id', Auth::id())->count();
        if ($totalBankAccounts <= 1) {
            session()->flash('error', 'Cannot delete your only bank account. You must have at least one bank account.');
            return;
        }

        $this->deletingBankId = $bankId;
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        try {
            $bankDetail = AcsBankDetail::where('id', $this->deletingBankId)
                ->where('acs_users_id', Auth::id())
                ->firstOrFail();

            if (!$bankDetail->canBeDeleted()) {
                session()->flash('error', 'Cannot delete default bank account.');
                $this->closeDeleteModal();
                return;
            }

            // Additional protection - ensure at least one bank account remains
            $totalBankAccounts = AcsBankDetail::where('acs_users_id', Auth::id())->count();
            if ($totalBankAccounts <= 1) {
                session()->flash('error', 'Cannot delete your only bank account.');
                $this->closeDeleteModal();
                return;
            }

            $bankDetail->delete();
            $this->loadBankAccounts();
            $this->closeDeleteModal();

            session()->flash('success', 'Bank account deleted successfully!');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to delete bank account: ' . $e->getMessage());
            $this->closeDeleteModal();
        }
    }

    public function closeDeleteModal()
    {
        $this->showDeleteModal = false;
        $this->deletingBankId = null;
    }

    private function resetForm()
    {
        $this->acs_bank_names_id = '';
        $this->account_number = '';
        $this->account_holder_name = '';
        $this->bank_statement = null;
        $this->is_default = false;
        $this->editingBankId = null;
        $this->resetErrorBag();
    }
}
