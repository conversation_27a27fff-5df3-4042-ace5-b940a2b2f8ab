<?php

namespace App\Livewire\MasterAgent;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Rule;
use App\Models\User;
use App\Models\AcsUserPermission;
use App\Models\AcsRole;
use App\Models\AcsUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

// #[Layout('layouts.master-agent.app')]
#[Title('Register New Sub Main Agent')]
class RegisterSubMainAgentForm extends Component
{
    // Registration form fields
    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('required|email|unique:acs_users,email')]
    public $email = '';

    // UI Settings
    #[Rule('array')]
    public $hiddenMenus = [];

        // Permissions management
    public $permissions = [];
    public $availablePermissions = [];
    public $registrationLink = '';
    public $showLinkCopy = false;

    public function mount()
    {
        $this->initializePermissions();
    }

    public function initializePermissions()
    {
        $this->availablePermissions = [
            'dashboard' => ['view', 'edit'],
            'profile' => ['view', 'edit'],
            'agents' => ['view', 'create', 'edit', 'delete'],
            'branch' => ['view', 'create', 'edit', 'delete'],
            'reports' => ['view', 'create', 'edit', 'delete'],
            'agreements' => ['view', 'edit'],
            'bank_account' => ['view', 'edit'],
            'password_management' => ['view', 'edit'],
        ];

        // Set default permissions (all to 'view' only)
        foreach ($this->availablePermissions as $resource => $actions) {
            $this->permissions[$resource] = ['view'];
        }
    }

    public function togglePermission($resource, $action)
    {
        if (!isset($this->permissions[$resource])) {
            $this->permissions[$resource] = [];
        }

        if (!is_array($this->permissions[$resource])) {
            $this->permissions[$resource] = [$this->permissions[$resource]];
        }

        $currentPermissions = $this->permissions[$resource];

        if (in_array($action, $currentPermissions)) {
            // Remove permission
            $this->permissions[$resource] = array_values(array_diff($currentPermissions, [$action]));

            // Ensure at least 'view' permission remains if no other permissions
            if (empty($this->permissions[$resource])) {
                $this->permissions[$resource] = ['view'];
            }
        } else {
            // Add permission
            $this->permissions[$resource][] = $action;
            $this->permissions[$resource] = array_unique($this->permissions[$resource]);
        }
    }

    public function register()
    {
        $this->validate();

        try {
            DB::beginTransaction();

            // Find or create sub-main-agent role
            $subMainRole = AcsRole::firstOrCreate(
                ['name' => 'sub-main-agent'],
                ['slug' => 'sub-main-agent']
            );

            // Create temporary password and token
            $tempPassword = Str::random(12);
            $token = Str::random(64);
            // Create user
            $user = AcsUser::create([
                'name' => $this->name,
                'email' => $this->email,
                'username' => $this->email, // Required field, using email as username
                'password' => Hash::make($tempPassword),
                'status' => 'pending',
                'invited_by' => Auth::id(), // Correct field name (was parent_id)
                'acs_role_id' => $subMainRole->id, // Correct field name (was role_id)
                'acs_coorperative_id' => Auth::user()->acs_coorperative_id, // Get from inviter
            ]);

            // Create user permissions
            AcsUserPermission::create([
                'sub_main_agent_id' => $user->id, // Correct field name (was user_id)
                'main_agent_id' => Auth::id(), // Correct field name (was granted_by)
                'permissions' => $this->permissions,
                'hidden_menus' => $this->hiddenMenus,
                'is_active' => true,
            ]);

            // Create password reset token for registration link
            DB::table('password_reset_tokens')->updateOrInsert(
                ['email' => $this->email],
                [
                    'token' => Hash::make($token),
                    'created_at' => now()
                ]
            );

            // Generate registration link
            $this->registrationLink = route('agent.password.create') . '?token=' . $token;

            $this->showLinkCopy = true;

            DB::commit();

            session()->flash('success', 'Sub-main-agent registered successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Registration failed: ' . $e->getMessage());
        }
    }

    public function copyLink()
    {
        session()->flash('success', 'Registration link copied to clipboard!');
    }

    public function backToList()
    {
        return redirect()->route('master_agent.sub-main-agent-management');
    }

    public function render()
    {
        return view('livewire.master-agent.register-sub-main-agent-form');
    }
}
