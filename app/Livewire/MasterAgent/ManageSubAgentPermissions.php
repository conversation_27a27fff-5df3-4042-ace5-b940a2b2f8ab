<?php

namespace App\Livewire\MasterAgent;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use App\Models\User;
use App\Models\AcsUserPermission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

// #[Layout('layouts.master-agent.app')]
#[Title('Manage Permissions')]
class ManageSubAgentPermissions extends Component
{
    public $subAgent;
    public $permissions = [];
    public $availablePermissions = [];
    public $hiddenMenus = [];
    public $isLoading = false;

    public function mount($userId)
    {
        try {
            // Load the sub-agent with their permissions
            $this->subAgent = User::with(['receivedPermissions', 'cooperative', 'cooperativeBranch'])
                ->whereHas('role', function ($q) {
                    $q->where('name', 'sub-main-agent');
                })
                ->whereHas('receivedPermissions', function ($q) {
                    $q->where('main_agent_id', Auth::id());
                })
                ->findOrFail($userId);

            $this->initializePermissions();

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to load user data: ' . $e->getMessage());
            return redirect()->route('master_agent.sub-main-agents');
        }
    }

    private function initializePermissions()
    {
        $this->availablePermissions = [
            'dashboard' => ['view', 'edit'],
            'profile' => ['view', 'edit'],
            'agents' => ['view', 'create', 'edit', 'delete'],
            'branch' => ['view', 'create', 'edit', 'delete'],
            'reports' => ['view', 'create', 'edit', 'delete'],
            'agreements' => ['view', 'edit'],
            'bank_account' => ['view', 'edit'],
            'password_management' => ['view', 'edit'],
        ];

        if ($this->subAgent->receivedPermissions) {
            $existingPermissions = $this->subAgent->receivedPermissions->permissions;
            $existingHiddenMenus = $this->subAgent->receivedPermissions->hidden_menus;

            // Properly handle null hidden_menus - convert null to empty array
            if ($existingHiddenMenus === null || $existingHiddenMenus === '') {
                $this->hiddenMenus = [];
            } else if (is_array($existingHiddenMenus)) {
                $this->hiddenMenus = $existingHiddenMenus;
            } else {
                // Handle string or other formats
                $this->hiddenMenus = [];
            }

            // Convert old format to new format if needed
            foreach ($this->availablePermissions as $resource => $actions) {
                if (isset($existingPermissions[$resource])) {
                    $currentPerm = $existingPermissions[$resource];
                    // If it's a string, convert to array
                    if (is_string($currentPerm)) {
                        $this->permissions[$resource] = [$currentPerm];
                    } else if (is_array($currentPerm)) {
                        $this->permissions[$resource] = $currentPerm;
                    } else {
                        $this->permissions[$resource] = ['view'];
                    }
                } else {
                    $this->permissions[$resource] = ['view'];
                }
            }
        } else {
            // Set default permissions
            foreach ($this->availablePermissions as $resource => $actions) {
                $this->permissions[$resource] = ['view'];
            }
            $this->hiddenMenus = [];
        }
    }

    public function togglePermission($resource, $action)
    {
        if (!isset($this->permissions[$resource])) {
            $this->permissions[$resource] = [];
        }

        if (!is_array($this->permissions[$resource])) {
            $this->permissions[$resource] = [$this->permissions[$resource]];
        }

        $currentPermissions = $this->permissions[$resource];

        if (in_array($action, $currentPermissions)) {
            // Remove permission
            $this->permissions[$resource] = array_values(array_diff($currentPermissions, [$action]));

            // Ensure at least 'view' permission remains if no other permissions
            if (empty($this->permissions[$resource])) {
                $this->permissions[$resource] = ['view'];
            }
        } else {
            // Add permission
            $this->permissions[$resource][] = $action;
            $this->permissions[$resource] = array_unique($this->permissions[$resource]);
        }
    }

    public function updatePermissions()
    {
        $this->isLoading = true;

        try {
            // Prepare hidden menus for database storage
            $hiddenMenusForDb = empty($this->hiddenMenus) ? null : $this->hiddenMenus;

            DB::beginTransaction();

            if ($this->subAgent->receivedPermissions) {
                // Update existing permissions
                $this->subAgent->receivedPermissions->update([
                    'permissions' => $this->permissions,
                    'hidden_menus' => $hiddenMenusForDb,
                ]);
            } else {
                // Create new permissions
                AcsUserPermission::create([
                    'main_agent_id' => Auth::id(),
                    'sub_main_agent_id' => $this->subAgent->id,
                    'permissions' => $this->permissions,
                    'hidden_menus' => $hiddenMenusForDb,
                    'is_active' => true,
                ]);
            }

            DB::commit();

            session()->flash('success', 'Permissions and menu settings updated successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to update permissions: ' . $e->getMessage());
        } finally {
            $this->isLoading = false;
        }
    }

    public function cancel()
    {
        return redirect()->route('master_agent.sub-main-agents');
    }

    public function render()
    {
        return view('livewire.master-agent.manage-sub-agent-permissions');
    }
}
