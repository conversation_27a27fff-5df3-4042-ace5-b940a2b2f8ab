<?php

namespace App\Livewire\MasterAgent;

use Livewire\Component;
use Livewire\Attributes\Rule;
use Livewire\Attributes\On;
use Livewire\WithFileUploads;
use App\Services\AcsCoorperativeBranchService;
use App\Services\AcsNotificationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Models\AcsBankName;
use App\Models\AcsMainAgentPaymentSetting;

class BranchList extends Component
{
    use WithFileUploads;

    // Branch creation form properties
    #[Rule('required|string|max:255')]
    public $branch_name = '';

    #[Rule('required|string|max:255')]
    public $organization_name = '';

    #[Rule('required|string|max:255')]
    public $business_registration_no = '';

    #[Rule('nullable|file|mimes:pdf,jpg,jpeg,png|max:2048')]
    public $business_registration_document = null;

    // Bank fields - validation will be conditional
    public $acs_bank_names_id = '';
    public $account_name = '';
    public $account_number = '';

    // Edit branch form properties (NO RULE attributes here)
    public $edit_branch_name = '';
    public $edit_organization_name = '';
    public $edit_business_registration_no = '';
    public $edit_business_registration_document = null;
    public $edit_acs_bank_names_id = '';
    public $edit_account_name = '';
    public $edit_account_number = '';

    public $editing_branch_id = null;
    public $deleting_branch_id = null;

    // Modal state
    public $showCreateBranchModal = false;
    public $showEditBranchModal = false;
    public $showDeleteConfirmationModal = false;

    // Branch data
    public $branches = [];
    public $viewingBranch = null;
    public $bankNames = [];

    // Payment settings
    public $paymentSettings = null;
    public $paymentMethod = 'main_cooperative';

    // Toggle for showing inactive branches (default: false - hide inactive)
    public $showInactiveBranches = false;

    // Statistics
    public $totalBranches = 0;
    public $activeBranches = 0;
    public $inactiveBranches = 0;
    public $enabledRegistrations = 0;
    public $disabledRegistrations = 0;

    protected $branchService;
    protected $notificationService;

    protected $messages = [
        'branch_name.required' => 'Branch name is required.',
        'organization_name.required' => 'Organization name is required.',
        'business_registration_no.required' => 'Business registration number is required.',
        'business_registration_document.mimes' => 'Business registration document must be a PDF, JPG, JPEG, or PNG file.',
        'business_registration_document.max' => 'Business registration document must not exceed 2MB.',
        'acs_bank_names_id.required' => 'Please select a bank (required for Individual Branch payment method).',
        'acs_bank_names_id.exists' => 'Please select a valid bank.',
        'account_name.required' => 'Account name is required (required for Individual Branch payment method).',
        'account_number.required' => 'Account number is required (required for Individual Branch payment method).',
        'edit_branch_name.required' => 'Branch name is required.',
        'edit_organization_name.required' => 'Organization name is required.',
        'edit_business_registration_no.required' => 'Business registration number is required.',
        'edit_business_registration_document.mimes' => 'Business registration document must be a PDF, JPG, JPEG, or PNG file.',
        'edit_business_registration_document.max' => 'Business registration document must not exceed 2MB.',
        'edit_acs_bank_names_id.required' => 'Please select a bank (required for Individual Branch payment method).',
        'edit_acs_bank_names_id.exists' => 'Please select a valid bank.',
        'edit_account_name.required' => 'Account name is required (required for Individual Branch payment method).',
        'edit_account_number.required' => 'Account number is required (required for Individual Branch payment method).',
    ];

    public function boot(AcsCoorperativeBranchService $branchService, AcsNotificationService $notificationService)
    {
        $this->branchService = $branchService;
        $this->notificationService = $notificationService;
    }

    public function mount()
    {
        $this->loadBranches();
        $this->loadBankNames();
        $this->loadPaymentSettings();
    }

    public function render()
    {
        return view('livewire.master-agent.branch-list', [
            'paymentMethod' => $this->paymentMethod,
            'paymentSettings' => $this->paymentSettings
        ]);
    }

    public function loadBankNames()
    {
        $this->bankNames = AcsBankName::orderBy('bank_name')->get();
    }

    public function loadPaymentSettings()
    {
        $this->paymentSettings = AcsMainAgentPaymentSetting::getActiveSettingForAgent(Auth::id());
        $this->paymentMethod = $this->paymentSettings ? $this->paymentSettings->payment_method : 'main_cooperative';
    }

    public function loadBranches()
    {
        try {
            $user = Auth::user();
            if ($user && $user->acs_coorperative_id) {
                // Get all branches first for statistics
                $allBranches = DB::table('acs_coorperative_branch')
                    ->leftJoin('acs_users as created_by_user', 'acs_coorperative_branch.created_by', '=', 'created_by_user.id')
                    ->leftJoin('acs_users as verified_by_user', 'acs_coorperative_branch.verified_by', '=', 'verified_by_user.id')
                    ->leftJoin('acs_bank_names', 'acs_coorperative_branch.acs_bank_names_id', '=', 'acs_bank_names.id')
                    ->select(
                        'acs_coorperative_branch.*',
                        'created_by_user.name as created_by_name',
                        'verified_by_user.name as verified_by_name',
                        'acs_bank_names.bank_name'
                    )
                    ->where('acs_coorperative_branch.acs_coorperative_id', $user->acs_coorperative_id)
                    ->where('acs_coorperative_branch.created_by', $user->id)
                    ->whereNull('acs_coorperative_branch.deleted_at')
                    ->orderBy('acs_coorperative_branch.created_at', 'desc')
                    ->get();

                // Calculate statistics
                $this->calculateStatistics($allBranches);

                // Filter branches based on showInactiveBranches toggle
                if ($this->showInactiveBranches) {
                    $this->branches = $allBranches->toArray();
                } else {
                    $this->branches = $allBranches->where('status', 'active')->values()->toArray();
                }
            } else {
                $this->branches = [];
                $this->resetStatistics();
            }
        } catch (\Exception $e) {
            $this->branches = [];
            $this->resetStatistics();
            session()->flash('error', 'Failed to load branches: ' . $e->getMessage());
        }
    }

    public function calculateStatistics($branches)
    {
        $this->totalBranches = $branches->count();
        $this->activeBranches = $branches->where('status', 'active')->count();
        $this->inactiveBranches = $branches->where('status', '!=', 'active')->count();

        // Calculate enabled/disabled registrations (only for active branches)
        $activeBranchesCollection = $branches->where('status', 'active');
        $this->enabledRegistrations = $activeBranchesCollection->where('registration_enabled', true)->count();
        $this->disabledRegistrations = $activeBranchesCollection->where('registration_enabled', false)->count();
    }

    public function resetStatistics()
    {
        $this->totalBranches = 0;
        $this->activeBranches = 0;
        $this->inactiveBranches = 0;
        $this->enabledRegistrations = 0;
        $this->disabledRegistrations = 0;
    }

    public function toggleInactiveBranches()
    {
        $this->showInactiveBranches = !$this->showInactiveBranches;
        $this->loadBranches();
    }

    public function openCreateBranchModal()
    {
        $this->resetBranchForm();
        $this->dispatch('show-modal', modalId: 'createBranch');
    }

    public function closeCreateBranchModal()
    {
        $this->resetBranchForm();
        $this->dispatch('close-modal', modalId: 'createBranch');
    }

    #[On('escape-pressed')]
    public function handleEscape()
    {
        if ($this->showCreateBranchModal) {
            $this->closeCreateBranchModal();
        }
    }

    public function resetBranchForm()
    {
        $this->branch_name = '';
        $this->organization_name = '';
        $this->business_registration_no = '';
        $this->business_registration_document = null;
        $this->acs_bank_names_id = '';
        $this->account_name = '';
        $this->account_number = '';
        $this->resetValidation();
    }

    public function createBranch()
    {
        // Load current payment settings
        $this->loadPaymentSettings();

        // Base validation rules
        $rules = [
            'branch_name' => 'required|string|max:255',
            'organization_name' => 'required|string|max:255',
            'business_registration_no' => 'required|string|max:255',
            'business_registration_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
        ];

        // Add bank validation rules only for individual_branch payment method
        if ($this->paymentMethod === 'individual_branch') {
            $rules['acs_bank_names_id'] = 'required|exists:acs_bank_names,id';
            $rules['account_name'] = 'required|string|max:255';
            $rules['account_number'] = 'required|string|max:50';
        }

        $this->validate($rules);

        // Custom validation for unique branch name within the same cooperative
        $user = Auth::user();
        if ($user && $user->acs_coorperative_id) {
            $existingBranch = DB::table('acs_coorperative_branch')
                ->where('acs_coorperative_id', $user->acs_coorperative_id)
                ->where('name', $this->branch_name)
                ->whereNull('deleted_at')
                ->first();

            if ($existingBranch) {
                $this->addError('branch_name', 'A branch with this name already exists in your cooperative.');
                return;
            }
        }

        try {
            DB::beginTransaction();

            if (!$user || !$user->acs_coorperative_id) {
                throw new \Exception('User not associated with any cooperative');
            }

            $registrationToken = Str::uuid();
            $documentPath = null;

            // Handle file upload
            if ($this->business_registration_document) {
                $documentPath = $this->business_registration_document->store('branch-documents', 'public');
            }

            $branchData = [
                'acs_coorperative_id' => $user->acs_coorperative_id,
                'name' => $this->branch_name,
                'organization_name' => $this->organization_name,
                'business_registration_no' => $this->business_registration_no,
                'business_registration_document' => $documentPath,
                'status' => 'pending',
                'registration_token' => $registrationToken,
                'created_by' => $user->id,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Add bank information only for individual_branch payment method
            if ($this->paymentMethod === 'individual_branch') {
                $branchData['acs_bank_names_id'] = $this->acs_bank_names_id;
                $branchData['account_name'] = $this->account_name;
                $branchData['account_number'] = $this->account_number;
            }

            $branchId = DB::table('acs_coorperative_branch')->insertGetId($branchData);

            // Send notification to admins
            $notificationData = [
                'id' => $branchId,
                'name' => $this->branch_name,
                'organization_name' => $this->organization_name,
                'business_registration_no' => $this->business_registration_no,
            ];
            $this->notificationService->sendBranchCreatedNotification($user, $notificationData);

            DB::commit();

            $this->loadBranches();
            $this->closeCreateBranchModal();
            session()->flash('success', 'Branch created successfully!');
            $this->dispatch('show-success-alert', message: 'Branch created successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to create branch: ' . $e->getMessage());
            $this->dispatch('show-error-alert', message: 'Failed to create branch: ' . $e->getMessage());
        }
    }

    public function editBranch($branchId)
    {
        try {
            $branch = DB::table('acs_coorperative_branch')
                ->where('id', $branchId)
                ->where('status', 'pending')
                ->first();

            if (!$branch) {
                throw new \Exception('Branch not found or cannot be edited');
            }

            $this->editing_branch_id = $branchId;
            $this->edit_branch_name = $branch->name;
            $this->edit_organization_name = $branch->organization_name ?? '';
            $this->edit_business_registration_no = $branch->business_registration_no;
            $this->edit_acs_bank_names_id = $branch->acs_bank_names_id ?? '';
            $this->edit_account_name = $branch->account_name ?? '';
            $this->edit_account_number = $branch->account_number ?? '';
            $this->showEditBranchModal = true;
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to load branch: ' . $e->getMessage());
            $this->dispatch('show-error-alert', message: 'Failed to load branch: ' . $e->getMessage());
        }
    }

    public function closeEditBranchModal()
    {
        $this->showEditBranchModal = false;
        $this->resetEditBranchForm();
        $this->dispatch('close-modal', modalId: 'editBranch');
    }

    public function resetEditBranchForm()
    {
        $this->editing_branch_id = null;
        $this->edit_branch_name = '';
        $this->edit_organization_name = '';
        $this->edit_business_registration_no = '';
        $this->edit_business_registration_document = null;
        $this->edit_acs_bank_names_id = '';
        $this->edit_account_name = '';
        $this->edit_account_number = '';
        $this->resetValidation();
    }

    public function updateBranch()
    {
        // Load current payment settings
        $this->loadPaymentSettings();

        // Base validation rules
        $rules = [
            'edit_branch_name' => 'required|string|max:255',
            'edit_organization_name' => 'required|string|max:255',
            'edit_business_registration_no' => 'required|string|max:255',
            'edit_business_registration_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
        ];

        // Add bank validation rules only for individual_branch payment method
        if ($this->paymentMethod === 'individual_branch') {
            $rules['edit_acs_bank_names_id'] = 'required|exists:acs_bank_names,id';
            $rules['edit_account_name'] = 'required|string|max:255';
            $rules['edit_account_number'] = 'required|string|max:50';
        }

        $this->validate($rules);

        // Custom validation for unique branch name within the same cooperative (excluding the current branch being edited)
        $user = Auth::user();
        if ($user && $user->acs_coorperative_id) {
            $existingBranch = DB::table('acs_coorperative_branch')
                ->where('acs_coorperative_id', $user->acs_coorperative_id)
                ->where('name', $this->edit_branch_name)
                ->where('id', '!=', $this->editing_branch_id) // Exclude the current branch being edited
                ->whereNull('deleted_at')
                ->first();

            if ($existingBranch) {
                $this->addError('edit_branch_name', 'A branch with this name already exists in your cooperative.');
                return;
            }
        }

        try {
            DB::beginTransaction();

            $branch = DB::table('acs_coorperative_branch')
                ->where('id', $this->editing_branch_id)
                ->where('status', 'pending')
                ->first();

            if (!$branch) {
                throw new \Exception('Branch not found or cannot be edited');
            }

            $updateData = [
                'name' => $this->edit_branch_name,
                'organization_name' => $this->edit_organization_name,
                'business_registration_no' => $this->edit_business_registration_no,
                'updated_at' => now(),
            ];

            // Add bank information only for individual_branch payment method
            if ($this->paymentMethod === 'individual_branch') {
                $updateData['acs_bank_names_id'] = $this->edit_acs_bank_names_id;
                $updateData['account_name'] = $this->edit_account_name;
                $updateData['account_number'] = $this->edit_account_number;
            }

            // Handle file upload if provided
            if ($this->edit_business_registration_document) {
                // Delete old file if exists
                if ($branch->business_registration_document) {
                    Storage::disk('public')->delete($branch->business_registration_document);
                }
                $updateData['business_registration_document'] = $this->edit_business_registration_document->store('branch-documents', 'public');
            }

            DB::table('acs_coorperative_branch')
                ->where('id', $this->editing_branch_id)
                ->update($updateData);

            DB::commit();

            $this->loadBranches();
            $this->closeEditBranchModal();
            session()->flash('success', 'Branch updated successfully!');
            $this->dispatch('show-success-alert', message: 'Branch updated successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to update branch: ' . $e->getMessage());
            $this->dispatch('show-error-alert', message: 'Failed to update branch: ' . $e->getMessage());
        }
    }

    public function confirmDeleteBranch($branchId)
    {
        $this->deleting_branch_id = $branchId;
        $this->dispatch('show-modal', modalId: 'deleteBranchConfirmation');
    }

    public function deleteBranch()
    {
        try {
            DB::beginTransaction();

            $branch = DB::table('acs_coorperative_branch')
                ->where('id', $this->deleting_branch_id)
                ->where('status', 'pending')
                ->first();

            if (!$branch) {
                throw new \Exception('Branch not found or cannot be deleted');
            }

            DB::table('acs_coorperative_branch')
                ->where('id', $this->deleting_branch_id)
                ->update([
                    'deleted_at' => now(),
                ]);

            DB::commit();

            $this->loadBranches();
            $this->dispatch('close-modal', modalId: 'deleteBranchConfirmation');
            $this->deleting_branch_id = null;
            session()->flash('success', 'Branch deleted successfully!');
            $this->dispatch('show-success-alert', message: 'Branch deleted successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to delete branch: ' . $e->getMessage());
            $this->dispatch('show-error-alert', message: 'Failed to delete branch: ' . $e->getMessage());
        }
    }

    public function viewBranch($branchId)
    {
        $branch = collect($this->branches)->firstWhere('id', $branchId);
        $this->viewingBranch = $branch;
        $this->dispatch('show-modal', modalId: 'viewBranchModal');
    }

    public function closeViewBranchModal()
    {
        $this->viewingBranch = null;
        $this->dispatch('close-modal', modalId: 'viewBranchModal');
    }

    public function toggleRegistrationStatus($branchId)
    {
        try {
            DB::beginTransaction();

            $branch = DB::table('acs_coorperative_branch')
                ->where('id', $branchId)
                ->where('status', 'active')
                ->first();

            if (!$branch) {
                throw new \Exception('Branch not found or not active');
            }

            $newStatus = !$branch->registration_enabled;

            DB::table('acs_coorperative_branch')
                ->where('id', $branchId)
                ->update([
                    'registration_enabled' => $newStatus,
                    'updated_at' => now(),
                ]);

            DB::commit();

            $this->loadBranches();

            // Update the viewing branch if it's the same one
            if ($this->viewingBranch && $this->viewingBranch->id == $branchId) {
                $this->viewingBranch = collect($this->branches)->firstWhere('id', $branchId);
            }

            $statusText = $newStatus ? 'enabled' : 'disabled';
            session()->flash('success', "Registration has been {$statusText} for this branch!");
            $this->dispatch('show-success-alert', message: "Registration has been {$statusText} for this branch!");
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to update registration status: ' . $e->getMessage());
            $this->dispatch('show-error-alert', message: 'Failed to update registration status: ' . $e->getMessage());
        }
    }

    public function toggleAllBranches($enable = true)
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();
            if (!$user || !$user->acs_coorperative_id) {
                throw new \Exception('User not associated with any cooperative');
            }

            DB::table('acs_coorperative_branch')
                ->where('acs_coorperative_id', $user->acs_coorperative_id)
                ->where('created_by', $user->id)
                ->where('status', 'active')
                ->update([
                    'registration_enabled' => $enable,
                    'updated_at' => now(),
                ]);

            DB::commit();

            $this->loadBranches();

            $statusText = $enable ? 'enabled' : 'disabled';
            session()->flash('success', "All branches have been {$statusText}!");
            $this->dispatch('show-success-alert', message: "All branches have been {$statusText}!");
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to update all branches: ' . $e->getMessage());
            $this->dispatch('show-error-alert', message: 'Failed to update all branches: ' . $e->getMessage());
        }
    }
}
