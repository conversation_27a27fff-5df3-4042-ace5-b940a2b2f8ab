<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use Livewire\Attributes\Rule;
use App\Services\AcsTermsConditionService;
use App\Models\AcsTermsCondition;
use App\Models\AcsRole;
use App\Models\User;
use App\Models\AcsAgreementEmailNotification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

#[Layout('layouts.admin')]
#[Title('Agreement Management')]
class AgreementManagement extends Component
{
    use WithPagination, WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    // Filter properties
    public $search = '';
    public $roleFilter = '';
    public $statusFilter = '';
    public $perPage = 10;
    public $showFilters = false;

    // Modal states (handled by JavaScript events)
    // Form properties
    public $editingId = null;
    public $deletingId = null;
    public $viewingId = null;

    // Form fields
    #[Rule('required|string|max:255')]
    public $title = '';

    #[Rule('required|exists:acs_roles,id')]
    public $acs_role_id = '';

    #[Rule('nullable|string|max:500')]
    public $content = '';

    #[Rule('nullable|file|mimes:pdf|max:10240')] // Max 10MB
    public $file_upload = null;

    #[Rule('required|in:0,1')]
    public $status = 1;

    // Main agent selection
    public $select_main_agent = '1';

    // Available data
    public $availableRoles = [];
    public $currentFile = null;
    public $removeFile = false;

    protected $messages = [
        'title.required' => 'Title is required.',
        'title.max' => 'Title must not exceed 255 characters.',
        'acs_role_id.required' => 'Role is required.',
        'acs_role_id.exists' => 'Selected role is invalid.',
        'content.max' => 'Content must not exceed 500 characters.',
        'file_upload.file' => 'Please select a valid file.',
        'file_upload.mimes' => 'File must be a PDF.',
        'file_upload.max' => 'File must not exceed 10MB.',
        'status.required' => 'Status is required.',
        'status.in' => 'Status must be Active or Inactive.',
    ];

    protected AcsTermsConditionService $termsConditionService;

    public function boot(AcsTermsConditionService $termsConditionService)
    {
        $this->termsConditionService = $termsConditionService;
    }

    public function mount()
    {
        $this->loadAvailableRoles();
    }

    public function loadAvailableRoles()
    {
        $this->availableRoles = $this->termsConditionService->getAvailableRoles();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->roleFilter = '';
        $this->statusFilter = '';
        $this->resetPage();
    }


    public function closeDeleteModal()
    {
        $this->deletingId = null;
    }

    // Modal management functions
    public function openEditModal($id)
    {
        $agreement = $this->termsConditionService->findOne($id);
        if (!$agreement) {
            session()->flash('error', 'Agreement not found.');
            return;
        }

        $this->editingId = $id;
        $this->title = $agreement->title;
        $this->acs_role_id = $agreement->acs_role_id;
        $this->content = $agreement->content;
        $this->status = $agreement->status;
        $this->currentFile = $agreement->file_path;
        $this->removeFile = false;
        $this->select_main_agent = $agreement->main_agent_type === 'specific' ? '2' : '1';
    }

    public function closeEditModal()
    {
        $this->resetForm();
    }

    public function openViewModal($id)
    {
        $this->viewingId = $id;
    }

    public function closeViewModal()
    {
        $this->viewingId = null;
    }

    public function openDeleteModal($id)
    {
        $this->deletingId = $id;
    }

    public function closeCreateModal()
    {
        $this->resetForm();
    }

    public function removeCurrentFile()
    {
        $this->currentFile = null;
        $this->removeFile = true;
    }

    /**
     * Check if the current role is main agent
     */
    private function isMainAgentRole(): bool
    {
        $mainAgentRole = AcsRole::where('name', 'main-agent')->first();
        return $mainAgentRole && $this->acs_role_id == $mainAgentRole->id;
    }

    // CRUD operations
    public function store()
    {
        $this->validate();

        try {
            // First, deactivate other agreements with the same role
            AcsTermsCondition::where('acs_role_id', $this->acs_role_id)
                ->where('status', 1)
                ->update(['status' => 0]);

            $isMainAgentRole = $this->isMainAgentRole();

            $data = [
                'title' => $this->title,
                'acs_role_id' => $this->acs_role_id,
                'main_agent_type' => $isMainAgentRole && $this->select_main_agent == '2' ? 'specific' : 'all',
                'content' => $this->content,
                'status' => $this->status,
            ];

            $result = $this->termsConditionService->store($data, $this->file_upload);

            if ($result['success']) {
                $this->dispatch('close-modal');
                $this->resetForm();

                // If this is for specific main agent, redirect to email management page
                if ($isMainAgentRole && $this->select_main_agent == '2') {
                    session()->flash('success', 'Agreement created successfully. Please add main agent emails and send notifications.');
                    $redirectUrl = route('admin.specific-main-agent-agreement', ['agreement_id' => $result['data']->id]);
                    Log::info('Redirecting to email management page: ' . $redirectUrl);
                    return $this->redirect($redirectUrl);
                } else {
                    // If this is for all main agents, automatically send emails to all main agents
                    if ($isMainAgentRole && $data['main_agent_type'] == 'all') {
                        $this->sendEmailsToAllMainAgents($result['data']);
                        session()->flash('success', 'Agreement created successfully and emails sent to all main agents.');
                    } else {
                        session()->flash('success', $result['message']);
                    }
                }
            } else {
                session()->flash('error', $result['message']);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Error creating agreement: ' . $e->getMessage());
        }
    }

    /**
     * Send emails to all main agents for a new agreement
     */
    private function sendEmailsToAllMainAgents($agreement)
    {
        try {
            // Get all active main agents
            $mainAgentRole = AcsRole::where('name', 'main-agent')->first();
            if (!$mainAgentRole) {
                Log::error('Main agent role not found');
                return;
            }

            $mainAgents = User::where('acs_role_id', $mainAgentRole->id)
                ->where('status', 'active')
                ->get();

            if ($mainAgents->isEmpty()) {
                Log::info('No active main agents found to send emails to');
                return;
            }

            $successCount = 0;
            $failCount = 0;

            foreach ($mainAgents as $mainAgent) {
                try {
                    // Create email notification record
                    $notification = AcsAgreementEmailNotification::create([
                        'terms_conditions_id' => $agreement->id,
                        'email' => $mainAgent->email,
                        'main_agent_name' => $mainAgent->name,
                        'main_agent_id' => $mainAgent->id,
                        'status' => AcsAgreementEmailNotification::STATUS_PENDING,
                    ]);

                    // Send the email
                    $this->sendAgreementEmail($notification, $agreement, $mainAgent);

                    // Mark as sent
                    $notification->markAsSent();
                    $successCount++;

                    Log::info('Agreement email sent successfully to: ' . $mainAgent->email);
                } catch (\Exception $e) {
                    $failCount++;
                    Log::error('Failed to send agreement email to ' . $mainAgent->email . ': ' . $e->getMessage());

                    // Mark as failed if notification was created
                    if (isset($notification)) {
                        $notification->markAsFailed($e->getMessage());
                    }
                }
            }

            Log::info("Agreement email sending completed. Success: {$successCount}, Failed: {$failCount}");
        } catch (\Exception $e) {
            Log::error('Error in sendEmailsToAllMainAgents: ' . $e->getMessage());
        }
    }

    /**
     * Send agreement email to a specific main agent
     */
    private function sendAgreementEmail($notification, $agreement, $mainAgent)
    {
        // Email data
        $emailData = [
            'mainAgent' => $mainAgent,
            'agreement' => $agreement,
            'fileUrl' => $agreement->file_path ? Storage::url($agreement->file_path) : null,
        ];

        Mail::send('emails.new-agreement-notification', $emailData, function ($message) use ($notification, $agreement) {
            $message->to($notification->email, $notification->main_agent_name ?: $notification->email)
                    ->subject('New Agreement Available: ' . $agreement->title);

            // Attach PDF if exists
            if ($agreement->file_path && Storage::exists($agreement->file_path)) {
                $message->attach(Storage::path($agreement->file_path));
            }
        });
    }


    public function update()
    {
        $this->validate();
        try {
            // Deactivate other active agreements for this role (exclude current editing ID)
            AcsTermsCondition::where('acs_role_id', $this->acs_role_id)
                ->where('status', 1)
                ->where('id', '!=', $this->editingId)
                ->update(['status' => 0]);

            $data = [
                'title' => $this->title,
                'acs_role_id' => $this->acs_role_id,
                'content' => $this->content,
                'status' => $this->status,
            ];

            $result = $this->termsConditionService->update($this->editingId, $data, $this->file_upload, $this->removeFile);

            if ($result['success']) {
                $this->resetForm();
                session()->flash('success', $result['message']);
                $this->dispatch('close-modal');
            } else {
                session()->flash('error', $result['message']);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Error updating agreement: ' . $e->getMessage());
        }
    }


    public function delete()
    {
        if (!$this->deletingId) {
            session()->flash('error', 'No agreement selected for deletion.');
            return;
        }

        try {
            $result = $this->termsConditionService->delete($this->deletingId);

            if ($result['success']) {
                $this->resetForm();
                session()->flash('success', $result['message']);
                $this->dispatch('close-modal');
            } else {
                session()->flash('error', $result['message']);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Error deleting agreement: ' . $e->getMessage());
        }
    }

    public function closeModal()
    {
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->title = '';
        $this->acs_role_id = '';
        $this->content = '';
        $this->status = 1;
        $this->file_upload = null;
        $this->currentFile = null;
        $this->removeFile = false;
        $this->editingId = null;
        $this->deletingId = null;
        $this->viewingId = null;
        $this->select_main_agent = '1';
        $this->resetErrorBag();
    }

    public function downloadFile($filePath)
    {
        if (!$filePath || !Storage::disk('public')->exists($filePath)) {
            session()->flash('error', 'File not found.');
            return;
        }

        return response()->download(Storage::disk('public')->path($filePath));
    }

    public function goToEmailManagement($agreementId)
    {
        return $this->redirect(route('admin.specific-main-agent-agreement', ['agreement_id' => $agreementId]));
    }

    public function render()
    {
        $agreements = $this->termsConditionService->getPaginated(
            $this->search,
            $this->roleFilter,
            $this->statusFilter,
            $this->perPage
        );

        $statistics = $this->termsConditionService->getStatistics();

        $viewingAgreement = null;
        if ($this->viewingId) {
            $viewingAgreement = $this->termsConditionService->findOne($this->viewingId);
        }

        return view('livewire.admin.agreement-management', [
            'agreements' => $agreements,
            'statistics' => $statistics,
            'viewingAgreement' => $viewingAgreement,
        ]);
    }
}
