<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Rule;
use Livewire\Attributes\On;
use Livewire\WithPagination;
use App\Services\AcsUserService;
use App\Models\AcsCooperative;
use App\Models\User;
use App\Models\AcsUsersDetail;
use App\Models\AcsUsersStatusHistory;
use App\Notifications\MainAgentRegistrationNotification;
use App\Notifications\MainAgentKycApprovalNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Auth;

class RegisterMainAgent extends Component
{
    use WithPagination;
    #[Rule('required|string|max:255')]
    public $name = '';

    #[Rule('required|email|unique:acs_users,email')]
    public $email = '';

    #[Rule('nullable|exists:acs_coorperative,id')]
    public $acs_coorperative_id = '';

    public $showModal = false;
    public $showDetailModal = false;
    public $showKycModal = false;
    public $cooperatives = [];
    public $registrationLink = '';
    public $showLinkCopy = false;
    public $selectedAgent = null;
    public $kycAction = '';
    public $kycReason = '';
    public $showKycTab = false;

    // Filter and search properties
    public $showFilters = false;
    public $search = '';
    public $statusFilter = '';
    public $kycStatusFilter = '';
    public $perPage = 10;
    public $sortBy = 'name';
    public $sortOrder = 'asc';

    // Tab properties
    public $activeTab = 'pending_activation';

    public function mount()
    {
        $this->cooperatives = AcsCooperative::all();
    }

    private function getMainAgentRoleId()
    {
        $mainAgentRole = \App\Models\AcsRole::where('name', 'main-agent')->first();
        if (!$mainAgentRole) {
            throw new \Exception('Main agent role not found in the system.');
        }
        return $mainAgentRole->id;
    }

    public function render()
    {
        $query = DB::table('acs_users')
            ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
            ->leftJoin('acs_coorperative_branch', 'acs_users.acs_coorperative_branch_id', '=', 'acs_coorperative_branch.id')
            ->leftJoin('acs_users_details', 'acs_users.id', '=', 'acs_users_details.acs_users_id')
            ->select(
                'acs_users.*',
                'acs_coorperative.name as cooperative_name',
                'acs_coorperative_branch.name as branch_name',
                'acs_users_details.status as kyc_status',
                'acs_users_details.kyc_approve_at'
            )
            ->where('acs_users.acs_role_id', $this->getMainAgentRoleId());

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('acs_users.name', 'like', '%' . $this->search . '%')
                    ->orWhere('acs_users.email', 'like', '%' . $this->search . '%');
            });
        }

        // Apply status filter
        if ($this->statusFilter) {
            $query->where('acs_users.status', $this->statusFilter);
        }

        // Apply KYC status filter
        if ($this->kycStatusFilter) {
            if ($this->kycStatusFilter === 'no_kyc') {
                $query->whereNull('acs_users_details.status');
            } else {
                $query->where('acs_users_details.status', $this->kycStatusFilter);
            }
        }

        // Apply tab filter
        $this->applyTabFilter($query);

        // Apply sorting
        $sortField = $this->sortBy;
        if ($this->sortBy === 'kyc_status') {
            $sortField = 'acs_users_details.status';
        } elseif ($this->sortBy === 'created_at') {
            $sortField = 'acs_users.created_at';
        } else {
            $sortField = 'acs_users.' . $this->sortBy;
        }

        $agents = $query->orderBy($sortField, $this->sortOrder)
            ->paginate($this->perPage);

        return view('livewire.admin.register-main-agent', compact('agents'));
    }

    public function applyTabFilter($query)
    {
        switch ($this->activeTab) {
            case 'active_approved':
                $query->where('acs_users.status', 'active')
                    ->where('acs_users_details.status', 'approved');
                break;

            case 'pending_activation':
                $query->where('acs_users.status', 'pending');
                break;

            case 'invitation_sent':
                $query->where('acs_users.status', 'invitation_sent');
                break;

            case 'pending_kyc':
                $query->where('acs_users.status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('acs_users_details.status')
                            ->orWhereIn('acs_users_details.status', ['pending', 'rejected']);
                    });
                break;

            case 'all':
            default:
                // No additional filter for 'all' tab
                break;
        }
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage(); // Reset pagination when changing tabs

        // Clear existing filters when switching tabs
        $this->search = '';
        $this->statusFilter = '';
        $this->kycStatusFilter = '';
    }

    public function getAllAgentsCount()
    {
        return DB::table('acs_users')
            ->where('acs_users.acs_role_id', $this->getMainAgentRoleId())
            ->count();
    }

    public function getActiveApprovedCount()
    {
        return DB::table('acs_users')
            ->leftJoin('acs_users_details', 'acs_users.id', '=', 'acs_users_details.acs_users_id')
            ->where('acs_users.acs_role_id', $this->getMainAgentRoleId())
            ->where('acs_users.status', 'active')
            ->where('acs_users_details.status', 'approved')
            ->count();
    }

    public function getPendingActivationCount()
    {
        return DB::table('acs_users')
            ->where('acs_users.acs_role_id', $this->getMainAgentRoleId())
            ->where('acs_users.status', 'pending')
            ->count();
    }

    public function getInvitationSentCount()
    {
        return DB::table('acs_users')
            ->where('acs_users.acs_role_id', $this->getMainAgentRoleId())
            ->where('acs_users.status', 'invitation_sent')
            ->count();
    }

    public function getPendingKycCount()
    {
        return DB::table('acs_users')
            ->leftJoin('acs_users_details', 'acs_users.id', '=', 'acs_users_details.acs_users_id')
            ->where('acs_users.acs_role_id', $this->getMainAgentRoleId())
            ->where('acs_users.status', 'active')
            ->where(function ($q) {
                $q->whereNull('acs_users_details.status')
                    ->orWhereIn('acs_users_details.status', ['pending', 'rejected']);
            })
            ->count();
    }

    public function openModal()
    {
        $this->showModal = true;
        $this->resetForm();
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    #[On('escape-pressed')]
    public function handleEscape()
    {
        if ($this->showModal) {
            $this->closeModal();
        } elseif ($this->showDetailModal) {
            $this->closeDetailModal();
        }
    }

    public function openDetailModal($agentId)
    {
        $this->selectedAgent = DB::table('acs_users')
            ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
            ->leftJoin('acs_coorperative_branch', 'acs_users.acs_coorperative_branch_id', '=', 'acs_coorperative_branch.id')
            ->leftJoin('acs_users_details', 'acs_users.id', '=', 'acs_users_details.acs_users_id')
            ->select(
                'acs_users.*',
                'acs_coorperative.name as cooperative_name',
                'acs_coorperative_branch.name as branch_name',
                'acs_users_details.status as kyc_status',
                'acs_users_details.kyc_approve_at'
            )
            ->where('acs_users.id', $agentId)
            ->first();

        $this->showDetailModal = true;
        $this->showKycTab = false; // Reset to first tab

        // Generate registration link if agent is pending
        if ($this->selectedAgent && ($this->selectedAgent->status === 'pending' || $this->selectedAgent->status === 'invitation_sent')) {
            $this->generateRegistrationLinkForAgent($this->selectedAgent->email);
        }
    }

    public function closeDetailModal()
    {
        $this->showDetailModal = false;
        $this->selectedAgent = null;
        $this->registrationLink = '';
    }

    private function generateRegistrationLinkForAgent($email)
    {
        try {
            // Check if password reset token exists
            $existingToken = DB::table('password_reset_tokens')
                ->where('email', $email)
                ->first();

            if ($existingToken) {
                // Use existing token to regenerate link
                $tokenRecord = DB::table('password_reset_tokens')
                    ->where('email', $email)
                    ->first();

                if ($tokenRecord) {
                    // Generate a new token for the link
                    $token = Str::random(64);

                    // Update the existing token
                    DB::table('password_reset_tokens')
                        ->where('email', $email)
                        ->update([
                            'token' => Hash::make($token),
                            'created_at' => now()
                        ]);

                    // Generate the registration link (simple URL with only token)
                    $this->registrationLink = route('agent.password.create') . '?token=' . $token;
                }
            }
        } catch (\Exception $e) {
            // If there's an error generating the link, just don't show it
            $this->registrationLink = '';
        }
    }

    public function resetForm()
    {
        $this->name = '';
        $this->email = '';
        $this->acs_coorperative_id = '';
        $this->showLinkCopy = false;
        $this->registrationLink = '';
        $this->resetValidation();
    }

    public function registerAgent()
    {
        $this->validate();

        try {
            DB::beginTransaction();

            // Get the main-agent role
            $mainAgentRole = \App\Models\AcsRole::where('name', 'main-agent')->first();
            if (!$mainAgentRole) {
                throw new \Exception('Main agent role not found in the system.');
            }

            // Create the user with temporary password
            $user = User::create([
                'name' => $this->name,
                'email' => $this->email,
                'username' => $this->email, // Using email as username
                'phone' => '', // Will be filled later
                'password' => Hash::make(Str::random(32)), // Temporary password
                'acs_role_id' => $mainAgentRole->id, // main-agent role
                'invited_by' => Auth::id(), // Set the inviter to the current admin
                'acs_coorperative_id' => $this->acs_coorperative_id ?: null,
                'status' => User::STATUS_PENDING, // Set as pending until password is created
            ]);

            // Create password reset token
            $token = Str::random(64);

            DB::table('password_reset_tokens')->updateOrInsert(
                ['email' => $this->email],
                [
                    'token' => Hash::make($token),
                    'created_at' => now()
                ]
            );

            // Generate registration link (simple URL with only token)
            $this->registrationLink = route('agent.password.create') . '?token=' . $token;

            DB::commit();

            session()->flash('success', 'Main agent registered successfully!');
            $this->showLinkCopy = true;
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to register main agent: ' . $e->getMessage());
        }
    }

    public function sendEmail()
    {
        if ($this->registrationLink) {
            try {
                // Find the user
                $user = User::where('email', $this->email)->first();

                if ($user) {
                    // Extract token from the registration link URL
                    $token = $this->extractTokenFromUrl($this->registrationLink);

                    // Send notification
                    $user->notify(new MainAgentRegistrationNotification($user, $token));

                    // Update user status to indicate invitation sent
                    $user->update(['status' => User::STATUS_INVITATION_SENT]);

                    session()->flash('success', 'Registration email invitation sent successfully to ' . $this->email . '!');

                    // Reset form and close modal after successful email send
                    $this->resetForm();
                    $this->closeModal();

                    // Dispatch browser event to show success alert
                    $this->dispatch('show-success-alert', message: 'Registration email invitation sent successfully!');
                } else {
                    session()->flash('error', 'User not found.');
                }
            } catch (\Exception $e) {
                session()->flash('error', 'Failed to send email: ' . $e->getMessage());

                // Dispatch browser event to show error alert
                $this->dispatch('show-error-alert', message: 'Failed to send email: ' . $e->getMessage());
            }
        } else {
            session()->flash('error', 'Registration link not available.');
            $this->dispatch('show-error-alert', message: 'Registration link not available.');
        }
    }

    private function extractTokenFromUrl($url)
    {
        $parsedUrl = parse_url($url);
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $queryParams);
            return $queryParams['token'] ?? null;
        }
        return null;
    }

    public function copyLink()
    {
        // Copy the link and show success message
        session()->flash('copy_success', 'Link copied to clipboard!');
        $this->dispatch('copy-to-clipboard', $this->registrationLink);
    }

    public function resendEmailForAgent()
    {
        if ($this->selectedAgent && $this->registrationLink) {
            try {
                // Find the user
                $user = User::where('email', $this->selectedAgent->email)->first();

                if ($user) {
                    // Extract token from the registration link URL
                    $token = $this->extractTokenFromUrl($this->registrationLink);

                    // Send notification
                    $user->notify(new MainAgentRegistrationNotification($user, $token));

                    // Update user status to indicate invitation sent
                    $user->update(['status' => User::STATUS_INVITATION_SENT]);

                    // Update selected agent status for UI
                    $this->selectedAgent->status = User::STATUS_INVITATION_SENT;

                    session()->flash('success', 'Registration email invitation resent successfully to ' . $this->selectedAgent->email . '!');

                    // Dispatch browser event to show success alert
                    $this->dispatch('show-success-alert', message: 'Registration email invitation resent successfully!');
                } else {
                    session()->flash('error', 'User not found.');
                    $this->dispatch('show-error-alert', message: 'User not found.');
                }
            } catch (\Exception $e) {
                session()->flash('error', 'Failed to resend email: ' . $e->getMessage());
                $this->dispatch('show-error-alert', message: 'Failed to resend email: ' . $e->getMessage());
            }
        } else {
            session()->flash('error', 'Registration link not available.');
            $this->dispatch('show-error-alert', message: 'Registration link not available.');
        }
    }

    public function showKycManagement()
    {
        $this->showKycTab = true;
    }

    public function showAgentDetails()
    {
        $this->showKycTab = false;
    }

    public function openKycModal($action)
    {
        $this->kycAction = $action;
        $this->kycReason = '';
        $this->showKycModal = true;
        $this->resetValidation();
    }

    public function closeKycModal()
    {
        $this->showKycModal = false;
        $this->kycAction = '';
        $this->kycReason = '';
        $this->resetValidation();
    }

    public function processKycAction()
    {
        // Only require reason for rejection, not for approval
        $rules = [];
        if ($this->kycAction === 'rejected') {
            $rules['kycReason'] = 'required|string|max:1000';
        } else {
            $rules['kycReason'] = 'nullable|string|max:1000';
        }

        $this->validate($rules);

        if (!$this->selectedAgent || !in_array($this->kycAction, ['approved', 'rejected'])) {
            session()->flash('error', 'Invalid action or agent not selected.');
            return;
        }

        try {
            DB::beginTransaction();

            // Access the id property correctly from stdClass object
            $agentId = is_object($this->selectedAgent) ?
                (property_exists($this->selectedAgent, 'id') ? $this->selectedAgent->id : null) : (isset($this->selectedAgent['id']) ? $this->selectedAgent['id'] : null);

            if (!$agentId) {
                throw new \Exception('Agent ID not found.');
            }

            $user = User::find($agentId);
            if (!$user) {
                throw new \Exception('User not found.');
            }

            // Get or create user details
            $userDetails = $user->userDetails;
            if (!$userDetails) {
                $userDetails = AcsUsersDetail::create([
                    'acs_users_id' => $user->id,
                    'status' => $this->kycAction,
                ]);
            }

            $fromStatus = $userDetails->status;

            // Update user details
            $userDetails->update([
                'status' => $this->kycAction,
                'kyc_approve_at' => $this->kycAction === 'approved' ? now() : null,
            ]);

            // Record status history
            AcsUsersStatusHistory::create([
                'acs_users_id' => $user->id,
                'from_status' => $fromStatus,
                'to_status' => $this->kycAction,
                'reason' => $this->kycReason,
                'action_done_by' => Auth::id(),
            ]);

            DB::commit();

            // Send email notification for KYC status update
            try {
                $user->notify(new MainAgentKycApprovalNotification($user, $this->kycAction, $this->kycReason));

                if ($this->kycAction === 'approved') {
                    session()->flash('success', 'KYC status updated successfully! Email notification sent to main agent.');
                    $this->dispatch('show-success-alert', message: 'KYC status updated successfully! Email notification sent to main agent.');
                } else {
                    session()->flash('success', 'KYC status updated successfully! Email notification sent to main agent.');
                    $this->dispatch('show-success-alert', message: 'KYC status updated successfully! Email notification sent to main agent.');
                }
            } catch (\Exception $e) {
                // Log the error but don't fail the KYC update
                \Illuminate\Support\Facades\Log::error('Failed to send KYC status email notification: ' . $e->getMessage(), [
                    'user_id' => $user->id,
                    'status' => $this->kycAction,
                    'trace' => $e->getTraceAsString()
                ]);

                session()->flash('success', 'KYC status updated successfully! (Email notification failed to send)');
                $this->dispatch('show-success-alert', message: 'KYC status updated successfully! (Email notification failed to send)');
            }

            // Update selected agent for UI
            $this->selectedAgent->kyc_status = $this->kycAction;
            $this->selectedAgent->kyc_approve_at = $this->kycAction === 'approved' ? now()->format('Y-m-d H:i:s') : null;

            $this->closeKycModal();
            $this->closeDetailModal();
        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Failed to update KYC status: ' . $e->getMessage());
            $this->dispatch('show-error-alert', message: 'Failed to update KYC status: ' . $e->getMessage());
        }
    }

    public function getKycStatusHistory()
    {
        if (!$this->selectedAgent) {
            return collect();
        }

        // Convert stdClass to array if needed for access
        $agentId = is_object($this->selectedAgent) ?
            (property_exists($this->selectedAgent, 'id') ? $this->selectedAgent->id : null) : (isset($this->selectedAgent['id']) ? $this->selectedAgent['id'] : null);

        if (!$agentId) {
            return collect();
        }

        return AcsUsersStatusHistory::with('actionBy')
            ->where('acs_users_id', $agentId)
            ->latest()
            ->get();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->statusFilter = '';
        $this->kycStatusFilter = '';
        $this->sortBy = 'name';
        $this->sortOrder = 'asc';
    }

    public function sortByField($field)
    {
        if ($this->sortBy === $field) {
            $this->sortOrder = $this->sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortOrder = 'asc';
        }
    }

    public function resendInvitation($agentId)
    {
        $agent = DB::table('acs_users')->where('id', $agentId)->first();

        if ($agent && ($agent->status === 'pending' || $agent->status === 'invitation_sent')) {
            $this->selectedAgent = $agent;
            $this->generateRegistrationLinkForAgent($agent->email);
            $this->resendEmailForAgent();
        }
    }

    public function confirmSuspend($agentId)
    {
        // This would open a confirmation modal for suspending an agent
        // For now, we'll just show a message
        session()->flash('info', 'Suspend functionality not yet implemented.');
    }
}
