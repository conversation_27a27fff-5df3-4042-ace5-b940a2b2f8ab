<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Title;
use Livewire\Attributes\Layout;
use App\Models\AcsCommissionDistribution;
use App\Models\AcsCooperative;
use App\Models\AcsCooperativeBranch;
use App\Models\User;
use Illuminate\Support\Facades\DB;

#[Layout('layouts.admin')]
#[Title('Commission Distribution Reports')]
class CommissionDistributionReports extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Filter properties
    public $cooperative_id = '';
    public $branch_id = '';
    public $main_agent_id = '';
    public $agent_id = '';
    public $date_from = '';
    public $date_to = '';
    public $min_amount = '';
    public $max_amount = '';

    // Sorting properties
    public $sortBy = 'created_at';
    public $sortOrder = 'desc';

    // Pagination
    public $perPage = 15;

    // Filter panel visibility
    public $showFilters = false;

    // Dropdown data
    public $cooperatives = [];
    public $branches = [];
    public $mainAgents = [];
    public $agents = [];

    public function mount()
    {
        $this->loadDropdownData();
    }

    public function loadDropdownData()
    {
        $this->cooperatives = AcsCooperative::select('id', 'name')->get();
        $this->branches = AcsCooperativeBranch::select('id', 'name', 'acs_coorperative_id')->get();
        $this->mainAgents = User::whereHas('role', function ($query) {
            $query->whereIn('name', ['main-agent', 'sub-main-agent']);
        })->select('id', 'name', 'email')->get();
        $this->agents = User::whereHas('role', function ($query) {
            $query->where('name', 'agent');
        })->select('id', 'name', 'email')->get();
    }

    public function updatedCooperativeId()
    {
        if ($this->cooperative_id) {
            $this->branches = AcsCooperativeBranch::where('acs_coorperative_id', $this->cooperative_id)
                ->select('id', 'name', 'acs_coorperative_id')
                ->get();
        } else {
            $this->branches = AcsCooperativeBranch::select('id', 'name', 'acs_coorperative_id')->get();
        }
        $this->branch_id = '';
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function clearFilters()
    {
        $this->cooperative_id = '';
        $this->branch_id = '';
        $this->main_agent_id = '';
        $this->agent_id = '';
        $this->date_from = '';
        $this->date_to = '';
        $this->min_amount = '';
        $this->max_amount = '';
        $this->loadDropdownData();
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortOrder = $this->sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortOrder = 'asc';
        }
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function getStatsProperty()
    {
        $query = AcsCommissionDistribution::query();

        $this->applyFilters($query);

        return [
            'total_distributions' => $query->count(),
            'total_commission_amount' => $query->sum(DB::raw('main_agent_commission_amount + agent_commission_amount')),
            'total_main_agent_commission' => $query->sum('main_agent_commission_amount'),
            'total_agent_commission' => $query->sum('agent_commission_amount'),
            'avg_commission_amount' => $query->avg(DB::raw('main_agent_commission_amount + agent_commission_amount')) ?? 0,
        ];
    }

    private function applyFilters($query)
    {
        if ($this->cooperative_id) {
            $query->where('acs_coorperative_id', $this->cooperative_id);
        }

        if ($this->branch_id) {
            $query->where('acs_coorperative_branch_id', $this->branch_id);
        }

        if ($this->main_agent_id) {
            $query->where('acs_main_agent_user_id', $this->main_agent_id);
        }

        if ($this->agent_id) {
            $query->where('acs_agent_user_id', $this->agent_id);
        }

        if ($this->date_from) {
            $query->whereDate('created_at', '>=', $this->date_from);
        }

        if ($this->date_to) {
            $query->whereDate('created_at', '<=', $this->date_to);
        }

        if ($this->min_amount) {
            $query->having(DB::raw('main_agent_commission_amount + agent_commission_amount'), '>=', $this->min_amount);
        }

        if ($this->max_amount) {
            $query->having(DB::raw('main_agent_commission_amount + agent_commission_amount'), '<=', $this->max_amount);
        }
    }

    public function render()
    {
        $query = AcsCommissionDistribution::with([
            'cooperative:id,name',
            'cooperativeBranch:id,name,acs_coorperative_id',
            'mainAgent:id,name,email',
            'agent:id,name,email',
            'salesCommission:id'
        ]);

        $this->applyFilters($query);

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortOrder);

        $distributions = $query->paginate($this->perPage);

        return view('livewire.admin.commission-distribution-reports', [
            'distributions' => $distributions,
            'stats' => $this->stats,
        ]);
    }
}
