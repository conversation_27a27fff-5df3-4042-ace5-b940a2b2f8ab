<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use Livewire\Attributes\Rule;
use App\Services\AcsAnnouncementService;
use App\Services\ZeptoMailService;
use App\Models\AcsAnnouncement;
use App\Models\AcsRole;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

// #[Layout('layouts.admin')]
#[Title('Announcement Management')]
class AnnouncementManagement extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Tab management
    public $activeTab = 'announcements';

    // Filter properties
    public $search = '';
    public $roleFilter = '';
    public $statusFilter = '';
    public $perPage = 10;
    public $showFilters = false;

    // Modal states
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showDeleteModal = false;
    public $showViewModal = false;
    public $showEmailModal = false;
    public $showBulkEmailModal = false;
    public $showEmailDeliveryModal = false;

    // Form properties
    public $editingId = null;
    public $deletingId = null;
    public $viewingId = null;
    public $emailingId = null;
    public $emailDeliveryId = null;

    // Form fields
    #[Rule('required|string|max:255')]
    public $title = '';

    #[Rule('required|exists:acs_roles,id')]
    public $acs_role_id = '';

    #[Rule('required|string|max:1000')]
    public $content = '';

    #[Rule('required|in:0,1')]
    public $status = 1;

    // Email modal properties
    public $selectedRoleIds = [];
    public $emailingAnnouncement = null;

    // Zepto API properties
    public $zeptoEmailAddress = '';
    public $zeptoStartDate = '';
    public $zeptoEndDate = '';
    public $zeptoData = null;
    public $zeptoLoading = false;
    public $zeptoError = '';
    public $zeptoStats = null;
    public $zeptoBounces = null;

    // Available data
    public $availableRoles = [];

    protected $messages = [
        'title.required' => 'Title is required.',
        'title.max' => 'Title must not exceed 255 characters.',
        'acs_role_id.required' => 'Role is required.',
        'acs_role_id.exists' => 'Selected role is invalid.',
        'content.required' => 'Content is required.',
        'content.max' => 'Content must not exceed 1000 characters.',
        'status.required' => 'Status is required.',
        'status.in' => 'Status must be Active or Inactive.',
        'selectedRoleIds.required' => 'Please select at least one role to send emails.',
    ];

    protected AcsAnnouncementService $announcementService;
    protected ZeptoMailService $zeptoService;

    public function boot(AcsAnnouncementService $announcementService, ZeptoMailService $zeptoService)
    {
        $this->announcementService = $announcementService;
        $this->zeptoService = $zeptoService;
    }

    public function mount()
    {
        $this->loadAvailableRoles();
        $this->initializeZeptoFilters();
    }

    public function loadAvailableRoles()
    {
        $this->availableRoles = $this->announcementService->getAvailableRoles();
    }

    public function initializeZeptoFilters()
    {
        // Set default date range for Zepto (last 7 days)
        $this->zeptoEndDate = Carbon::now()->format('Y-m-d');
        $this->zeptoStartDate = Carbon::now()->subDays(7)->format('Y-m-d');
    }

    // Tab management methods
    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;

        // Load data for the active tab
        if ($tab === 'zepto') {
            $this->loadZeptoData();
        }
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->roleFilter = '';
        $this->statusFilter = '';
        $this->resetPage();
    }

    // Zepto API methods
    public function loadZeptoData()
    {
        $this->zeptoLoading = true;
        $this->zeptoError = '';

        try {
            // Get email delivery status
            $deliveryResult = $this->zeptoService->getEmailDeliveryStatus(
                $this->zeptoEmailAddress ?: null,
                $this->zeptoStartDate,
                $this->zeptoEndDate
            );

            if ($deliveryResult['success']) {
                $this->zeptoData = $deliveryResult['processed_data'];
            } else {
                $this->zeptoError = $deliveryResult['message'];
            }

            // Get statistics
            $statsResult = $this->zeptoService->getDeliveryStatistics(
                $this->zeptoStartDate,
                $this->zeptoEndDate
            );

            if ($statsResult['success']) {
                $this->zeptoStats = $statsResult['processed_data'];
            }

            // Get bounce reports
            $bounceResult = $this->zeptoService->getBounceReports(
                $this->zeptoStartDate,
                $this->zeptoEndDate
            );

            if ($bounceResult['success']) {
                $this->zeptoBounces = $bounceResult['processed_data'];
            }

        } catch (\Exception $e) {
            $this->zeptoError = 'Error loading Zepto data: ' . $e->getMessage();
        }

        $this->zeptoLoading = false;
    }

    public function refreshZeptoData()
    {
        $this->loadZeptoData();
        session()->flash('success', 'Zepto data refreshed successfully!');
    }

    public function exportZeptoData()
    {
        if (!$this->zeptoData) {
            session()->flash('error', 'No Zepto data available to export.');
            return;
        }

        // Create CSV content
        $csvContent = "Email,Status,Sent At,Delivered At,Subject,Bounce Reason,Error Message\n";

        foreach ($this->zeptoData['emails'] as $email) {
            $csvContent .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $email['email'],
                $email['status'],
                $email['sent_at'] ?? '',
                $email['delivered_at'] ?? '',
                $email['subject'],
                $email['bounce_reason'] ?? '',
                $email['error_message'] ?? ''
            );
        }

        // Return download response
        return response()->streamDownload(function () use ($csvContent) {
            echo $csvContent;
        }, 'zepto_email_report_' . date('Y-m-d') . '.csv', [
            'Content-Type' => 'text/csv',
        ]);
    }

    // Modal management functions
    public function openCreateModal()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function closeCreateModal()
    {
        $this->showCreateModal = false;
        $this->resetForm();
    }

    public function openEditModal($id)
    {
        $announcement = $this->announcementService->findOne($id);
        if (!$announcement) {
            session()->flash('error', 'Announcement not found.');
            return;
        }

        $this->editingId = $id;
        $this->title = $announcement->title;
        $this->acs_role_id = $announcement->acs_role_id;
        $this->content = $announcement->content;
        $this->status = $announcement->status;
        $this->showEditModal = true;
    }

    public function closeEditModal()
    {
        $this->showEditModal = false;
        $this->resetForm();
    }

    public function openViewModal($id)
    {
        $this->viewingId = $id;
        $this->showViewModal = true;
    }

    public function closeViewModal()
    {
        $this->showViewModal = false;
        $this->viewingId = null;
    }

    public function openDeleteModal($id)
    {
        $this->deletingId = $id;
        $this->showDeleteModal = true;
    }

    public function closeDeleteModal()
    {
        $this->showDeleteModal = false;
        $this->deletingId = null;
    }

    public function openEmailModal($id)
    {
        $announcement = $this->announcementService->findOne($id);
        if (!$announcement) {
            session()->flash('error', 'Announcement not found.');
            return;
        }

        $this->emailingId = $id;
        $this->emailingAnnouncement = $announcement;
        $this->selectedRoleIds = [$announcement->acs_role_id]; // Pre-select the announcement's role
        $this->showEmailModal = true;
    }

    public function closeEmailModal()
    {
        $this->showEmailModal = false;
        $this->emailingId = null;
        $this->emailingAnnouncement = null;
        $this->selectedRoleIds = [];
    }

    public function openBulkEmailModal()
    {
        $this->showBulkEmailModal = true;
    }

    public function closeBulkEmailModal()
    {
        $this->showBulkEmailModal = false;
    }

    public function openEmailDeliveryModal($id)
    {
        $announcement = $this->announcementService->findOne($id);
        if (!$announcement || !$announcement->email_sent) {
            session()->flash('error', 'No email delivery information found for this announcement.');
            return;
        }

        $this->emailDeliveryId = $id;
        $this->showEmailDeliveryModal = true;
    }

    public function closeEmailDeliveryModal()
    {
        $this->showEmailDeliveryModal = false;
        $this->emailDeliveryId = null;
    }

    // CRUD operations
    public function store()
    {
        $this->validate();

        try {
            $data = [
                'title' => $this->title,
                'acs_role_id' => $this->acs_role_id,
                'content' => $this->content,
                'status' => $this->status,
            ];

            $result = $this->announcementService->store($data);

            if ($result['success']) {
                $this->closeCreateModal();
                session()->flash('success', $result['message']);
            } else {
                session()->flash('error', $result['message']);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Error creating announcement: ' . $e->getMessage());
        }
    }

    public function update()
    {
        $this->validate();

        try {
            $data = [
                'title' => $this->title,
                'acs_role_id' => $this->acs_role_id,
                'content' => $this->content,
                'status' => $this->status,
            ];

            $result = $this->announcementService->update($this->editingId, $data);

            if ($result['success']) {
                $this->closeEditModal();
                session()->flash('success', $result['message']);
            } else {
                session()->flash('error', $result['message']);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Error updating announcement: ' . $e->getMessage());
        }
    }

    public function delete()
    {
        if (!$this->deletingId) {
            session()->flash('error', 'No announcement selected for deletion.');
            return;
        }

        try {
            $result = $this->announcementService->delete($this->deletingId);

            if ($result['success']) {
                $this->closeDeleteModal();
                session()->flash('success', $result['message']);
            } else {
                session()->flash('error', $result['message']);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Error deleting announcement: ' . $e->getMessage());
        }
    }

    public function sendEmail()
    {
        // Validate that at least one role is selected
        if (empty($this->selectedRoleIds)) {
            session()->flash('error', 'Please select at least one role to send emails.');
            return;
        }

        if (!$this->emailingId) {
            session()->flash('error', 'No announcement selected for email sending.');
            return;
        }

        try {
            $result = $this->announcementService->sendAnnouncementEmail(
                $this->emailingId,
                $this->selectedRoleIds
            );

            if ($result['success']) {
                $this->closeEmailModal();
                session()->flash('success', $result['message']);
            } else {
                session()->flash('error', $result['message']);
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Error sending emails: ' . $e->getMessage());
        }
    }

    public function sendEmailsForActiveAnnouncements()
    {
        try {
            $activeAnnouncements = AcsAnnouncement::where('status', AcsAnnouncement::STATUS_ACTIVE)
                ->where(function($query) {
                    $query->where('email_sent', false)
                          ->orWhereNull('email_sent');
                })
                ->get();

            if ($activeAnnouncements->isEmpty()) {
                session()->flash('error', 'No active announcements found that haven\'t been sent via email.');
                $this->closeBulkEmailModal();
                return;
            }

            $totalSent = 0;
            $totalFailed = 0;
            $processedCount = 0;

            foreach ($activeAnnouncements as $announcement) {
                $result = $this->announcementService->sendAnnouncementEmail($announcement->id);

                if ($result['success']) {
                    $totalSent += $result['sent_count'];
                    $totalFailed += $result['failed_count'];
                    $processedCount++;
                }
            }

            $this->closeBulkEmailModal();

            if ($processedCount > 0) {
                $message = "Processed {$processedCount} announcements. Total emails sent: {$totalSent}";
                if ($totalFailed > 0) {
                    $message .= ". Failed: {$totalFailed}";
                }
                session()->flash('success', $message);
            } else {
                session()->flash('error', 'Failed to process any announcements.');
            }
        } catch (\Exception $e) {
            $this->closeBulkEmailModal();
            session()->flash('error', 'Error sending bulk emails: ' . $e->getMessage());
        }
    }


    private function resetForm()
    {
        $this->title = '';
        $this->acs_role_id = '';
        $this->content = '';
        $this->status = 1;
        $this->editingId = null;
        $this->deletingId = null;
        $this->viewingId = null;
        $this->emailingId = null;
        $this->emailDeliveryId = null;
        $this->selectedRoleIds = [];
        $this->resetErrorBag();
    }

    public function render()
    {
        $announcements = $this->announcementService->getPaginated(
            $this->search,
            $this->roleFilter,
            $this->statusFilter,
            $this->perPage
        );

        $viewingAnnouncement = null;
        if ($this->viewingId) {
            $viewingAnnouncement = $this->announcementService->findOne($this->viewingId);
        }

        $emailDeliveryAnnouncement = null;
        $emailLogs = null;
        if ($this->emailDeliveryId) {
            $emailDeliveryAnnouncement = $this->announcementService->findOne($this->emailDeliveryId);
            if ($emailDeliveryAnnouncement && $emailDeliveryAnnouncement->email_sent) {
                $emailLogs = $this->announcementService->getEmailLogs($this->emailDeliveryId);
            }
        }

        return view('livewire.admin.announcement-management', [
            'announcements' => $announcements,
            'viewingAnnouncement' => $viewingAnnouncement,
            'emailingAnnouncement' => $this->emailingAnnouncement,
            'emailDeliveryAnnouncement' => $emailDeliveryAnnouncement,
            'emailLogs' => $emailLogs,
        ]);
    }
}
