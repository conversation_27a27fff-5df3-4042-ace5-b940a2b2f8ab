<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Rule;
use App\Services\AcsTermsConditionService;
use App\Models\AcsRole;

class AgreementEditModal extends Component
{
    use WithFileUploads;

    public $isOpen = false;
    public $agreementId = null;
    public $currentFile = null;

    protected $listeners = ['open-edit-modal' => 'openModal'];

    // Form fields
    #[Rule('required|string|max:255')]
    public $title = '';

    #[Rule('required|exists:acs_roles,id')]
    public $acs_role_id = '';

    #[Rule('nullable|string|max:500')]
    public $content = '';

    #[Rule('nullable|file|mimes:pdf|max:10240')] // Max 10MB
    public $file_upload = null;

    #[Rule('required|boolean')]
    public $status = true;

    protected $termsConditionService;

    public function boot(AcsTermsConditionService $termsConditionService)
    {
        $this->termsConditionService = $termsConditionService;
    }

    public function openModal($agreementId)
    {
        $this->agreementId = $agreementId;
        $this->loadAgreement();
        $this->isOpen = true;
    }

    public function close()
    {
        $this->isOpen = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->agreementId = null;
        $this->title = '';
        $this->acs_role_id = '';
        $this->content = '';
        $this->file_upload = null;
        $this->status = true;
        $this->currentFile = null;
        $this->resetValidation();
    }

    public function loadAgreement()
    {
        $agreement = $this->termsConditionService->findOne($this->agreementId);
        
        if ($agreement) {
            $this->title = $agreement->title;
            $this->acs_role_id = $agreement->acs_role_id;
            $this->content = $agreement->content ?? '';
            $this->status = $agreement->status;
            $this->currentFile = $agreement->file_path;
        }
    }

    public function removeCurrentFile()
    {
        $this->currentFile = null;
    }

    public function downloadFile($filePath)
    {
        return $this->termsConditionService->downloadFile($filePath);
    }

    public function update()
    {
        $this->validate();

        try {
            $data = [
                'title' => $this->title,
                'acs_role_id' => $this->acs_role_id,
                'content' => $this->content,
                'status' => $this->status,
            ];

            if ($this->file_upload) {
                $data['file_upload'] = $this->file_upload;
            }

            if ($this->currentFile === null) {
                $data['remove_file'] = true;
            }

            $this->termsConditionService->update($this->agreementId, $data);

            session()->flash('success', 'Agreement updated successfully!');
            $this->close();
            $this->dispatch('agreement-updated');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update agreement: ' . $e->getMessage());
        }
    }

    public function render()
    {
        $availableRoles = AcsRole::all();
        
        return view('livewire.admin.agreement-edit-modal', [
            'availableRoles' => $availableRoles,
        ]);
    }
}