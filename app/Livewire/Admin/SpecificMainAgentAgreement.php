<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use Livewire\Attributes\Rule;
use App\Services\AcsTermsConditionService;
use App\Models\AcsTermsCondition;
use App\Models\AcsAgreementEmailNotification;
use App\Models\AcsUser;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

#[Layout('layouts.admin')]
#[Title('Manage Agreement Email Notifications')]
class SpecificMainAgentAgreement extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Agreement ID from route parameter
    public $agreementId;
    public $agreement;

    // Email management
    public $newEmail = '';

    // Select2 search functionality
    public $searchQuery = '';
    public $searchResults = [];
    public $selectedMainAgent = null;
    public $isSearching = false;

    // Available data
    public $availableRoles = [];

    protected $messages = [
        'newEmail.required' => 'Please select a main agent.',
        'newEmail.email' => 'Please select a valid main agent.',
    ];

    protected AcsTermsConditionService $termsConditionService;

    public function boot(AcsTermsConditionService $termsConditionService)
    {
        $this->termsConditionService = $termsConditionService;
    }

    public function mount($agreement_id = null)
    {
        $this->agreementId = $agreement_id;
        $this->loadAgreement();
    }

    public function loadAgreement()
    {
        if ($this->agreementId) {
            $this->agreement = AcsTermsCondition::with(['role', 'emailNotifications.mainAgent'])
                ->find($this->agreementId);

            if (!$this->agreement) {
                session()->flash('error', 'Agreement not found.');
                return redirect()->route('admin.agreement-list');
            }

            if ($this->agreement->main_agent_type !== 'specific') {
                session()->flash('error', 'This agreement is not for specific main agents.');
                return redirect()->route('admin.agreement-list');
            }
        }
    }

    public function clearSearchResults()
    {
        $this->searchResults = [];
        $this->isSearching = false;
    }

    public function clearSearchOnBlur()
    {
        // Small delay to allow for clicks on dropdown items
        $this->dispatch('search-blur');
    }

    public function updatedSearchQuery()
    {
        $this->searchMainAgents();
    }

    public function searchMainAgents()
    {
        if (strlen($this->searchQuery) < 2) {
            $this->searchResults = [];
            $this->isSearching = false;
            return;
        }

        $this->isSearching = true;

        // Get main-agent role ID
        $mainAgentRole = \App\Models\AcsRole::where('name', 'main-agent')->first();

        if (!$mainAgentRole) {
            $this->searchResults = [];
            $this->isSearching = false;
            session()->flash('error', 'Main agent role not found. Please contact administrator.');
            return;
        }

        // Check if there are any main agents at all
        $totalMainAgents = AcsUser::where('acs_role_id', $mainAgentRole->id)->count();
        if ($totalMainAgents === 0) {
            $this->searchResults = [];
            $this->isSearching = false;
            session()->flash('warning', 'No main agents found in the system.');
            return;
        }

        $this->searchResults = AcsUser::where(function ($query) {
                $query->where('email', 'like', '%' . $this->searchQuery . '%')
                      ->orWhere('name', 'like', '%' . $this->searchQuery . '%');
            })
            ->where('acs_role_id', $mainAgentRole->id) // Only main-agent users
            ->where('status', 'active') // Only active users
            ->select('id', 'name', 'email')
            ->limit(10)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'text' => $user->name . ' (' . $user->email . ')',
                    'name' => $user->name,
                    'email' => $user->email,
                ];
            })
            ->toArray();

        $this->isSearching = false;
    }

    public function selectMainAgent($agentId)
    {
        // Check if already added
        if ($this->isMainAgentAlreadyAdded($agentId)) {
            session()->flash('warning', 'This main agent is already added to the notification list.');
            return;
        }

        $user = AcsUser::find($agentId);
        if ($user) {
            $this->selectedMainAgent = $user;
            $this->newEmail = $user->email;
            $this->searchQuery = '';
            $this->searchResults = [];
        }
    }

    public function clearSelection()
    {
        $this->selectedMainAgent = null;
        $this->newEmail = '';
        $this->searchQuery = '';
        $this->searchResults = [];
        $this->isSearching = false;
    }

    public function isMainAgentAlreadyAdded($mainAgentId)
    {
        if (!$this->agreement) {
            return false;
        }

        return AcsAgreementEmailNotification::where('terms_conditions_id', $this->agreement->id)
            ->where('main_agent_id', $mainAgentId)
            ->exists();
    }

    public function addEmail()
    {
        // Check if a main agent is selected
        if (!$this->selectedMainAgent) {
            session()->flash('error', 'Please select a main agent first.');
            return;
        }

        // Check if email already exists for this agreement
        $existingNotification = AcsAgreementEmailNotification::where('terms_conditions_id', $this->agreement->id)
            ->where('email', $this->selectedMainAgent->email)
            ->first();

        if ($existingNotification) {
            session()->flash('error', 'This main agent is already added to the notification list.');
            return;
        }

        // Create email notification record
        AcsAgreementEmailNotification::create([
            'terms_conditions_id' => $this->agreement->id,
            'email' => $this->selectedMainAgent->email,
            'main_agent_name' => $this->selectedMainAgent->name,
            'main_agent_id' => $this->selectedMainAgent->id,
            'status' => AcsAgreementEmailNotification::STATUS_PENDING,
        ]);

        $this->clearSelection();
        session()->flash('success', 'Main agent added successfully.');
    }

    public function removeEmail($notificationId)
    {
        $notification = AcsAgreementEmailNotification::find($notificationId);

        if ($notification && $notification->terms_conditions_id == $this->agreement->id) {
            $notification->delete();
            session()->flash('success', 'Email removed successfully.');
        } else {
            session()->flash('error', 'Email notification not found.');
        }
    }

    public function resendEmail($notificationId)
    {
        $notification = AcsAgreementEmailNotification::with('mainAgent')
            ->where('id', $notificationId)
            ->where('terms_conditions_id', $this->agreement->id)
            ->first();

        if (!$notification) {
            session()->flash('error', 'Email notification not found.');
            return;
        }

        try {
            $this->sendAgreementEmail($notification);
            session()->flash('success', 'Email resent successfully.');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to resend email: ' . $e->getMessage());
        }
    }

    public function sendAllEmails()
    {
        $pendingNotifications = AcsAgreementEmailNotification::with('mainAgent')
            ->where('terms_conditions_id', $this->agreement->id)
            ->where('status', AcsAgreementEmailNotification::STATUS_PENDING)
            ->get();

        if ($pendingNotifications->isEmpty()) {
            session()->flash('warning', 'No pending emails to send.');
            return;
        }

        $successCount = 0;
        $failCount = 0;

        foreach ($pendingNotifications as $notification) {
            try {
                $this->sendAgreementEmail($notification);
                $successCount++;
            } catch (\Exception $e) {
                $notification->markAsFailed($e->getMessage());
                $failCount++;
            }
        }

        if ($successCount > 0) {
            session()->flash('success', "Successfully sent {$successCount} email(s).");
        }

        if ($failCount > 0) {
            session()->flash('error', "Failed to send {$failCount} email(s). Check the failed emails list.");
        }
    }

    private function sendAgreementEmail($notification)
    {
        $mainAgent = $notification->mainAgent;
        $agreement = $this->agreement;

        // Email data
        $emailData = [
            'mainAgent' => $mainAgent ?: (object)['name' => $notification->main_agent_name, 'email' => $notification->email],
            'agreement' => $agreement,
            'fileUrl' => $agreement->file_path ? Storage::url($agreement->file_path) : null,
        ];

        Mail::send('emails.new-agreement-notification', $emailData, function ($message) use ($notification, $agreement) {
            $message->to($notification->email, $notification->main_agent_name ?: $notification->email)
                    ->subject('New Agreement Available: ' . $agreement->title);

            // Attach PDF if exists
            if ($agreement->file_path && Storage::exists($agreement->file_path)) {
                $message->attach(Storage::path($agreement->file_path));
            }
        });

        // Mark as sent
        $notification->markAsSent();

        Log::info('Agreement email sent successfully to: ' . $notification->email);
    }

    public function cancel()
    {
        return redirect()->route('admin.agreement-list');
    }

    public function downloadFile($filePath)
    {
        if (!$filePath || !Storage::disk('public')->exists($filePath)) {
            session()->flash('error', 'File not found.');
            return;
        }

        return response()->download(Storage::disk('public')->path($filePath));
    }

    public function render()
    {
        if (!$this->agreement) {
            return view('livewire.admin.specific-main-agent-agreement');
        }

        $emailNotifications = AcsAgreementEmailNotification::with('mainAgent')
            ->where('terms_conditions_id', $this->agreement->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $statistics = [
            'total' => $emailNotifications->total(),
            'pending' => AcsAgreementEmailNotification::where('terms_conditions_id', $this->agreement->id)
                ->where('status', AcsAgreementEmailNotification::STATUS_PENDING)
                ->count(),
            'sent' => AcsAgreementEmailNotification::where('terms_conditions_id', $this->agreement->id)
                ->where('status', AcsAgreementEmailNotification::STATUS_SENT)
                ->count(),
            'failed' => AcsAgreementEmailNotification::where('terms_conditions_id', $this->agreement->id)
                ->where('status', AcsAgreementEmailNotification::STATUS_FAILED)
                ->count(),
        ];

        return view('livewire.admin.specific-main-agent-agreement', [
            'emailNotifications' => $emailNotifications,
            'statistics' => $statistics,
        ]);
    }
}
