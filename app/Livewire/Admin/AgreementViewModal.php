<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use App\Services\AcsTermsConditionService;

class AgreementViewModal extends Component
{
    public $isOpen = false;
    public $agreement = null;

    protected $listeners = ['open-view-modal' => 'openModal'];

    protected $termsConditionService;

    public function boot(AcsTermsConditionService $termsConditionService)
    {
        $this->termsConditionService = $termsConditionService;
    }

    public function openModal($agreementId)
    {
        $this->agreement = $this->termsConditionService->findOne($agreementId);
        $this->isOpen = true;
    }

    public function close()
    {
        $this->isOpen = false;
        $this->agreement = null;
    }

    public function downloadFile($filePath)
    {
        return $this->termsConditionService->downloadFile($filePath);
    }

    public function render()
    {
        return view('livewire.admin.agreement-view-modal');
    }
}