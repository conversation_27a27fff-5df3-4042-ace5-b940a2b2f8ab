<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use App\Services\AcsCoorperativeService;
use App\Models\AcsCooperative;

// #[Layout('layouts.account.admin')]
#[Title('Cooperative Branches')]
class CooperativeBranches extends Component
{
    use WithPagination;

    public $search = '';
    public $cooperative;
    public $cooperativeId;

    protected AcsCoorperativeService $cooperativeService;

    public function boot(AcsCoorperativeService $cooperativeService)
    {
        $this->cooperativeService = $cooperativeService;
    }

    public function mount($cooperative)
    {
        $this->cooperativeId = $cooperative;
        $this->cooperative = AcsCooperative::findOrFail($cooperative);
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function render()
    {
        $branches = $this->cooperativeService->getCooperativeBranches($this->cooperativeId, $this->search, 10);

        return view('livewire.admin.cooperative-branches', [
            'branches' => $branches,
            'cooperative' => $this->cooperative
        ]);
    }
}
