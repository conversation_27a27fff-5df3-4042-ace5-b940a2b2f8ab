<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Rule;
use App\Services\AcsTermsConditionService;
use App\Models\AcsRole;

class AgreementCreateModal extends Component
{
    use WithFileUploads;

    public $isOpen = false;

    protected $listeners = ['open-create-modal' => 'open'];

    // Form fields
    #[Rule('required|string|max:255')]
    public $title = '';

    #[Rule('required|exists:acs_roles,id')]
    public $acs_role_id = '';

    #[Rule('nullable|string|max:500')]
    public $content = '';

    #[Rule('nullable|file|mimes:pdf|max:10240')] // Max 10MB
    public $file_upload = null;

    #[Rule('required|boolean')]
    public $status = true;

    protected $termsConditionService;

    public function boot(AcsTermsConditionService $termsConditionService)
    {
        $this->termsConditionService = $termsConditionService;
    }

    public function open()
    {
        $this->isOpen = true;
        $this->resetForm();
    }

    public function close()
    {
        $this->isOpen = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->title = '';
        $this->acs_role_id = '';
        $this->content = '';
        $this->file_upload = null;
        $this->status = true;
        $this->resetValidation();
    }

    public function store()
    {
        $this->validate();

        try {
            $data = [
                'title' => $this->title,
                'acs_role_id' => $this->acs_role_id,
                'content' => $this->content,
                'status' => $this->status,
            ];

            if ($this->file_upload) {
                $data['file_upload'] = $this->file_upload;
            }

            $this->termsConditionService->create($data);

            session()->flash('success', 'Agreement created successfully!');
            $this->close();
            $this->dispatch('agreement-created');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to create agreement: ' . $e->getMessage());
        }
    }

    public function render()
    {
        $availableRoles = AcsRole::all();
        
        return view('livewire.admin.agreement-create-modal', [
            'availableRoles' => $availableRoles,
        ]);
    }
}