<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use App\Services\AcsTermsConditionService;

class AgreementDeleteModal extends Component
{
    public $isOpen = false;
    public $agreementId = null;
    public $agreementTitle = '';

    protected $listeners = ['open-delete-modal' => 'openModal'];

    protected $termsConditionService;

    public function boot(AcsTermsConditionService $termsConditionService)
    {
        $this->termsConditionService = $termsConditionService;
    }

    public function openModal($agreementId)
    {
        $this->agreementId = $agreementId;
        $agreement = $this->termsConditionService->findOne($agreementId);
        $this->agreementTitle = $agreement ? $agreement->title : 'Agreement';
        $this->isOpen = true;
    }

    public function close()
    {
        $this->isOpen = false;
        $this->agreementId = null;
        $this->agreementTitle = '';
    }

    public function delete()
    {
        try {
            $this->termsConditionService->delete($this->agreementId);
            session()->flash('success', 'Agreement deleted successfully!');
            $this->close();
            $this->dispatch('agreement-deleted');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to delete agreement: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.admin.agreement-delete-modal');
    }
}