<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use App\Models\AcsCooperative;
use App\Models\AcsCooperativeBranch;
use App\Models\User;
use App\Models\AcsRole;

#[Layout('layouts.admin')]
#[Title('Organization Hierarchy')]
class OrganizationHierarchy extends Component
{
    public $search = '';
    public $expandedCooperatives = [];
    public $expandedBranches = [];
    public $selectedCooperativeId = '';
    public $selectedBranchId = '';
    public $statusFilter = '';
    public $showFilters = false;

    // Agent management properties
    public $showingAllAgents = []; // Array of branch IDs showing all agents
    public $agentSearchVisible = []; // Array of branch IDs with search visible
    public $agentSearch = []; // Array of branch-specific searches
    public $agentStatusFilter = []; // Array of branch-specific status filters
    public $agentPages = []; // Array of current page per branch
    public $agentsPerPage = 10;

    protected $queryString = [
        'search' => ['except' => ''],
        'selectedCooperativeId' => ['except' => ''],
        'selectedBranchId' => ['except' => ''],
        'statusFilter' => ['except' => '']
    ];

    public function mount()
    {
        // Initialize with first cooperative expanded if no search
        if (empty($this->search) && empty($this->selectedCooperativeId)) {
            $firstCoop = AcsCooperative::first();
            if ($firstCoop) {
                $this->expandedCooperatives[] = $firstCoop->id;
            }
        }
    }

    public function toggleCooperative($cooperativeId)
    {
        if (in_array($cooperativeId, $this->expandedCooperatives)) {
            $this->expandedCooperatives = array_diff($this->expandedCooperatives, [$cooperativeId]);
            // Also collapse all branches under this cooperative
            $branchIds = AcsCooperativeBranch::where('acs_coorperative_id', $cooperativeId)->pluck('id')->toArray();
            $this->expandedBranches = array_diff($this->expandedBranches, $branchIds);
        } else {
            $this->expandedCooperatives[] = $cooperativeId;
        }
    }

    public function toggleBranch($branchId)
    {
        if (in_array($branchId, $this->expandedBranches)) {
            $this->expandedBranches = array_diff($this->expandedBranches, [$branchId]);
        } else {
            $this->expandedBranches[] = $branchId;
        }
    }

    public function expandAll()
    {
        $cooperatives = $this->getCooperatives();
        $this->expandedCooperatives = $cooperatives->pluck('id')->toArray();

        $branches = AcsCooperativeBranch::whereIn('acs_coorperative_id', $this->expandedCooperatives)->get();
        $this->expandedBranches = $branches->pluck('id')->toArray();
    }

    public function collapseAll()
    {
        $this->expandedCooperatives = [];
        $this->expandedBranches = [];
    }

    // Agent Management Methods
    public function showAllAgents($branchId)
    {
        if (!in_array($branchId, $this->showingAllAgents)) {
            $this->showingAllAgents[] = $branchId;
        }
        $this->agentPages[$branchId] = 1; // Reset to first page
    }

    public function showLimitedAgents($branchId)
    {
        $this->showingAllAgents = array_diff($this->showingAllAgents, [$branchId]);
        unset($this->agentPages[$branchId]);
    }

    public function isShowingAllAgents($branchId)
    {
        return in_array($branchId, $this->showingAllAgents);
    }

    public function toggleAgentSearch($branchId)
    {
        if (in_array($branchId, $this->agentSearchVisible)) {
            $this->agentSearchVisible = array_diff($this->agentSearchVisible, [$branchId]);
            // Clear search when hiding
            unset($this->agentSearch[$branchId]);
            unset($this->agentStatusFilter[$branchId]);
        } else {
            $this->agentSearchVisible[] = $branchId;
        }
    }

    public function isAgentSearchVisible($branchId)
    {
        return in_array($branchId, $this->agentSearchVisible);
    }

    public function nextAgentPage($branchId)
    {
        $currentPage = $this->agentPages[$branchId] ?? 1;
        $this->agentPages[$branchId] = $currentPage + 1;
    }

    public function previousAgentPage($branchId)
    {
        $currentPage = $this->agentPages[$branchId] ?? 1;
        if ($currentPage > 1) {
            $this->agentPages[$branchId] = $currentPage - 1;
        }
    }

    public function viewAgent($agentId)
    {
        // TODO: Implement agent view functionality
        session()->flash('message', 'View agent functionality to be implemented');
    }

    public function editAgent($agentId)
    {
        // TODO: Implement agent edit functionality
        session()->flash('message', 'Edit agent functionality to be implemented');
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->selectedCooperativeId = '';
        $this->statusFilter = '';
        $this->agentSearch = [];
        $this->agentStatusFilter = [];
    }

    public function getCooperatives()
    {
        $query = AcsCooperative::with(['branches.users.role', 'branches.users.userDetails'])
            ->withCount([
                'branches',
                'users' => function($q) {
                    $q->whereHas('role', function($roleQuery) {
                        $roleQuery->where('name', 'main_agent');
                    });
                }
            ]);

        if (!empty($this->search)) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhereHas('branches', function($branchQuery) {
                      $branchQuery->where('name', 'like', '%' . $this->search . '%');
                  })
                  ->orWhereHas('users', function($userQuery) {
                      $userQuery->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%');
                  });
            });
        }

        if (!empty($this->selectedCooperativeId)) {
            $query->where('id', $this->selectedCooperativeId);
        }

        return $query->orderBy('name')->get();
    }

    public function getBranchesForCooperative($cooperativeId)
    {
        $query = AcsCooperativeBranch::where('acs_coorperative_id', $cooperativeId)
            ->with(['users.role', 'users.userDetails'])
            ->withCount(['users' => function($q) {
                $q->whereHas('role', function($roleQuery) {
                    $roleQuery->where('name', 'agent');
                });
            }]);

        if (!empty($this->search)) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhereHas('users', function($userQuery) {
                      $userQuery->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%');
                  });
            });
        }

        if (!empty($this->selectedBranchId)) {
            $query->where('id', $this->selectedBranchId);
        }

        return $query->orderBy('name')->get();
    }

        public function getAgentsForBranch($branchId)
    {
        $isShowingAll = $this->isShowingAllAgents($branchId);
        $currentPage = $this->agentPages[$branchId] ?? 1;

        // Base query for regular agents only (no main agents) in this branch
        $baseQuery = User::where('acs_coorperative_branch_id', $branchId)
            ->with(['role', 'userDetails', 'cooperative', 'cooperativeBranch'])
            ->whereHas('role', function($q) {
                $q->where('name', 'agent');
            });

        // Apply global search
        if (!empty($this->search)) {
            $baseQuery->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%')
                  ->orWhere('username', 'like', '%' . $this->search . '%');
            });
        }

        // Apply branch-specific search
        if (!empty($this->agentSearch[$branchId])) {
            $search = $this->agentSearch[$branchId];
            $baseQuery->where(function($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('email', 'like', '%' . $search . '%')
                  ->orWhere('username', 'like', '%' . $search . '%');
            });
        }

        // Apply global status filter
        if (!empty($this->statusFilter)) {
            $baseQuery->where('status', $this->statusFilter);
        }

        // Apply branch-specific status filter
        if (!empty($this->agentStatusFilter[$branchId])) {
            $baseQuery->where('status', $this->agentStatusFilter[$branchId]);
        }

        $totalAgentsCount = $baseQuery->count();

        // Handle pagination for regular agents if showing all
        if ($isShowingAll && $totalAgentsCount > $this->agentsPerPage) {
            $offset = ($currentPage - 1) * $this->agentsPerPage;
            $agents = $baseQuery->orderBy('name')
                          ->offset($offset)
                          ->limit($this->agentsPerPage)
                          ->get();

            $lastPage = ceil($totalAgentsCount / $this->agentsPerPage);

            $pagination = [
                'current_page' => $currentPage,
                'last_page' => $lastPage,
                'per_page' => $this->agentsPerPage,
                'from' => $offset + 1,
                'to' => min($offset + $this->agentsPerPage, $totalAgentsCount)
            ];
        } else {
            // Show limited number (first 5) or all without pagination
            $limit = $isShowingAll ? $totalAgentsCount : 5;
            $agents = $baseQuery->orderBy('name')->limit($limit)->get();
            $pagination = null;
        }

        return [
            'agents' => $agents,
            'agents_count' => $totalAgentsCount,
            'pagination' => $pagination
        ];
    }

    public function getMainAgentsForCooperative($cooperativeId)
    {
        // Base query for main agents in this cooperative
        $baseQuery = User::where('acs_coorperative_id', $cooperativeId)
            ->with(['role', 'userDetails', 'cooperative', 'cooperativeBranch'])
            ->whereHas('role', function($q) {
                $q->where('name', 'main-agent');
            });

        // Apply global search
        if (!empty($this->search)) {
            $baseQuery->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%')
                  ->orWhere('username', 'like', '%' . $this->search . '%');
            });
        }

        // Apply global status filter
        if (!empty($this->statusFilter)) {
            $baseQuery->where('status', $this->statusFilter);
        }

        return $baseQuery->orderBy('name')->get();
    }

    public function getStatusBadgeClass($status)
    {
        return match($status) {
            User::STATUS_ACTIVE => 'bg-success',
            User::STATUS_PENDING => 'bg-warning',
            User::STATUS_SUSPENDED => 'bg-danger',
            User::STATUS_INACTIVE => 'bg-secondary',
            User::STATUS_INVITATION_SENT => 'bg-info',
            default => 'bg-secondary'
        };
    }

    public function updatingSearch()
    {
        // Auto-expand cooperatives when searching
        if (!empty($this->search)) {
            $cooperatives = $this->getCooperatives();
            $this->expandedCooperatives = $cooperatives->pluck('id')->toArray();
        }
    }

    // Reset pagination when agent search changes
    public function updatedAgentSearch()
    {
        foreach ($this->agentSearch as $branchId => $search) {
            $this->agentPages[$branchId] = 1; // Reset to first page
        }
    }

    public function updatedAgentStatusFilter()
    {
        foreach ($this->agentStatusFilter as $branchId => $filter) {
            $this->agentPages[$branchId] = 1; // Reset to first page
        }
    }

    public function render()
    {
        $cooperatives = $this->getCooperatives();
        $totalCooperatives = AcsCooperative::count();
        $totalBranches = AcsCooperativeBranch::count();
        $totalMainAgents = User::whereHas('role', function($q) {
            $q->where('name', 'main_agent');
        })->count();

        $totalRegularAgents = User::whereHas('role', function($q) {
            $q->where('name', 'agent');
        })->count();

        $totalAgents = $totalMainAgents + $totalRegularAgents;

        return view('livewire.admin.organization-hierarchy', [
            'cooperatives' => $cooperatives,
            'totalCooperatives' => $totalCooperatives,
            'totalBranches' => $totalBranches,
            'totalAgents' => $totalAgents,
            'totalMainAgents' => $totalMainAgents,
            'totalRegularAgents' => $totalRegularAgents,
            'userStatuses' => [
                '' => 'All Status',
                User::STATUS_ACTIVE => 'Active',
                User::STATUS_PENDING => 'Pending',
                User::STATUS_SUSPENDED => 'Suspended',
                User::STATUS_INACTIVE => 'Inactive',
                User::STATUS_INVITATION_SENT => 'Invited'
            ]
        ]);
    }
}
