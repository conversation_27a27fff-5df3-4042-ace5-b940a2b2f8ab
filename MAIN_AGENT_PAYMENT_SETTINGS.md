# Main Agent Payment Settings

## Overview

The Main Agent Payment Settings feature allows main agents to configure how payments are processed for their cooperative and branches. This system provides flexibility in payment processing while maintaining proper oversight and control.

## Features

### 1. **Payment Method Configuration**
- **Main Cooperative**: All payments processed through the main cooperative's central account
- **Individual Branch**: Each branch processes payments through their own individual accounts

### 2. **Branch-Specific Configuration**
- When using Individual Branch payment method, configure each branch separately
- Set custom account information per branch
- Add branch-specific notes and instructions

### 3. **Branch-Specific Settings**
- Configure payment methods per branch
- Add custom account information
- Include branch-specific notes and instructions

## Database Structure

### Main Table: `acs_main_agent_payment_settings`

| Column | Type | Description |
|--------|------|-------------|
| `id` | Primary Key | Unique identifier |
| `main_agent_id` | ULID | Reference to the main agent (acs_users.id) |
| `payment_method` | ENUM | 'individual_branch' or 'main_cooperative' |
| `allow_branch_override` | BOOLEAN | Legacy field - always false (feature removed) |
| `branch_specific_settings` | JSON | Branch-specific payment configurations |
| `notes` | TEXT | General notes about payment configuration |
| `is_active` | BOOLEAN | Whether this setting is currently active |
| `created_by` | ULID | User who created this setting |
| `updated_by` | ULID | User who last updated this setting |
| `created_at` | TIMESTAMP | Creation timestamp |
| `updated_at` | TIMESTAMP | Last update timestamp |
| `deleted_at` | TIMESTAMP | Soft deletion timestamp |

## Implementation Details

### Model: `AcsMainAgentPaymentSetting`

**Location**: `app/Models/AcsMainAgentPaymentSetting.php`

**Key Methods**:
- `getActiveSettingForAgent($mainAgentId)` - Get current active setting for a main agent
- `updateOrCreateForAgent($mainAgentId, $data)` - Create or update settings for a main agent
- `getBranchSetting($branchId)` - Get specific branch setting
- `setBranchSetting($branchId, $settings)` - Update specific branch setting

**Constants**:
- `PAYMENT_METHOD_INDIVIDUAL_BRANCH` = 'individual_branch'
- `PAYMENT_METHOD_MAIN_COOPERATIVE` = 'main_cooperative'

### Livewire Component: `PaymentSettings`

**Location**: `app/Livewire/MasterAgent/PaymentSettings.php`

**Key Features**:
- Real-time form updates using Livewire's reactive properties
- Branch settings table shown only when Individual Branch method is selected
- Validation and error handling
- Integration with existing cooperative and branch data

### Livewire Component: `BranchList` (Enhanced)

**Location**: `app/Livewire/MasterAgent/BranchList.php`

**Enhanced Features**:
- Automatic detection of current payment method
- Dynamic UI controls based on payment method
- Bulk enable/disable controls for Main Cooperative method
- Individual branch controls for Individual Branch method
- Integration with payment settings validation
- Real-time payment method status display

### View Template

**Location**: `resources/views/livewire/master-agent/payment-settings.blade.php`

**UI Features**:
- Modern, responsive design using Bootstrap
- Card-based layout with clear sections
- Interactive forms with real-time updates
- Information sidebar with guidance
- Current configuration display

## Usage Flow

### 1. **Accessing Payment Settings**
- Main agents can access payment settings from their dashboard sidebar
- Navigate to "Settings" > "Payment Settings"
- Requires 'settings' permission with 'view' access

### 2. **Configuring Payment Method**
- Select between "Main Cooperative" or "Individual Branch"
- Main Cooperative: Centralized payment processing
- Individual Branch: Decentralized payment processing per branch

### 3. **Branch-Specific Configuration**
- When using "Individual Branch", configure each branch's payment settings
- Set custom account information for each branch
- Add branch-specific notes and instructions

### 4. **Branch Management Integration**
- Branch List page automatically reflects current payment method
- **Main Cooperative Mode**: Bulk enable/disable controls for all branches
- **Individual Branch Mode**: Individual branch controls available
- Payment method status displayed prominently on Branch List page

### 5. **Branch Registration Controls**
- **Main Cooperative**: Individual branch toggles are disabled, use bulk controls
- **Individual Branch**: Each branch can be individually enabled/disabled
- Registration URLs reflect payment method in help text

### 6. **Saving Settings**
- All settings are saved atomically using database transactions
- Only one active setting per main agent is maintained
- Previous settings are deactivated automatically
- Branch List page automatically updates based on payment method changes

## Security Features

### 1. **Permission-Based Access**
- Requires 'settings' permission with 'view' access
- Only main agents can access their own payment settings
- Sub-main-agents require explicit permission grants

### 2. **Data Integrity**
- Database constraints ensure data consistency
- Transaction-based updates prevent race conditions
- Soft deletion maintains audit trail

### 3. **Validation**
- Form validation on both frontend and backend
- Payment method enum validation
- Maximum character limits on text fields

## API Integration

### Route Configuration

**File**: `routes/main-agent.php`

```php
Route::get('payment-settings', \App\Livewire\MasterAgent\PaymentSettings::class)
    ->name('payment-settings')
    ->middleware('sub.main.agent.permission:settings,view');
```

### Navigation Integration

**File**: `resources/views/layouts/account/sidebar/master-agent.blade.php`

- Added to Settings section in sidebar
- Uses credit card icon for visual identification
- Includes proper route naming and active state detection

## Migration Details

**File**: `database/migrations/2025_01_17_100000_create_acs_main_agent_payment_settings_table.php`

**Key Features**:
- Foreign key constraints to maintain referential integrity
- Proper indexing for performance optimization
- Soft deletion support
- JSON column for flexible branch settings storage

## Branch Settings JSON Structure

```json
{
  "branch_id": 123,
  "branch_name": "Downtown Branch",
  "payment_method": "individual_branch",
  "custom_account": "Account details or reference",
  "notes": "Special instructions for this branch"
}
```

## Future Enhancements

### Potential Improvements
1. **Payment Processing Integration**: Direct integration with payment gateways
2. **Reporting Dashboard**: Analytics on payment method usage
3. **Approval Workflow**: Admin approval for payment method changes
4. **Audit Logging**: Detailed change history tracking
5. **Bulk Operations**: Mass update capabilities for multiple branches

### Scalability Considerations
1. **Caching**: Cache frequently accessed payment settings
2. **Performance**: Optimize JSON queries for large branch counts
3. **Backup**: Regular backup of payment configuration data

## Testing

### Manual Testing Checklist
- [ ] Create new payment settings
- [ ] Update existing payment settings
- [ ] Toggle branch override settings
- [ ] Configure individual branch settings
- [ ] Verify permission-based access
- [ ] Test form validation
- [ ] Confirm data persistence
- [ ] Check sidebar navigation

### Edge Cases
- [ ] Main agent with no cooperative assigned
- [ ] Cooperative with no active branches
- [ ] Concurrent updates by multiple users
- [ ] Large number of branches (performance testing)

## Support and Maintenance

### Common Issues
1. **Permission Denied**: Ensure user has 'settings' permission
2. **Branch Not Loading**: Verify cooperative assignment and branch status
3. **Settings Not Saving**: Check database constraints and validation rules

### Maintenance Tasks
1. **Regular Cleanup**: Remove old inactive settings
2. **Performance Monitoring**: Monitor query performance for large datasets
3. **Security Review**: Regular review of permission requirements

## Related Documentation
- [Sub Main Agent System](SUB_MAIN_AGENT_SYSTEM.md)
- [Permission Management Guide](PERMISSION_MANAGEMENT_GUIDE.md)
- [Enhanced Permission System](ENHANCED_PERMISSION_SYSTEM.md) 
