<?php

use Illuminate\Support\Facades\Route;

// Dashboard
Route::get('dashboard', \App\Livewire\MasterAgent\Dashboard::class)
    ->name('dashboard')
    ->middleware('sub.main.agent.permission:dashboard,view');

// Terms and conditions acceptance (no permission check needed)
Route::get('accept-terms', \App\Livewire\MasterAgent\AcceptTerms::class)->name('accept-terms');

// Profile management
Route::get('profile', \App\Livewire\MasterAgent\Profile::class)
    ->name('profile.form')
    ->middleware('sub.main.agent.permission:profile,view');

// Branch List
Route::get('branch-list', \App\Livewire\MasterAgent\BranchList::class)
    ->name('branch-list')
    ->middleware('sub.main.agent.permission:branch,view');

// Agent management
Route::get('agent', \App\Livewire\MasterAgent\RegisterAgent::class)
    ->name('index')
    ->middleware('sub.main.agent.permission:agents,view');

// Sub-Main-Agent management (only for main-agent role)
Route::get('sub-main-agent-management', \App\Livewire\MasterAgent\RegisterSubMainAgent::class)->name('sub-main-agent-management');
Route::get('sub-main-agents', \App\Livewire\MasterAgent\RegisterSubMainAgent::class)->name('sub-main-agents');
Route::get('register-sub-main-agent', \App\Livewire\MasterAgent\RegisterSubMainAgentForm::class)->name('register-sub-main-agent');
Route::get('manage-permissions/{userId}', \App\Livewire\MasterAgent\ManageSubAgentPermissions::class)->name('manage-permissions');

// Agreement List
Route::get('aggrement-list', \App\Livewire\MasterAgent\AgreementList::class)
    ->name('aggrement-list')
    ->middleware('sub.main.agent.permission:agreements,view');

// Bank Account
Route::get('bank-account', \App\Livewire\MasterAgent\BankAccount::class)
    ->middleware('sub.main.agent.permission:bank_account,view');

// Payment Settings
Route::get('payment-settings', \App\Livewire\MasterAgent\PaymentSettings::class)
    ->name('payment-settings')
    ->middleware('sub.main.agent.permission:settings,view');

// Account Management
Route::get('change-password', \App\Livewire\MasterAgent\ChangePassword::class)
    ->name('change-password')
    ->middleware('sub.main.agent.permission:password_management,view');
