<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Livewire\Admin\WorkingPositionManagement;

// Admin management
Route::get('list-admin', \App\Livewire\Admin\ListAdmins::class)->name('list-admin');

// Agent management
// Route::get('agent', \App\Livewire\Admin\ListAgents::class)->name('agent.index');

// User registration
// Route::get('register-admin', \App\Livewire\Auth\RegisterAdmin::class)->name('register-admin');

// Dashboard
Route::get('dashboard', \App\Livewire\Admin\Dashboard::class)->name('dashboard');

// Session management
Route::get('sessions', \App\Livewire\Admin\ActiveSessions::class)->name('sessions.index');

// Notification management
Route::prefix('notifications')->name('notifications.')->group(function () {
    Route::post('mark-as-read/{id}', function ($id) {
        $notificationService = new \App\Services\AcsNotificationService();
        $success = $notificationService->markAsRead($id);
        return response()->json(['success' => $success]);
    })->name('mark-as-read');

    Route::post('mark-all-as-read', function () {
        $notificationService = new \App\Services\AcsNotificationService();
        $success = $notificationService->markAllAsReadForUser(Auth::user()->id);
        return response()->json(['success' => $success]);
    })->name('mark-all-as-read');
});

// Test notification component
Route::get('test-notifications', \App\Livewire\Admin\HeaderNotifications::class)->name('test-notifications');

// Master agent management
Route::get('master-agent', \App\Livewire\Admin\RegisterMainAgent::class)->name('index');


// Agent management
Route::get('agent-list', \App\Livewire\Admin\RegisterAgent::class)->name('agent.index');

// KYC Document Downloads
Route::prefix('kyc-documents')->name('kyc-documents.')->group(function () {
    Route::get('bank-statement/{agentId}', [\App\Http\Controllers\Admin\KycDocumentController::class, 'downloadBankStatement'])->name('bank-statement');
    Route::get('ic-front/{agentId}', [\App\Http\Controllers\Admin\KycDocumentController::class, 'downloadIcFront'])->name('ic-front');
    Route::get('ic-back/{agentId}', [\App\Http\Controllers\Admin\KycDocumentController::class, 'downloadIcBack'])->name('ic-back');
});

// Cooperative and Branch management
Route::get('cooperative', \App\Livewire\Admin\ListCooperatives::class)->name('cooperatives');
Route::get('cooperative/{cooperative}/branches', \App\Livewire\Admin\CooperativeBranches::class)->name('cooperatives.branches');
Route::get('branch', \App\Livewire\Admin\ListBranches::class)->name('branch.index');

Route::get('agreement-list', \App\Livewire\Admin\AgreementManagement::class)->name('agreement-list');
Route::get('specific-main-agent-agreement/{agreement_id?}', \App\Livewire\Admin\SpecificMainAgentAgreement::class)->name('specific-main-agent-agreement');

// Announcement management
Route::get('announcements', \App\Livewire\Admin\AnnouncementManagement::class)->name('announcements.index');

// Organization hierarchy
Route::get('organization-hierarchy', \App\Livewire\Admin\OrganizationHierarchy::class)->name('organization-hierarchy');

// Route::get('cooperative', [CoorperativeController::class, 'index'])->name('cooperative.index');

// Route::get('branch', [BranchController::class, 'index'])->name('branch.index');

Route::get('sales-commissions/monthly', function () {
    return view('admin.sales-commission.monthly');
});

// Commission Management Routes (Livewire)
Route::prefix('commission')->name('commission.')->group(function () {
    Route::get('/', \App\Livewire\Admin\CommissionManagement::class)->name('index');
    // Approval workflow (Livewire)
    Route::get('pending-approvals', \App\Livewire\Admin\PendingApprovals::class)->name('pending-approvals');
    // Commission distribution reports
    Route::get('distribution-reports', \App\Livewire\Admin\CommissionDistributionReports::class)->name('distribution-reports');
});

// Separate route for pending approvals (for consistent naming)
Route::get('pending-approvals', \App\Livewire\Admin\PendingApprovals::class)->name('pending-approvals');

Route::get('sales-commissions/campaign', function () {
    return view('admin.sales-commission.campaign');
});


Route::get('commission-report', function () {
    return view('admin.commission-report.index');
});

Route::get('commission-report/{uuid}', function () {
    return view('admin.commission-report.view');
});


Route::get('gold-price', function () {
    return view('admin.gold-price');
});

Route::get('profit-simulation', function () {
    return view('admin.profit-simulation');
});

Route::get('agreement-workflow', function () {
    return view('admin.agreement-workflow.index');
});

Route::get('agreement-workflow/{uuid}', function () {
    return view('admin.agreement-workflow.view');
});
// Other views converted to Livewire components
Route::get('sales-commissions/monthly', \App\Livewire\Admin\SalesCommissionMonthly::class);
Route::get('sales-commissions/campaign', \App\Livewire\Admin\SalesCommissionCampaign::class);
Route::get('commission-report', \App\Livewire\Admin\CommissionReportIndex::class);
Route::get('commission-report/{uuid}', \App\Livewire\Admin\CommissionReportView::class);
Route::get('campaign-management', \App\Livewire\Admin\CampaignManagement::class)->name('campaign-management');
Route::get('rms-gold-commerce-integration', \App\Livewire\Admin\RmsGoldCommerceIntegration::class)->name('rms-gold-commerce-integration');
Route::get('email-templates', \App\Livewire\Admin\EmailTemplateManagement::class)->name('email-templates');
Route::get('gold-price', \App\Livewire\Admin\GoldPrice::class);
Route::get('profit-simulation', \App\Livewire\Admin\ProfitSimulation::class);
Route::get('agreement-workflow', \App\Livewire\Admin\AgreementWorkflowIndex::class);
Route::get('agreement-workflow/{uuid}', \App\Livewire\Admin\AgreementWorkflowView::class);
Route::get('referral-code-management', \App\Livewire\Admin\ReferralCodeManagement::class)->name('referral-code-management');
Route::get('working-positions', WorkingPositionManagement::class)->name('admin.working-positions');
Route::get('income-range-management', \App\Livewire\Admin\RangeIncomeManagement::class)->name('income-range-management');

// Account Management
Route::get('change-password', \App\Livewire\Admin\ChangePassword::class)->name('change-password');
